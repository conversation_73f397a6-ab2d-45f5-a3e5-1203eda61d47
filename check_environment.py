#!/usr/bin/env python3
"""
本地Spark环境检查脚本
"""
import sys
import os
import subprocess
import importlib

def check_python_version():
    """检查Python版本"""
    print("=== Python环境检查 ===")
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        print("✅ Python版本符合要求 (>=3.8)")
        return True
    else:
        print("❌ Python版本过低，需要3.8或更高版本")
        return False

def check_java():
    """检查Java环境"""
    print("\n=== Java环境检查 ===")
    try:
        result = subprocess.run(['java', '-version'], 
                              capture_output=True, text=True, stderr=subprocess.STDOUT)
        if result.returncode == 0:
            java_version = result.stderr.split('\n')[0]
            print(f"✅ Java已安装: {java_version}")
            return True
        else:
            print("❌ Java未安装或配置错误")
            return False
    except FileNotFoundError:
        print("❌ Java未找到，请安装Java 8或更高版本")
        return False

def check_spark():
    """检查Spark环境"""
    print("\n=== Spark环境检查 ===")
    
    # 检查SPARK_HOME
    spark_home = os.environ.get('SPARK_HOME')
    if spark_home:
        print(f"✅ SPARK_HOME: {spark_home}")
    else:
        print("⚠️ SPARK_HOME未设置")
    
    # 检查pyspark
    try:
        import pyspark
        print(f"✅ PySpark已安装: {pyspark.__version__}")
        
        # 尝试创建SparkContext
        from pyspark.sql import SparkSession
        spark = SparkSession.builder \
            .appName("EnvironmentTest") \
            .master("local[2]") \
            .config("spark.driver.memory", "2g") \
            .getOrCreate()
        
        # 简单测试
        df = spark.createDataFrame([(1, "test")], ["id", "text"])
        count = df.count()
        spark.stop()
        
        print(f"✅ Spark测试成功，数据行数: {count}")
        return True
        
    except Exception as e:
        print(f"❌ Spark测试失败: {e}")
        return False

def check_required_packages():
    """检查必需的Python包"""
    print("\n=== Python包检查 ===")
    
    required_packages = {
        'torch': 'PyTorch',
        'transformers': 'Hugging Face Transformers',
        'datasets': 'Hugging Face Datasets',
        'pandas': 'Pandas',
        'numpy': 'NumPy',
        'scikit-learn': 'Scikit-learn',
        'tqdm': 'TQDM',
        'matplotlib': 'Matplotlib',
        'seaborn': 'Seaborn'
    }
    
    missing_packages = []
    
    for package, name in required_packages.items():
        try:
            module = importlib.import_module(package)
            version = getattr(module, '__version__', 'Unknown')
            print(f"✅ {name}: {version}")
        except ImportError:
            print(f"❌ {name}: 未安装")
            missing_packages.append(package)
    
    return missing_packages

def check_gpu():
    """检查GPU可用性"""
    print("\n=== GPU环境检查 ===")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"✅ GPU可用: {gpu_count}个GPU")
            print(f"   主GPU: {gpu_name}")
            print(f"   CUDA版本: {torch.version.cuda}")
            return True
        else:
            print("⚠️ GPU不可用，将使用CPU训练")
            return False
    except ImportError:
        print("❌ PyTorch未安装，无法检查GPU")
        return False

def check_disk_space():
    """检查磁盘空间"""
    print("\n=== 磁盘空间检查 ===")
    
    import shutil
    total, used, free = shutil.disk_usage(".")
    
    total_gb = total // (1024**3)
    used_gb = used // (1024**3)
    free_gb = free // (1024**3)
    
    print(f"总空间: {total_gb}GB")
    print(f"已使用: {used_gb}GB")
    print(f"可用空间: {free_gb}GB")
    
    if free_gb >= 5:
        print("✅ 磁盘空间充足")
        return True
    else:
        print("⚠️ 磁盘空间不足，建议至少5GB可用空间")
        return False

def generate_install_commands(missing_packages):
    """生成安装命令"""
    if not missing_packages:
        return
    
    print("\n=== 安装建议 ===")
    print("缺失的包可以通过以下命令安装：")
    print(f"pip install {' '.join(missing_packages)}")
    
    print("\n完整的依赖安装命令：")
    print("pip install torch transformers datasets pandas numpy scikit-learn tqdm matplotlib seaborn pyspark")

def main():
    """主函数"""
    print("🔍 开始检查本地Spark GLUE环境...")
    print("=" * 50)
    
    checks = []
    
    # 执行各项检查
    checks.append(("Python", check_python_version()))
    checks.append(("Java", check_java()))
    checks.append(("Spark", check_spark()))
    
    missing_packages = check_required_packages()
    checks.append(("Python包", len(missing_packages) == 0))
    
    checks.append(("GPU", check_gpu()))
    checks.append(("磁盘空间", check_disk_space()))
    
    # 生成安装建议
    generate_install_commands(missing_packages)
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 环境检查总结:")
    
    all_passed = True
    for name, passed in checks:
        status = "✅" if passed else "❌"
        print(f"{status} {name}")
        if not passed and name in ["Python", "Java", "Spark"]:
            all_passed = False
    
    if all_passed:
        print("\n🎉 环境检查通过！可以开始测试Spark GLUE项目")
    else:
        print("\n⚠️ 部分环境检查未通过，请先解决相关问题")
    
    return all_passed

if __name__ == "__main__":
    main()
