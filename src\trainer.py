"""
训练器模块 - 基于Spark的分布式训练
"""
import os
import logging
import time
import json
from typing import Dict, List, Tuple, Optional, Any
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
import numpy as np
from tqdm import tqdm
from pyspark.sql import SparkSession, DataFrame
from sklearn.metrics import accuracy_score, f1_score, matthews_corrcoef
from scipy.stats import pearsonr, spearmanr

from model_manager import GLUEModelManager, ModelEvaluator
from utils import ProgressTracker, format_time
import config

logger = logging.getLogger(__name__)

class GLUEDataset(Dataset):
    """GLUE数据集类"""
    
    def __init__(self, texts: List[str], labels: List[int] = None, 
                 tokenizer=None, max_length: int = 128):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = self.texts[idx]
        
        # 处理句子对
        if isinstance(text, (list, tuple)) and len(text) == 2:
            encoding = self.tokenizer(
                text[0], text[1],
                truncation=True,
                padding='max_length',
                max_length=self.max_length,
                return_tensors='pt'
            )
        else:
            encoding = self.tokenizer(
                text,
                truncation=True,
                padding='max_length',
                max_length=self.max_length,
                return_tensors='pt'
            )
        
        item = {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'token_type_ids': encoding['token_type_ids'].flatten()
        }
        
        if self.labels is not None:
            # 确保标签是数值类型
            label = self.labels[idx]
            if isinstance(label, str):
                try:
                    if '.' in str(label):  # 浮点数
                        label = float(label)
                    else:
                        label = int(label)
                except ValueError:
                    label = 0  # 默认标签

            # 根据标签类型选择tensor类型
            if isinstance(label, float):
                item['labels'] = torch.tensor(label, dtype=torch.float)
            else:
                item['labels'] = torch.tensor(label, dtype=torch.long)
        
        return item

class GLUETrainer:
    """GLUE任务训练器"""
    
    def __init__(self, model_manager: GLUEModelManager, task: str, 
                 output_dir: str = None, device: str = None):
        self.model_manager = model_manager
        self.task = task
        self.output_dir = output_dir or os.path.join(config.MODEL_SAVE_DIR, task)
        self.device = device or config.DEVICE
        
        # 训练状态
        self.current_epoch = 0
        self.global_step = 0
        self.best_metric = 0.0
        self.training_history = []
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        logger.info(f"训练器初始化完成: 任务={task}, 输出目录={self.output_dir}")
    
    def prepare_data(self, train_df: DataFrame, eval_df: DataFrame) -> Tuple[DataLoader, DataLoader]:
        """准备训练和验证数据"""
        logger.info("准备训练数据...")
        
        # 转换Spark DataFrame到Python列表
        train_data = self._spark_df_to_lists(train_df)
        eval_data = self._spark_df_to_lists(eval_df)
        
        # 创建数据集
        train_dataset = GLUEDataset(
            texts=train_data['texts'],
            labels=train_data['labels'],
            tokenizer=self.model_manager.tokenizer,
            max_length=config.MAX_SEQ_LENGTH
        )
        
        eval_dataset = GLUEDataset(
            texts=eval_data['texts'],
            labels=eval_data['labels'],
            tokenizer=self.model_manager.tokenizer,
            max_length=config.MAX_SEQ_LENGTH
        )
        
        # 创建数据加载器
        train_loader = DataLoader(
            train_dataset,
            batch_size=config.BATCH_SIZE,
            shuffle=True,
            num_workers=0  # Windows兼容性
        )
        
        eval_loader = DataLoader(
            eval_dataset,
            batch_size=config.BATCH_SIZE,
            shuffle=False,
            num_workers=0
        )
        
        logger.info(f"数据准备完成: 训练集={len(train_dataset)}, 验证集={len(eval_dataset)}")
        return train_loader, eval_loader
    
    def _spark_df_to_lists(self, df: DataFrame) -> Dict[str, List]:
        """将Spark DataFrame转换为Python列表"""
        # 收集数据到driver
        rows = df.collect()
        
        texts = []
        labels = []
        
        for row in rows:
            # 根据任务类型处理文本
            if self.task in ["MRPC", "STS-B", "QQP", "MNLI", "QNLI", "RTE", "WNLI"]:
                # 句子对任务
                if hasattr(row, 'sentence1') and hasattr(row, 'sentence2'):
                    texts.append((row.sentence1, row.sentence2))
                else:
                    # 备用列名
                    text_cols = [col for col in row.asDict().keys() if 'sentence' in col.lower() or 'text' in col.lower()]
                    if len(text_cols) >= 2:
                        texts.append((row[text_cols[0]], row[text_cols[1]]))
                    else:
                        logger.warning(f"无法找到句子对列: {row.asDict().keys()}")
                        continue
            else:
                # 单句子任务
                if hasattr(row, 'sentence'):
                    texts.append(row.sentence)
                else:
                    # 备用列名
                    text_cols = [col for col in row.asDict().keys() if 'sentence' in col.lower() or 'text' in col.lower()]
                    if text_cols:
                        texts.append(row[text_cols[0]])
                    else:
                        logger.warning(f"无法找到文本列: {row.asDict().keys()}")
                        continue
            
            # 处理标签
            if hasattr(row, 'label') and row.label is not None:
                label = row.label
                # 确保标签是数值类型
                if isinstance(label, str):
                    try:
                        if '.' in str(label):  # 浮点数
                            label = float(label)
                        else:
                            label = int(label)
                    except ValueError:
                        label = 0
                labels.append(label)
            else:
                # 对于回归任务，使用0.0作为默认值
                if config.TASK_TYPES.get(self.task) == "regression":
                    labels.append(0.0)
                else:
                    labels.append(0)  # 默认标签
        
        return {'texts': texts, 'labels': labels}
    
    def train(self, train_loader: DataLoader, eval_loader: DataLoader, 
              epochs: int = None, learning_rate: float = None) -> Dict[str, Any]:
        """训练模型"""
        epochs = epochs or config.EPOCHS
        learning_rate = learning_rate or config.LEARNING_RATE
        
        logger.info(f"开始训练: 轮数={epochs}, 学习率={learning_rate}")
        
        # 创建优化器和调度器
        optimizer = self.model_manager.create_optimizer(learning_rate)
        total_steps = len(train_loader) * epochs
        scheduler = self.model_manager.create_scheduler(optimizer, total_steps)
        
        # 创建评估器
        evaluator = ModelEvaluator(self.model_manager)
        
        # 训练循环
        start_time = time.time()
        progress_tracker = ProgressTracker(total_steps)
        progress_tracker.start()
        
        for epoch in range(epochs):
            self.current_epoch = epoch
            logger.info(f"开始第 {epoch + 1}/{epochs} 轮训练")
            
            # 训练一个epoch
            train_loss = self._train_epoch(train_loader, optimizer, scheduler, progress_tracker)
            
            # 验证
            eval_metrics = self._evaluate(eval_loader, evaluator)
            
            # 记录训练历史
            epoch_info = {
                'epoch': epoch + 1,
                'train_loss': train_loss,
                'eval_metrics': eval_metrics,
                'learning_rate': scheduler.get_last_lr()[0] if scheduler else learning_rate
            }
            self.training_history.append(epoch_info)
            
            # 打印进度
            logger.info(f"Epoch {epoch + 1}/{epochs}:")
            logger.info(f"  训练损失: {train_loss:.4f}")
            for metric, value in eval_metrics.items():
                logger.info(f"  {metric}: {value:.4f}")
            
            # 保存最佳模型
            current_metric = self._get_main_metric(eval_metrics)
            if current_metric > self.best_metric:
                self.best_metric = current_metric
                self._save_checkpoint(epoch + 1, is_best=True)
                logger.info(f"保存最佳模型: {self._get_main_metric_name()}={current_metric:.4f}")
        
        total_time = time.time() - start_time
        logger.info(f"训练完成! 总时间: {format_time(total_time)}")
        
        # 保存训练历史
        self._save_training_history()
        
        return {
            'best_metric': self.best_metric,
            'total_time': total_time,
            'training_history': self.training_history
        }

    def _train_epoch(self, train_loader: DataLoader, optimizer, scheduler,
                    progress_tracker: ProgressTracker) -> float:
        """训练一个epoch"""
        self.model_manager.model.train()
        total_loss = 0.0
        num_batches = 0

        for batch in tqdm(train_loader, desc=f"Epoch {self.current_epoch + 1}"):
            # 移动数据到设备
            batch = {k: v.to(self.device) for k, v in batch.items()}

            # 前向传播
            outputs = self.model_manager.model(**batch)
            loss = outputs.loss

            # 反向传播
            optimizer.zero_grad()
            loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(
                self.model_manager.model.parameters(),
                config.MAX_GRAD_NORM
            )

            # 更新参数
            optimizer.step()
            if scheduler:
                scheduler.step()

            # 记录损失
            total_loss += loss.item()
            num_batches += 1
            self.global_step += 1

            # 更新进度
            progress_tracker.step()

            # 定期日志
            if self.global_step % config.LOGGING_STEPS == 0:
                avg_loss = total_loss / num_batches
                progress_info = progress_tracker.get_progress()
                logger.info(f"Step {self.global_step}: loss={avg_loss:.4f}, "
                          f"progress={progress_info['progress_percent']}")

        return total_loss / num_batches

    def _evaluate(self, eval_loader: DataLoader, evaluator: ModelEvaluator) -> Dict[str, float]:
        """评估模型"""
        self.model_manager.model.eval()

        all_predictions = []
        all_labels = []
        total_loss = 0.0
        num_batches = 0

        with torch.no_grad():
            for batch in tqdm(eval_loader, desc="Evaluating"):
                # 移动数据到设备
                batch = {k: v.to(self.device) for k, v in batch.items()}

                # 前向传播
                outputs = self.model_manager.model(**batch)
                loss = outputs.loss
                logits = outputs.logits

                # 记录损失
                total_loss += loss.item()
                num_batches += 1

                # 收集预测和标签
                if config.TASK_TYPES.get(self.task) == "regression":
                    predictions = logits.squeeze().cpu().numpy()
                else:
                    predictions = torch.argmax(logits, dim=-1).cpu().numpy()

                labels = batch['labels'].cpu().numpy()

                all_predictions.extend(predictions)
                all_labels.extend(labels)

        # 计算指标
        avg_loss = total_loss / num_batches
        metrics = self._compute_metrics(all_predictions, all_labels)
        metrics['eval_loss'] = avg_loss

        return metrics

    def _compute_metrics(self, predictions: List[float], labels: List[int]) -> Dict[str, float]:
        """计算评估指标"""
        if config.TASK_TYPES.get(self.task) == "regression":
            # 回归任务 (STS-B)
            pearson_corr = pearsonr(predictions, labels)[0]
            spearman_corr = spearmanr(predictions, labels)[0]
            return {
                "pearson_correlation": pearson_corr,
                "spearman_correlation": spearman_corr,
                "combined_score": (pearson_corr + spearman_corr) / 2
            }
        else:
            # 分类任务
            accuracy = accuracy_score(labels, predictions)

            if self.task == "CoLA":
                mcc = matthews_corrcoef(labels, predictions)
                return {"accuracy": accuracy, "matthews_corrcoef": mcc}
            elif len(np.unique(labels)) > 2:
                f1 = f1_score(labels, predictions, average="macro")
                return {"accuracy": accuracy, "f1_macro": f1}
            else:
                f1 = f1_score(labels, predictions)
                return {"accuracy": accuracy, "f1": f1}

    def _get_main_metric(self, metrics: Dict[str, float]) -> float:
        """获取主要评估指标"""
        metric_name = config.TASK_METRICS.get(self.task, "accuracy")

        if metric_name == "matthews_corrcoef":
            return metrics.get("matthews_corrcoef", 0.0)
        elif metric_name == "pearson_spearman":
            return metrics.get("combined_score", 0.0)
        elif metric_name == "f1":
            return metrics.get("f1", metrics.get("f1_macro", 0.0))
        else:
            return metrics.get("accuracy", 0.0)

    def _get_main_metric_name(self) -> str:
        """获取主要评估指标名称"""
        return config.TASK_METRICS.get(self.task, "accuracy")

    def _save_checkpoint(self, epoch: int, is_best: bool = False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'global_step': self.global_step,
            'model_state_dict': self.model_manager.model.state_dict(),
            'best_metric': self.best_metric,
            'task': self.task
        }

        # 保存当前检查点
        checkpoint_path = os.path.join(self.output_dir, f"checkpoint-{epoch}.pt")
        torch.save(checkpoint, checkpoint_path)

        # 保存最佳模型
        if is_best:
            best_path = os.path.join(self.output_dir, "best_model.pt")
            torch.save(checkpoint, best_path)

            # 同时保存HuggingFace格式
            self.model_manager.save_model(
                os.path.join(self.output_dir, "best_model_hf"),
                self.task
            )

    def _save_training_history(self):
        """保存训练历史"""
        history_path = os.path.join(self.output_dir, "training_history.json")
        with open(history_path, 'w', encoding='utf-8') as f:
            json.dump(self.training_history, f, indent=2, ensure_ascii=False)

        logger.info(f"训练历史已保存: {history_path}")

    def load_checkpoint(self, checkpoint_path: str):
        """加载检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        self.model_manager.model.load_state_dict(checkpoint['model_state_dict'])
        self.current_epoch = checkpoint['epoch']
        self.global_step = checkpoint['global_step']
        self.best_metric = checkpoint['best_metric']

        logger.info(f"检查点已加载: epoch={self.current_epoch}, "
                   f"step={self.global_step}, best_metric={self.best_metric}")

class SparkTrainer:
    """基于Spark的分布式训练器"""

    def __init__(self, spark: SparkSession, trainer: GLUETrainer):
        self.spark = spark
        self.trainer = trainer

    def distributed_train(self, train_df: DataFrame, eval_df: DataFrame) -> Dict[str, Any]:
        """分布式训练"""
        logger.info("开始分布式训练...")

        # 缓存数据以提高性能
        train_df.cache()
        eval_df.cache()

        # 准备数据
        train_loader, eval_loader = self.trainer.prepare_data(train_df, eval_df)

        # 执行训练
        results = self.trainer.train(train_loader, eval_loader)

        # 清理缓存
        train_df.unpersist()
        eval_df.unpersist()

        return results
