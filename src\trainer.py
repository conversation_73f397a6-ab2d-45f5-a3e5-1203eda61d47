"""
训练和评估模块
实现模型训练流程，包括训练循环、验证、性能评估等
"""
import os
import time
import json
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

from src.data_processor import GLUEDataProcessor
from src.model_manager import GLUEModelManager
from config import Config

class GLUETrainer:
    """GLUE任务训练器"""
    
    def __init__(self, task_name: str):
        """
        初始化训练器
        
        Args:
            task_name: GLUE任务名称
        """
        self.task_name = task_name
        self.config = Config()
        self.config.create_directories()
        
        # 初始化组件
        self.data_processor = GLUEDataProcessor()
        self.model_manager = GLUEModelManager(task_name)
        
        self.logger = self._setup_logger()
        
        # 训练历史
        self.training_history = {}
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(f"{__name__}.{self.task_name}")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
            
            # 文件处理器
            log_file = os.path.join(
                self.config.LOGS_DIR, 
                f"{self.task_name}_training.log"
            )
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(console_formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def prepare_data(self) -> Dict[str, Any]:
        """
        准备训练数据
        
        Returns:
            处理后的数据集字典
        """
        self.logger.info(f"Preparing data for task: {self.task_name}")
        
        # 加载原始数据
        raw_datasets = self.data_processor.load_task_data(self.task_name)
        
        # 预处理数据
        processed_datasets = self.data_processor.preprocess_for_model(
            raw_datasets, self.task_name
        )
        
        # 获取数据统计信息
        data_stats = self.data_processor.get_data_statistics(
            processed_datasets, self.task_name
        )
        
        # 保存数据统计信息
        stats_file = os.path.join(
            self.config.RESULTS_DIR, 
            f"{self.task_name}_data_stats.json"
        )
        with open(stats_file, 'w') as f:
            json.dump(data_stats, f, indent=2)
        
        self.logger.info(f"Data statistics saved to: {stats_file}")
        
        return processed_datasets
    
    def train_model(self, datasets: Dict[str, Any]) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            datasets: 预处理后的数据集
            
        Returns:
            训练结果字典
        """
        self.logger.info(f"Starting training for task: {self.task_name}")
        start_time = time.time()
        
        # 加载模型和分词器
        model, tokenizer = self.model_manager.load_model_and_tokenizer()
        
        # 分词数据集
        tokenized_datasets = self.model_manager.tokenize_dataset(datasets)
        
        # 确保有训练和验证数据
        if 'train' not in tokenized_datasets:
            raise ValueError("Training dataset not found")
        
        train_dataset = tokenized_datasets['train']
        eval_dataset = tokenized_datasets.get('dev', tokenized_datasets.get('validation'))
        
        if eval_dataset is None:
            self.logger.warning("No validation dataset found, using 10% of training data")
            # 分割训练数据
            train_size = int(0.9 * len(train_dataset))
            eval_size = len(train_dataset) - train_size
            train_dataset, eval_dataset = train_dataset.train_test_split(
                train_size=train_size, 
                test_size=eval_size,
                seed=self.config.SEED
            ).values()
        
        # 创建训练器
        trainer = self.model_manager.create_trainer(train_dataset, eval_dataset)
        
        # 开始训练
        self.logger.info("Starting model training...")
        train_result = trainer.train()
        
        # 保存模型
        self.model_manager.save_model(trainer)
        
        # 评估模型
        eval_result = trainer.evaluate()
        
        # 计算训练时间
        training_time = time.time() - start_time
        
        # 整理训练结果
        results = {
            'task_name': self.task_name,
            'training_time': training_time,
            'train_result': {
                'train_loss': train_result.training_loss,
                'train_runtime': train_result.metrics.get('train_runtime', 0),
                'train_samples_per_second': train_result.metrics.get('train_samples_per_second', 0)
            },
            'eval_result': eval_result,
            'model_config': {
                'model_name': self.config.MODEL_NAME,
                'num_epochs': self.config.NUM_EPOCHS,
                'batch_size': self.config.BATCH_SIZE,
                'learning_rate': self.config.LEARNING_RATE,
                'max_length': self.config.MAX_LENGTH
            },
            'timestamp': datetime.now().isoformat()
        }
        
        # 保存训练结果
        results_file = os.path.join(
            self.config.RESULTS_DIR,
            f"{self.task_name}_training_results.json"
        )
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        self.logger.info(f"Training completed in {training_time:.2f} seconds")
        self.logger.info(f"Training results saved to: {results_file}")
        
        # 保存训练历史
        self.training_history = results
        
        return results
    
    def evaluate_model(self, datasets: Dict[str, Any], 
                      model_path: str = None) -> Dict[str, Any]:
        """
        评估模型性能
        
        Args:
            datasets: 数据集字典
            model_path: 模型路径（可选）
            
        Returns:
            评估结果字典
        """
        self.logger.info(f"Evaluating model for task: {self.task_name}")
        
        # 加载模型
        if model_path:
            self.model_manager.load_saved_model(model_path)
        elif not self.model_manager.model:
            # 尝试加载默认保存的模型
            default_path = os.path.join(self.config.MODEL_SAVE_DIR, self.task_name)
            if os.path.exists(default_path):
                self.model_manager.load_saved_model(default_path)
            else:
                raise ValueError("No model found. Train a model first or provide model_path.")
        
        # 分词数据集
        tokenized_datasets = self.model_manager.tokenize_dataset(datasets)
        
        evaluation_results = {}
        
        # 评估每个数据集分割
        for split, dataset in tokenized_datasets.items():
            if len(dataset) == 0:
                continue
                
            self.logger.info(f"Evaluating on {split} set...")
            
            # 创建临时训练器用于评估
            trainer = self.model_manager.create_trainer(dataset, dataset)
            
            # 进行评估
            eval_result = trainer.evaluate(eval_dataset=dataset)
            evaluation_results[split] = eval_result
            
            self.logger.info(f"{split} evaluation completed")
        
        # 保存评估结果
        eval_file = os.path.join(
            self.config.RESULTS_DIR,
            f"{self.task_name}_evaluation_results.json"
        )
        with open(eval_file, 'w') as f:
            json.dump(evaluation_results, f, indent=2)
        
        self.logger.info(f"Evaluation results saved to: {eval_file}")
        
        return evaluation_results
    
    def plot_training_history(self, save_path: str = None):
        """
        绘制训练历史图表
        
        Args:
            save_path: 图表保存路径
        """
        if not self.training_history:
            self.logger.warning("No training history available")
            return
        
        if save_path is None:
            save_path = os.path.join(
                self.config.RESULTS_DIR,
                f"{self.task_name}_training_plot.png"
            )
        
        # 创建图表
        fig, axes = plt.subplots(1, 2, figsize=(15, 5))
        
        # 训练损失图
        if 'train_result' in self.training_history:
            train_loss = self.training_history['train_result'].get('train_loss', 0)
            axes[0].bar(['Training Loss'], [train_loss])
            axes[0].set_title('Training Loss')
            axes[0].set_ylabel('Loss')
        
        # 评估指标图
        if 'eval_result' in self.training_history:
            eval_metrics = self.training_history['eval_result']
            metric_names = []
            metric_values = []
            
            for key, value in eval_metrics.items():
                if key.startswith('eval_') and isinstance(value, (int, float)):
                    metric_names.append(key.replace('eval_', ''))
                    metric_values.append(value)
            
            if metric_names:
                axes[1].bar(metric_names, metric_values)
                axes[1].set_title('Evaluation Metrics')
                axes[1].set_ylabel('Score')
                axes[1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"Training plot saved to: {save_path}")
    
    def run_full_pipeline(self) -> Dict[str, Any]:
        """
        运行完整的训练流水线
        
        Returns:
            完整的结果字典
        """
        self.logger.info(f"Starting full pipeline for task: {self.task_name}")
        
        try:
            # 1. 准备数据
            datasets = self.prepare_data()
            
            # 2. 训练模型
            training_results = self.train_model(datasets)
            
            # 3. 评估模型
            evaluation_results = self.evaluate_model(datasets)
            
            # 4. 绘制训练历史
            self.plot_training_history()
            
            # 5. 整理最终结果
            final_results = {
                'task_name': self.task_name,
                'training_results': training_results,
                'evaluation_results': evaluation_results,
                'pipeline_completed': True,
                'timestamp': datetime.now().isoformat()
            }
            
            # 保存最终结果
            final_file = os.path.join(
                self.config.RESULTS_DIR,
                f"{self.task_name}_final_results.json"
            )
            with open(final_file, 'w') as f:
                json.dump(final_results, f, indent=2)
            
            self.logger.info(f"Full pipeline completed for task: {self.task_name}")
            self.logger.info(f"Final results saved to: {final_file}")
            
            return final_results
            
        except Exception as e:
            self.logger.error(f"Pipeline failed for task {self.task_name}: {str(e)}")
            raise
        
        finally:
            # 清理资源
            self.data_processor.close()
    
    def __del__(self):
        """析构函数，清理资源"""
        if hasattr(self, 'data_processor'):
            self.data_processor.close()
