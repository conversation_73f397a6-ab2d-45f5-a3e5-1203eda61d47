"""
端到端测试 - 完整的训练和预测流程
"""
import os
import sys
import logging
import torch
import pandas as pd
from pyspark.sql import SparkSession

# 设置环境变量避免TensorFlow依赖
os.environ['USE_TF'] = 'NO'
os.environ['USE_TORCH'] = 'YES'

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from model_manager import GLUEModelManager
from trainer import GLUETrainer
from predictor import GLUEPredictor
from data_processor import GLUEDataProcessor
from utils import setup_logging, set_seed
import config

def create_spark_session():
    """创建Spark会话"""
    try:
        spark = SparkSession.builder \
            .appName("GLUE-End-to-End-Test") \
            .master("local[2]") \
            .config("spark.driver.memory", "4g") \
            .config("spark.executor.memory", "4g") \
            .getOrCreate()
        
        print(f"Spark会话创建成功，版本: {spark.version}")
        return spark
    except Exception as e:
        print(f"创建Spark会话失败: {e}")
        return None

def test_small_scale_training():
    """测试小规模训练"""
    print("=" * 60)
    print("端到端测试：小规模训练和预测")
    print("=" * 60)
    
    try:
        # 创建Spark会话
        spark = create_spark_session()
        if spark is None:
            return False
        
        # 1. 数据加载和处理
        print("\n1. 数据加载和处理")
        print("-" * 30)
        
        processor = GLUEDataProcessor(spark, config.DATA_DIR)
        
        # 加载CoLA数据（较小的数据集）
        train_df = processor.load_task_data("CoLA", "train")
        eval_df = processor.load_task_data("CoLA", "dev")
        
        print(f"✓ 数据加载成功")
        print(f"  - 训练集: {train_df.count()} 样本")
        print(f"  - 验证集: {eval_df.count()} 样本")
        
        # 为了快速测试，只使用一小部分数据
        train_sample = train_df.limit(100)  # 只用100个样本
        eval_sample = eval_df.limit(50)     # 只用50个样本
        
        print(f"✓ 采样完成（用于快速测试）")
        print(f"  - 训练样本: {train_sample.count()}")
        print(f"  - 验证样本: {eval_sample.count()}")
        
        # 2. 模型初始化
        print("\n2. 模型初始化")
        print("-" * 30)
        
        model_manager = GLUEModelManager()
        model_manager.load_tokenizer()
        model_manager.load_model_for_task("CoLA")
        
        print(f"✓ 模型初始化成功")
        print(f"  - 模型参数: {model_manager.get_model_info()['total_parameters']:,}")
        
        # 3. 训练器设置
        print("\n3. 训练器设置")
        print("-" * 30)
        
        # 创建临时输出目录
        output_dir = os.path.join(config.MODEL_SAVE_DIR, "test_cola_small")
        trainer = GLUETrainer(model_manager, "CoLA", output_dir)
        
        print(f"✓ 训练器创建成功")
        print(f"  - 输出目录: {output_dir}")
        
        # 4. 数据准备
        print("\n4. 数据准备")
        print("-" * 30)
        
        train_loader, eval_loader = trainer.prepare_data(train_sample, eval_sample)
        
        print(f"✓ 数据准备完成")
        print(f"  - 训练批次: {len(train_loader)}")
        print(f"  - 验证批次: {len(eval_loader)}")
        
        # 5. 快速训练（只训练1个epoch）
        print("\n5. 快速训练")
        print("-" * 30)
        
        # 使用较小的学习率和较少的epoch进行快速测试
        training_results = trainer.train(
            train_loader, 
            eval_loader, 
            epochs=1,  # 只训练1个epoch
            learning_rate=5e-5
        )
        
        print(f"✓ 训练完成")
        print(f"  - 最佳指标: {training_results['best_metric']:.4f}")
        print(f"  - 训练时间: {training_results['total_time']:.2f}秒")
        
        # 6. 模型预测测试
        print("\n6. 模型预测测试")
        print("-" * 30)
        
        predictor = GLUEPredictor(model_manager, "CoLA")
        
        # 测试一些句子
        test_sentences = [
            "This is a grammatically correct sentence.",
            "This sentence is also correct.",
            "Sentence this correct is not.",  # 语法错误
            "Very good sentence structure here."
        ]
        
        predictions = predictor.predict_texts(test_sentences)
        
        print(f"✓ 预测完成")
        print(f"  - 预测数量: {predictions['num_samples']}")
        print(f"  - 预测结果: {predictions['predictions']}")
        
        # 显示详细预测结果
        for i, (sentence, pred, prob) in enumerate(zip(
            test_sentences, 
            predictions['predictions'], 
            predictions['probabilities']
        )):
            print(f"  句子 {i+1}: {sentence}")
            print(f"    预测: {'正确' if pred == 1 else '错误'} (置信度: {max(prob):.3f})")
        
        # 7. 生成提交文件
        print("\n7. 生成提交文件")
        print("-" * 30)
        
        submission_path = os.path.join(config.RESULTS_DIR, "test_cola_submission.tsv")
        predictor.generate_submission(test_sentences, submission_path)
        
        print(f"✓ 提交文件生成: {submission_path}")
        
        # 8. 清理资源
        print("\n8. 清理资源")
        print("-" * 30)
        
        spark.stop()
        print("✓ Spark会话已关闭")
        
        return True
        
    except Exception as e:
        print(f"✗ 端到端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_tasks():
    """测试多个任务的快速验证"""
    print("\n" + "=" * 60)
    print("多任务快速验证测试")
    print("=" * 60)
    
    try:
        # 测试不同类型的任务
        test_tasks = [
            ("CoLA", "classification", ["This is correct.", "Wrong this is."]),
            ("STS-B", "regression", [("The cat sleeps.", "A cat is sleeping.")]),
            ("MRPC", "classification", [("Hello world.", "Hi world.")])
        ]
        
        for task, task_type, test_data in test_tasks:
            print(f"\n测试任务: {task} ({task_type})")
            print("-" * 40)
            
            try:
                # 创建模型管理器和预测器
                model_manager = GLUEModelManager()
                model_manager.load_tokenizer()
                model_manager.load_model_for_task(task)
                
                predictor = GLUEPredictor(model_manager, task)
                
                # 执行预测
                results = predictor.predict_texts(test_data)
                
                print(f"✓ {task} 预测成功")
                print(f"  - 预测结果: {results['predictions']}")
                
                if task_type == "regression":
                    print(f"  - 相似度分数: {results['predictions']}")
                else:
                    print(f"  - 分类结果: {results['predictions']}")
                    if 'probabilities' in results:
                        print(f"  - 置信度: {[max(prob) for prob in results['probabilities']]}")
                
            except Exception as e:
                print(f"✗ {task} 测试失败: {e}")
                continue
        
        return True
        
    except Exception as e:
        print(f"✗ 多任务测试失败: {e}")
        return False

def test_performance_metrics():
    """测试性能指标"""
    print("\n" + "=" * 60)
    print("性能指标测试")
    print("=" * 60)
    
    try:
        import time
        
        # 创建模型管理器
        model_manager = GLUEModelManager()
        model_manager.load_tokenizer()
        model_manager.load_model_for_task("CoLA")
        
        predictor = GLUEPredictor(model_manager, "CoLA")
        
        # 测试不同批次大小的性能
        test_sentences = [
            "This is a test sentence for performance evaluation.",
            "Another sentence for testing purposes.",
            "Third sentence to measure prediction speed.",
            "Fourth sentence for comprehensive testing.",
            "Fifth sentence to complete the batch."
        ] * 10  # 50个句子
        
        batch_sizes = [1, 5, 10, 20]
        
        print("批次大小性能测试:")
        for batch_size in batch_sizes:
            start_time = time.time()
            
            results = predictor.predict_texts(test_sentences, batch_size=batch_size)
            
            end_time = time.time()
            elapsed = end_time - start_time
            
            throughput = len(test_sentences) / elapsed
            
            print(f"  批次大小 {batch_size:2d}: {elapsed:.3f}秒, {throughput:.1f} 句子/秒")
        
        return True
        
    except Exception as e:
        print(f"✗ 性能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始端到端测试...")
    
    # 设置环境
    set_seed(config.RANDOM_SEED)
    logger = setup_logging(config.LOG_DIR)
    
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    print(f"设备: {config.DEVICE}")
    
    # 运行测试
    tests = [
        test_small_scale_training,
        test_multiple_tasks,
        test_performance_metrics
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"测试 {test_func.__name__} 出现异常: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 60)
    print("端到端测试总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 所有端到端测试通过! ({passed}/{total})")
        print("\n✅ 系统已准备好进行完整的GLUE基准测试！")
    else:
        print(f"❌ 部分测试失败: {passed}/{total} 通过")
    
    for i, (test_func, result) in enumerate(zip(tests, results)):
        status = "✅" if result else "❌"
        print(f"{status} {test_func.__name__}")
