#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版测试脚本 - 避免编码问题
"""
import os
import sys
import subprocess
import time

def print_header():
    """打印标题"""
    print("Spark GLUE 本地测试")
    print("=" * 50)
    print("硬件配置:")
    print("   GPU: NVIDIA GeForce RTX 3060 (6GB)")
    print("   PyTorch: 2.7.1+cu118")
    print("   Spark: 3.4.0")
    print("   Java: 17.0.2")
    print("=" * 50)

def check_environment():
    """检查环境"""
    print("检查环境...")
    
    try:
        result = subprocess.run([sys.executable, "check_env_simple.py"], 
                              capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            print("[OK] 环境检查通过")
            print(result.stdout)
            return True
        else:
            print("[ERROR] 环境检查失败:")
            print(result.stdout)
            if result.stderr:
                print(result.stderr)
            return False
    except Exception as e:
        print(f"[ERROR] 环境检查出错: {e}")
        return False

def setup_project():
    """设置项目"""
    print("\n设置项目结构...")
    
    try:
        result = subprocess.run([sys.executable, "setup_local_project.py"], 
                              capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            print("[OK] 项目设置完成")
            return True
        else:
            print("[ERROR] 项目设置失败:")
            print(result.stdout)
            if result.stderr:
                print(result.stderr)
            return False
    except Exception as e:
        print(f"[ERROR] 项目设置出错: {e}")
        return False

def check_dependencies():
    """检查依赖"""
    print("\n检查依赖包...")
    
    required_packages = [
        "torch", "transformers", "datasets", 
        "pyspark", "pandas", "numpy", 
        "sklearn", "tqdm"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"[OK] {package}")
        except ImportError:
            print(f"[ERROR] {package} - 需要安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("[OK] 所有依赖包已安装")
    return True

def create_simple_test():
    """创建简单测试"""
    print("\n创建简单测试...")
    
    # 创建基本目录
    os.makedirs("data/glue/SST-2", exist_ok=True)
    os.makedirs("results", exist_ok=True)
    
    # 创建简单的测试数据
    import pandas as pd
    
    # SST-2测试数据
    train_data = [
        {"sentence": "This is a great movie!", "label": 1},
        {"sentence": "I love this film.", "label": 1},
        {"sentence": "Terrible movie.", "label": 0},
        {"sentence": "Amazing story.", "label": 1},
        {"sentence": "Boring plot.", "label": 0}
    ] * 20  # 重复20次，共100条数据
    
    dev_data = [
        {"sentence": "Excellent film!", "label": 1},
        {"sentence": "Bad movie.", "label": 0},
        {"sentence": "Great acting.", "label": 1},
        {"sentence": "Poor story.", "label": 0}
    ] * 5  # 共20条数据
    
    test_data = [
        {"index": i, "sentence": f"Test sentence {i}"}
        for i in range(10)
    ]
    
    # 保存数据
    pd.DataFrame(train_data).to_csv("data/glue/SST-2/train.tsv", sep='\t', index=False)
    pd.DataFrame(dev_data).to_csv("data/glue/SST-2/dev.tsv", sep='\t', index=False)
    pd.DataFrame(test_data).to_csv("data/glue/SST-2/test.tsv", sep='\t', index=False)
    
    print("[OK] 测试数据已创建")
    return True

def run_simple_training():
    """运行简单训练"""
    print("\n运行简单训练...")
    
    # 创建简单的训练脚本
    training_script = """
import os
import pandas as pd
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification, Trainer, TrainingArguments
from datasets import Dataset
from sklearn.metrics import accuracy_score
import numpy as np

def compute_metrics(eval_pred):
    predictions, labels = eval_pred
    predictions = np.argmax(predictions, axis=1)
    return {"accuracy": accuracy_score(labels, predictions)}

def main():
    print("开始简单训练测试...")
    
    # 检查设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 加载数据
    train_df = pd.read_csv("data/glue/SST-2/train.tsv", sep='\\t')
    dev_df = pd.read_csv("data/glue/SST-2/dev.tsv", sep='\\t')
    
    print(f"训练数据: {len(train_df)} 条")
    print(f"验证数据: {len(dev_df)} 条")
    
    # 加载模型和tokenizer
    model_name = "distilbert-base-uncased"
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForSequenceClassification.from_pretrained(model_name, num_labels=2)
    
    # 预处理数据
    def preprocess_data(df):
        texts = df['sentence'].tolist()
        labels = df['label'].tolist()
        
        encodings = tokenizer(texts, truncation=True, padding=True, max_length=128)
        
        return Dataset.from_dict({
            'input_ids': encodings['input_ids'],
            'attention_mask': encodings['attention_mask'],
            'labels': labels
        })
    
    train_dataset = preprocess_data(train_df)
    eval_dataset = preprocess_data(dev_df)
    
    # 训练参数
    training_args = TrainingArguments(
        output_dir="./results/test_output",
        num_train_epochs=1,  # 只训练1个epoch用于测试
        per_device_train_batch_size=4,  # 小批次
        per_device_eval_batch_size=4,
        warmup_steps=10,
        weight_decay=0.01,
        logging_dir="./results/logs",
        logging_steps=10,
        evaluation_strategy="epoch",
        save_strategy="epoch",
        load_best_model_at_end=True,
        report_to=None
    )
    
    # 创建训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        compute_metrics=compute_metrics,
    )
    
    # 开始训练
    print("开始训练...")
    trainer.train()
    
    # 评估
    print("评估模型...")
    eval_results = trainer.evaluate()
    
    print(f"测试完成！准确率: {eval_results['eval_accuracy']:.4f}")
    
    # 保存结果
    import json
    with open("results/simple_test_results.json", "w") as f:
        json.dump(eval_results, f, indent=2)
    
    print("结果已保存到: results/simple_test_results.json")
    return True

if __name__ == "__main__":
    main()
"""
    
    # 保存训练脚本
    with open("simple_training.py", "w", encoding='utf-8') as f:
        f.write(training_script)
    
    # 运行训练
    try:
        print("执行训练脚本...")
        result = subprocess.run([sys.executable, "simple_training.py"], 
                              text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("[OK] 简单训练完成")
            return True
        else:
            print("[ERROR] 训练失败")
            return False
            
    except Exception as e:
        print(f"[ERROR] 训练出错: {e}")
        return False

def show_results():
    """显示结果"""
    print("\n查看结果...")
    
    results_file = "results/simple_test_results.json"
    if os.path.exists(results_file):
        try:
            import json
            with open(results_file, 'r') as f:
                results = json.load(f)
            
            print("训练结果:")
            for key, value in results.items():
                print(f"  {key}: {value}")
            
            return True
        except Exception as e:
            print(f"[ERROR] 读取结果失败: {e}")
            return False
    else:
        print("[WARNING] 结果文件不存在")
        return False

def main():
    """主函数"""
    print_header()
    
    if len(sys.argv) > 1 and sys.argv[1] == "full":
        # 完整测试流程
        print("执行完整测试流程...")
        
        steps = [
            ("检查环境", check_environment),
            ("设置项目", setup_project),
            ("检查依赖", check_dependencies),
            ("创建测试", create_simple_test),
            ("运行训练", run_simple_training),
            ("显示结果", show_results)
        ]
        
        for step_name, step_func in steps:
            print(f"\n{step_name}...")
            if not step_func():
                print(f"[ERROR] {step_name}失败，停止执行")
                break
            print(f"[OK] {step_name}完成")
        else:
            print("\n完整测试流程执行完成！")
    
    else:
        print("使用方法:")
        print("  python test_simple.py full    # 运行完整测试")

if __name__ == "__main__":
    main()
