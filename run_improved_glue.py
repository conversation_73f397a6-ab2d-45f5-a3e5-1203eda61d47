"""
改进的GLUE基准测试脚本
使用优化的参数和更好的错误处理
"""
import os
import sys
import json
import time
from typing import Dict, Any

# 设置环境变量
os.environ['USE_TF'] = 'NO'
os.environ['USE_TORCH'] = 'YES'

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from main import GLUEBenchmark
from utils import setup_logging, set_seed
import config

def run_improved_task(task: str, epochs: int = 3, max_samples: int = 5000, 
                     learning_rate: float = 2e-5) -> Dict[str, Any]:
    """运行改进的单任务训练"""
    print(f"\n{'='*60}")
    print(f"改进训练: {task}")
    print(f"参数: epochs={epochs}, samples={max_samples}, lr={learning_rate}")
    print(f"{'='*60}")
    
    benchmark = GLUEBenchmark()
    
    try:
        # 训练
        print(f"🚀 开始训练 {task}...")
        start_time = time.time()
        
        train_results = benchmark.train_task(
            task=task,
            epochs=epochs,
            learning_rate=learning_rate,
            max_samples=max_samples
        )
        
        train_time = time.time() - start_time
        
        # 预测
        print(f"🔮 开始预测 {task}...")
        submission_path = benchmark.predict_task(task)
        
        # 收集结果
        results = {
            'task': task,
            'train_time': train_time,
            'best_metric': train_results['best_metric'],
            'training_history': train_results['training_history'],
            'submission_file': submission_path,
            'status': 'success',
            'epochs': epochs,
            'max_samples': max_samples,
            'learning_rate': learning_rate
        }
        
        # 显示最终结果
        if train_results['training_history']:
            final_metrics = train_results['training_history'][-1]['eval_metrics']
            print(f"✅ {task} 完成!")
            print(f"   最佳指标: {train_results['best_metric']:.4f}")
            print(f"   训练时间: {train_time:.2f}秒")
            
            # 显示具体指标
            if task == "CoLA":
                print(f"   Matthews相关系数: {final_metrics.get('matthews_corrcoef', 0)*100:.2f}%")
                print(f"   准确率: {final_metrics.get('accuracy', 0)*100:.2f}%")
            elif task == "STS-B":
                pearson = final_metrics.get('pearson_correlation', 0)
                spearman = final_metrics.get('spearman_correlation', 0)
                print(f"   Pearson相关系数: {pearson*100:.2f}%")
                print(f"   Spearman相关系数: {spearman*100:.2f}%")
            else:
                print(f"   准确率: {final_metrics.get('accuracy', 0)*100:.2f}%")
                if 'f1' in final_metrics:
                    print(f"   F1分数: {final_metrics.get('f1', 0)*100:.2f}%")
        
        return results
        
    except Exception as e:
        print(f"❌ {task} 失败: {e}")
        import traceback
        traceback.print_exc()
        return {
            'task': task,
            'status': 'failed',
            'error': str(e),
            'epochs': epochs,
            'max_samples': max_samples,
            'learning_rate': learning_rate
        }
    
    finally:
        benchmark.cleanup()

def get_task_specific_params(task: str) -> Dict[str, Any]:
    """获取任务特定的优化参数"""
    params = {
        # 小数据集任务 - 更多epoch，较小学习率
        "CoLA": {"epochs": 4, "max_samples": 8000, "learning_rate": 2e-5},
        "RTE": {"epochs": 4, "max_samples": 2500, "learning_rate": 2e-5},
        "WNLI": {"epochs": 5, "max_samples": 600, "learning_rate": 1e-5},
        "MRPC": {"epochs": 4, "max_samples": 3500, "learning_rate": 2e-5},
        
        # 中等数据集任务
        "STS-B": {"epochs": 3, "max_samples": 5000, "learning_rate": 2e-5},
        "SST-2": {"epochs": 3, "max_samples": 10000, "learning_rate": 2e-5},
        "QNLI": {"epochs": 3, "max_samples": 8000, "learning_rate": 2e-5},
        
        # 大数据集任务 - 较少epoch，但更多样本
        "QQP": {"epochs": 2, "max_samples": 15000, "learning_rate": 2e-5},
        "MNLI": {"epochs": 2, "max_samples": 20000, "learning_rate": 2e-5},
    }
    
    return params.get(task, {"epochs": 3, "max_samples": 5000, "learning_rate": 2e-5})

def main():
    """主函数"""
    print("🚀 改进的GLUE基准测试")
    print("=" * 80)
    
    # 设置日志
    logger = setup_logging(config.LOG_DIR, "INFO")
    set_seed(config.RANDOM_SEED)
    
    print(f"使用优化参数进行训练...")
    print(f"模型: {config.MODEL_NAME}")
    
    # 优先处理之前失败或表现较差的任务
    priority_tasks = [
        "STS-B",    # 修复NaN问题
        "MRPC",     # 修复失败问题
        "QNLI",     # 改进性能
        "WNLI",     # 改进性能
        "CoLA",     # 进一步优化
        "RTE",      # 进一步优化
        "SST-2",    # 已经不错，但可以更好
        "QQP",      # 大数据集
        "MNLI",     # 大数据集
    ]
    
    all_results = {}
    
    for task in priority_tasks:
        try:
            # 获取任务特定参数
            params = get_task_specific_params(task)
            
            print(f"\n🎯 处理任务: {task}")
            print(f"优化参数: {params}")
            
            result = run_improved_task(
                task=task,
                epochs=params["epochs"],
                max_samples=params["max_samples"],
                learning_rate=params["learning_rate"]
            )
            
            all_results[task] = result
            
            # 保存中间结果
            with open(os.path.join(config.RESULTS_DIR, "improved_glue_results.json"), 'w', encoding='utf-8') as f:
                json.dump(all_results, f, indent=2, ensure_ascii=False, default=str)
            
            # 如果任务失败，尝试用更保守的参数重试一次
            if result.get('status') == 'failed' and task in ["STS-B", "MRPC"]:
                print(f"🔄 使用保守参数重试 {task}...")
                conservative_params = {"epochs": 2, "max_samples": 1000, "learning_rate": 1e-5}
                
                retry_result = run_improved_task(
                    task=task,
                    epochs=conservative_params["epochs"],
                    max_samples=conservative_params["max_samples"],
                    learning_rate=conservative_params["learning_rate"]
                )
                
                if retry_result.get('status') == 'success':
                    all_results[task] = retry_result
                    print(f"✅ {task} 重试成功!")
            
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断测试")
            break
        except Exception as e:
            print(f"❌ 任务 {task} 出现异常: {e}")
            all_results[task] = {'status': 'failed', 'error': str(e)}
    
    # 生成改进后的分析报告
    print(f"\n{'='*80}")
    print("📊 改进结果分析")
    print(f"{'='*80}")
    
    # BERT-base基准结果
    bert_base_results = {
        "CoLA": 57.11, "SST-2": 92.66, "MRPC": 89.00, "STS-B": 89.13,
        "QQP": 90.84, "MNLI": 83.27, "QNLI": 91.47, "RTE": 72.20, "WNLI": 56.34
    }
    
    print(f"{'任务':<8} {'我们的结果':<12} {'BERT基准':<12} {'差异':<10} {'状态':<10}")
    print("-" * 60)
    
    successful_tasks = 0
    improved_tasks = 0
    
    for task in priority_tasks:
        if task in all_results and all_results[task].get('status') == 'success':
            result = all_results[task]
            if 'training_history' in result and result['training_history']:
                final_metrics = result['training_history'][-1]['eval_metrics']
                
                # 提取主要指标
                if task == "CoLA":
                    our_score = final_metrics.get('matthews_corrcoef', 0) * 100
                elif task == "STS-B":
                    our_score = final_metrics.get('pearson_correlation', 0) * 100
                else:
                    our_score = final_metrics.get('accuracy', 0) * 100
                
                bert_score = bert_base_results.get(task, 0)
                diff = our_score - bert_score
                
                if diff > -5:
                    status = "🟢 优秀" if diff > 0 else "🟡 良好"
                    if diff > 0:
                        improved_tasks += 1
                else:
                    status = "🔴 需改进"
                
                print(f"{task:<8} {our_score:<12.2f} {bert_score:<12.2f} {diff:+<10.2f} {status:<10}")
                successful_tasks += 1
        else:
            print(f"{task:<8} {'失败':<12} {bert_base_results.get(task, 0):<12.2f} {'N/A':<10} {'❌ 错误':<10}")
    
    print("-" * 60)
    print(f"成功任务: {successful_tasks}/{len(priority_tasks)}")
    print(f"改进任务: {improved_tasks}/{successful_tasks}")
    
    # 保存最终结果
    final_report = {
        "results": all_results,
        "summary": {
            "successful_tasks": successful_tasks,
            "improved_tasks": improved_tasks,
            "total_tasks": len(priority_tasks)
        },
        "bert_baseline": bert_base_results
    }
    
    with open(os.path.join(config.RESULTS_DIR, "improved_glue_final_report.json"), 'w', encoding='utf-8') as f:
        json.dump(final_report, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n📁 详细结果已保存:")
    print(f"- 完整结果: results/improved_glue_results.json")
    print(f"- 最终报告: results/improved_glue_final_report.json")
    print(f"- 提交文件: results/*_submission.tsv")
    
    print(f"\n🌐 提交到GLUE官网:")
    print(f"1. 检查 results/ 目录中的TSV文件")
    print(f"2. 访问 https://gluebenchmark.com/")
    print(f"3. 上传提交文件并查看排名")

if __name__ == "__main__":
    main()
