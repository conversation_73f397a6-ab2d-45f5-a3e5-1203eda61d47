#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Spark GLUE训练启动脚本
"""
import sys
import os
import argparse
from datetime import datetime

def print_header():
    """打印标题"""
    print("=" * 60)
    print("🚀 Spark GLUE 文本分类训练系统")
    print("=" * 60)
    print("结合Apache Spark数据处理和PyTorch深度学习")
    print("支持GLUE基准测试的多个文本分类任务")
    print("=" * 60)
    
    # 显示环境信息
    try:
        import torch
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"GPU设备: {torch.cuda.get_device_name(0)}")
            print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
        
        import pyspark
        print(f"PySpark版本: {pyspark.__version__}")
        
    except ImportError as e:
        print(f"环境检查失败: {e}")
    
    print("=" * 60)

def run_single_task(task_name="SST-2", num_samples=1000):
    """运行单任务训练"""
    print(f"\n🎯 运行单任务训练: {task_name}")
    print(f"样本数量: {num_samples}")
    
    try:
        from spark_glue_trainer import SparkGLUEPipeline
        
        # 创建训练流水线
        pipeline = SparkGLUEPipeline(task_name, "distilbert-base-uncased")
        
        # 运行训练
        results = pipeline.run_full_pipeline(num_samples)
        
        # 显示结果
        print(f"\n✅ {task_name} 训练完成!")
        print(f"   准确率: {results['eval_results']['eval_accuracy']:.4f}")
        print(f"   F1分数: {results['eval_results']['eval_f1']:.4f}")
        print(f"   训练时间: {results['training_time']:.1f}秒")
        print(f"   模型保存: {results['model_path']}")
        
        return results
        
    except Exception as e:
        print(f"❌ 单任务训练失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_multi_task(tasks=None, num_samples=800):
    """运行多任务训练"""
    if tasks is None:
        tasks = ["SST-2", "RTE", "CoLA", "MRPC"]
    
    print(f"\n🎯 运行多任务训练: {', '.join(tasks)}")
    print(f"每个任务样本数: {num_samples}")
    
    try:
        from spark_glue_multi_task import run_multi_task_training
        
        # 运行多任务训练
        results = run_multi_task_training()
        
        print(f"\n✅ 多任务训练完成!")
        print("详细结果已保存到: results/multi_task_results.json")
        
        return results
        
    except Exception as e:
        print(f"❌ 多任务训练失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_demo():
    """运行演示"""
    print("\n🎪 运行Spark GLUE演示")
    
    print("\n第一步: 单任务训练演示 (SST-2)")
    sst2_results = run_single_task("SST-2", 500)
    
    if sst2_results:
        print("\n第二步: 多任务训练演示")
        multi_results = run_multi_task(["SST-2", "RTE"], 300)
        
        if multi_results:
            print("\n🎉 演示完成!")
            print("所有结果已保存到 results/ 目录")
        else:
            print("\n⚠️ 多任务训练失败，但单任务成功")
    else:
        print("\n❌ 演示失败")

def run_benchmark():
    """运行完整基准测试"""
    print("\n🏆 运行完整GLUE基准测试")
    
    tasks = ["SST-2", "RTE", "CoLA", "MRPC"]
    results_summary = {}
    
    print("开始逐个训练所有任务...")
    
    for task in tasks:
        print(f"\n--- 训练 {task} ---")
        result = run_single_task(task, 1500)
        
        if result:
            results_summary[task] = {
                "accuracy": result['eval_results']['eval_accuracy'],
                "f1": result['eval_results']['eval_f1'],
                "training_time": result['training_time']
            }
            print(f"✅ {task} 成功")
        else:
            results_summary[task] = {"status": "failed"}
            print(f"❌ {task} 失败")
    
    # 保存基准测试结果
    import json
    os.makedirs("results", exist_ok=True)
    with open("results/benchmark_results.json", "w") as f:
        json.dump(results_summary, f, indent=2)
    
    # 显示总结
    print("\n" + "=" * 60)
    print("🏆 GLUE基准测试完成!")
    print("\n任务性能总结:")
    
    for task, result in results_summary.items():
        if "accuracy" in result:
            print(f"  {task:6s}: 准确率={result['accuracy']:.4f}, "
                  f"F1={result['f1']:.4f}, 时间={result['training_time']:.1f}s")
        else:
            print(f"  {task:6s}: 失败")
    
    print(f"\n详细结果保存在: results/benchmark_results.json")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Spark GLUE文本分类训练系统")
    parser.add_argument("--mode", choices=["single", "multi", "demo", "benchmark"], 
                       default="demo", help="运行模式")
    parser.add_argument("--task", default="SST-2", 
                       help="单任务模式的任务名称")
    parser.add_argument("--tasks", nargs='+', default=["SST-2", "RTE"], 
                       help="多任务模式的任务列表")
    parser.add_argument("--samples", type=int, default=1000, 
                       help="每个任务的样本数量")
    
    args = parser.parse_args()
    
    # 打印标题
    print_header()
    
    # 记录开始时间
    start_time = datetime.now()
    
    try:
        if args.mode == "single":
            run_single_task(args.task, args.samples)
        
        elif args.mode == "multi":
            run_multi_task(args.tasks, args.samples)
        
        elif args.mode == "demo":
            run_demo()
        
        elif args.mode == "benchmark":
            run_benchmark()
        
        else:
            print("❌ 未知的运行模式")
            return
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断训练")
    
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 显示总用时
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        print(f"\n⏱️ 总用时: {total_time:.1f}秒")
        print("=" * 60)

if __name__ == "__main__":
    main()
