#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的Spark和PySpark测试脚本
测试所有核心功能是否正常工作
"""
import os
import sys
import time
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def print_header():
    """打印测试标题"""
    print("=" * 70)
    print("🔍 Spark & PySpark 完整功能测试")
    print("=" * 70)
    print("测试本地Spark环境的所有核心功能")
    print("包括：环境检查、会话创建、数据操作、SQL查询、文件I/O等")
    print("=" * 70)

def test_environment():
    """测试环境变量和基本配置"""
    print("\n📋 1. 环境变量检查")
    print("-" * 30)
    
    results = {}
    
    # 检查Python版本
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    print(f"Python版本: {python_version}")
    results["python_version"] = python_version
    
    # 检查SPARK_HOME
    spark_home = os.environ.get('SPARK_HOME')
    if spark_home:
        print(f"✅ SPARK_HOME: {spark_home}")
        results["spark_home"] = spark_home
        results["spark_home_exists"] = os.path.exists(spark_home)
    else:
        print("⚠️ SPARK_HOME 未设置")
        results["spark_home"] = None
        results["spark_home_exists"] = False
    
    # 检查JAVA_HOME
    java_home = os.environ.get('JAVA_HOME')
    if java_home:
        print(f"✅ JAVA_HOME: {java_home}")
        results["java_home"] = java_home
    else:
        print("⚠️ JAVA_HOME 未设置")
        results["java_home"] = None
    
    # 检查PATH中的Java
    try:
        import subprocess
        java_result = subprocess.run(['java', '-version'], capture_output=True, text=True)
        if java_result.returncode == 0:
            java_version = java_result.stderr.split('\n')[0]
            print(f"✅ Java版本: {java_version}")
            results["java_version"] = java_version
        else:
            print("❌ Java不可用")
            results["java_version"] = None
    except Exception as e:
        print(f"❌ Java检查失败: {e}")
        results["java_version"] = None
    
    return results

def test_pyspark_import():
    """测试PySpark导入"""
    print("\n📦 2. PySpark导入测试")
    print("-" * 30)
    
    results = {}
    
    try:
        import pyspark
        print(f"✅ PySpark版本: {pyspark.__version__}")
        results["pyspark_version"] = pyspark.__version__
        results["pyspark_import"] = True
        
        # 测试核心模块导入
        modules_to_test = [
            ("pyspark.sql", "SparkSession"),
            ("pyspark.sql.functions", "col"),
            ("pyspark.sql.types", "StructType"),
            ("pyspark.ml", "Pipeline"),
            ("pyspark.mllib.stat", "Statistics")
        ]
        
        for module_name, class_name in modules_to_test:
            try:
                module = __import__(module_name, fromlist=[class_name])
                getattr(module, class_name)
                print(f"✅ {module_name}.{class_name}")
                results[f"{module_name}_{class_name}"] = True
            except Exception as e:
                print(f"❌ {module_name}.{class_name}: {e}")
                results[f"{module_name}_{class_name}"] = False
        
    except ImportError as e:
        print(f"❌ PySpark导入失败: {e}")
        results["pyspark_import"] = False
        results["pyspark_version"] = None
    
    return results

def test_spark_session():
    """测试Spark会话创建"""
    print("\n🚀 3. Spark会话创建测试")
    print("-" * 30)
    
    results = {}
    spark = None
    
    try:
        from pyspark.sql import SparkSession
        
        # 创建Spark会话
        print("创建Spark会话...")
        start_time = time.time()
        
        spark = SparkSession.builder \
            .appName("SparkTest") \
            .master("local[*]") \
            .config("spark.driver.memory", "2g") \
            .config("spark.executor.memory", "1g") \
            .config("spark.sql.adaptive.enabled", "true") \
            .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
            .getOrCreate()
        
        creation_time = time.time() - start_time
        
        print(f"✅ Spark会话创建成功 ({creation_time:.2f}秒)")
        print(f"   Spark版本: {spark.version}")
        print(f"   应用名称: {spark.sparkContext.appName}")
        print(f"   Master: {spark.sparkContext.master}")
        print(f"   默认并行度: {spark.sparkContext.defaultParallelism}")
        
        results["session_creation"] = True
        results["creation_time"] = creation_time
        results["spark_version"] = spark.version
        results["app_name"] = spark.sparkContext.appName
        results["master"] = spark.sparkContext.master
        results["default_parallelism"] = spark.sparkContext.defaultParallelism
        
        return results, spark
        
    except Exception as e:
        print(f"❌ Spark会话创建失败: {e}")
        results["session_creation"] = False
        results["error"] = str(e)
        return results, None

def test_dataframe_operations(spark):
    """测试DataFrame操作"""
    print("\n📊 4. DataFrame操作测试")
    print("-" * 30)
    
    results = {}
    
    if not spark:
        print("❌ Spark会话不可用，跳过DataFrame测试")
        results["dataframe_test"] = False
        return results
    
    try:
        # 创建测试数据
        print("创建测试DataFrame...")
        data = [
            ("Alice", 25, "Engineer"),
            ("Bob", 30, "Manager"),
            ("Charlie", 35, "Developer"),
            ("Diana", 28, "Analyst"),
            ("Eve", 32, "Designer")
        ]
        
        columns = ["name", "age", "job"]
        df = spark.createDataFrame(data, columns)
        
        print(f"✅ DataFrame创建成功，行数: {df.count()}")
        results["dataframe_creation"] = True
        results["row_count"] = df.count()
        
        # 测试基本操作
        print("测试基本操作...")
        
        # 显示数据
        print("前3行数据:")
        df.show(3)
        
        # 选择列
        name_age_df = df.select("name", "age")
        print(f"✅ 列选择成功，列数: {len(name_age_df.columns)}")
        results["column_selection"] = True
        
        # 过滤数据
        filtered_df = df.filter(df.age > 30)
        filtered_count = filtered_df.count()
        print(f"✅ 数据过滤成功，过滤后行数: {filtered_count}")
        results["data_filtering"] = True
        results["filtered_count"] = filtered_count
        
        # 分组聚合
        from pyspark.sql.functions import avg, count
        agg_df = df.groupBy("job").agg(avg("age").alias("avg_age"), count("*").alias("count"))
        agg_count = agg_df.count()
        print(f"✅ 分组聚合成功，分组数: {agg_count}")
        results["groupby_aggregation"] = True
        results["group_count"] = agg_count
        
        # 排序
        sorted_df = df.orderBy("age")
        print("✅ 数据排序成功")
        results["data_sorting"] = True
        
        return results
        
    except Exception as e:
        print(f"❌ DataFrame操作失败: {e}")
        results["dataframe_test"] = False
        results["error"] = str(e)
        return results

def test_sql_operations(spark):
    """测试SQL操作"""
    print("\n🔍 5. SQL操作测试")
    print("-" * 30)
    
    results = {}
    
    if not spark:
        print("❌ Spark会话不可用，跳过SQL测试")
        results["sql_test"] = False
        return results
    
    try:
        # 创建临时视图
        print("创建临时视图...")
        data = [
            ("Product A", 100, "Electronics"),
            ("Product B", 200, "Clothing"),
            ("Product C", 150, "Electronics"),
            ("Product D", 80, "Books"),
            ("Product E", 300, "Electronics")
        ]
        
        columns = ["product_name", "price", "category"]
        df = spark.createDataFrame(data, columns)
        df.createOrReplaceTempView("products")
        
        print("✅ 临时视图创建成功")
        results["temp_view_creation"] = True
        
        # 执行SQL查询
        print("执行SQL查询...")
        
        # 基本查询
        sql_result1 = spark.sql("SELECT * FROM products WHERE price > 100")
        count1 = sql_result1.count()
        print(f"✅ 基本SQL查询成功，结果行数: {count1}")
        results["basic_sql_query"] = True
        results["basic_query_count"] = count1
        
        # 聚合查询
        sql_result2 = spark.sql("""
            SELECT category, 
                   COUNT(*) as product_count, 
                   AVG(price) as avg_price,
                   MAX(price) as max_price
            FROM products 
            GROUP BY category
            ORDER BY avg_price DESC
        """)
        
        print("聚合查询结果:")
        sql_result2.show()
        
        agg_count = sql_result2.count()
        print(f"✅ 聚合SQL查询成功，分组数: {agg_count}")
        results["aggregation_sql_query"] = True
        results["agg_query_count"] = agg_count
        
        # 复杂查询
        sql_result3 = spark.sql("""
            SELECT category,
                   product_name,
                   price,
                   RANK() OVER (PARTITION BY category ORDER BY price DESC) as price_rank
            FROM products
        """)
        
        print("窗口函数查询结果:")
        sql_result3.show()
        
        print("✅ 窗口函数查询成功")
        results["window_function_query"] = True
        
        return results
        
    except Exception as e:
        print(f"❌ SQL操作失败: {e}")
        results["sql_test"] = False
        results["error"] = str(e)
        return results

def test_file_operations(spark):
    """测试文件I/O操作"""
    print("\n💾 6. 文件I/O操作测试")
    print("-" * 30)
    
    results = {}
    
    if not spark:
        print("❌ Spark会话不可用，跳过文件I/O测试")
        results["file_io_test"] = False
        return results
    
    try:
        # 创建测试目录
        test_dir = "spark_test_data"
        os.makedirs(test_dir, exist_ok=True)
        
        # 创建测试数据
        print("创建测试数据...")
        data = [
            ("2023-01-01", "A", 100),
            ("2023-01-02", "B", 200),
            ("2023-01-03", "A", 150),
            ("2023-01-04", "C", 300),
            ("2023-01-05", "B", 250)
        ]
        
        columns = ["date", "category", "value"]
        df = spark.createDataFrame(data, columns)
        
        # 测试CSV写入和读取
        csv_path = f"{test_dir}/test_data.csv"
        print(f"写入CSV文件: {csv_path}")
        df.coalesce(1).write.mode("overwrite").option("header", "true").csv(csv_path)
        
        print("读取CSV文件...")
        read_df = spark.read.option("header", "true").option("inferSchema", "true").csv(csv_path)
        read_count = read_df.count()
        
        print(f"✅ CSV文件I/O成功，读取行数: {read_count}")
        results["csv_io"] = True
        results["csv_read_count"] = read_count
        
        # 测试JSON写入和读取
        json_path = f"{test_dir}/test_data.json"
        print(f"写入JSON文件: {json_path}")
        df.coalesce(1).write.mode("overwrite").json(json_path)
        
        print("读取JSON文件...")
        json_df = spark.read.json(json_path)
        json_count = json_df.count()
        
        print(f"✅ JSON文件I/O成功，读取行数: {json_count}")
        results["json_io"] = True
        results["json_read_count"] = json_count
        
        # 测试Parquet写入和读取
        parquet_path = f"{test_dir}/test_data.parquet"
        print(f"写入Parquet文件: {parquet_path}")
        df.write.mode("overwrite").parquet(parquet_path)
        
        print("读取Parquet文件...")
        parquet_df = spark.read.parquet(parquet_path)
        parquet_count = parquet_df.count()
        
        print(f"✅ Parquet文件I/O成功，读取行数: {parquet_count}")
        results["parquet_io"] = True
        results["parquet_read_count"] = parquet_count
        
        # 清理测试文件
        import shutil
        shutil.rmtree(test_dir)
        print("✅ 测试文件清理完成")
        
        return results
        
    except Exception as e:
        print(f"❌ 文件I/O操作失败: {e}")
        results["file_io_test"] = False
        results["error"] = str(e)
        return results

def test_performance(spark):
    """测试性能"""
    print("\n⚡ 7. 性能测试")
    print("-" * 30)
    
    results = {}
    
    if not spark:
        print("❌ Spark会话不可用，跳过性能测试")
        results["performance_test"] = False
        return results
    
    try:
        # 创建大数据集
        print("创建大数据集进行性能测试...")
        
        import random
        from pyspark.sql.types import StructType, StructField, StringType, IntegerType
        
        # 生成10万条数据
        num_records = 100000
        print(f"生成{num_records:,}条测试数据...")
        
        start_time = time.time()
        
        # 使用RDD创建大数据集
        def generate_record(i):
            return (
                f"user_{i}",
                random.randint(18, 80),
                random.choice(["A", "B", "C", "D", "E"]),
                random.randint(1000, 10000)
            )
        
        rdd = spark.sparkContext.parallelize(range(num_records)).map(generate_record)
        
        schema = StructType([
            StructField("user_id", StringType(), True),
            StructField("age", IntegerType(), True),
            StructField("category", StringType(), True),
            StructField("score", IntegerType(), True)
        ])
        
        large_df = spark.createDataFrame(rdd, schema)
        
        # 缓存数据集
        large_df.cache()
        
        creation_time = time.time() - start_time
        print(f"✅ 大数据集创建完成 ({creation_time:.2f}秒)")
        
        # 执行各种操作并计时
        operations = []
        
        # 计数操作
        start_time = time.time()
        count = large_df.count()
        count_time = time.time() - start_time
        operations.append(("Count", count_time, f"{count:,}条记录"))
        
        # 过滤操作
        start_time = time.time()
        filtered_count = large_df.filter(large_df.age > 50).count()
        filter_time = time.time() - start_time
        operations.append(("Filter", filter_time, f"{filtered_count:,}条记录"))
        
        # 分组聚合操作
        start_time = time.time()
        agg_result = large_df.groupBy("category").agg(
            {"age": "avg", "score": "sum"}
        ).collect()
        agg_time = time.time() - start_time
        operations.append(("GroupBy", agg_time, f"{len(agg_result)}个分组"))
        
        # 排序操作
        start_time = time.time()
        sorted_df = large_df.orderBy("score")
        sorted_count = sorted_df.limit(1000).count()  # 只取前1000条
        sort_time = time.time() - start_time
        operations.append(("Sort", sort_time, f"前{sorted_count}条记录"))
        
        # 显示性能结果
        print("\n性能测试结果:")
        print(f"{'操作':>10s} {'时间(秒)':>10s} {'结果':>15s}")
        print("-" * 40)
        
        for op_name, op_time, op_result in operations:
            print(f"{op_name:>10s} {op_time:>10.3f} {op_result:>15s}")
            results[f"{op_name.lower()}_time"] = op_time
        
        results["performance_test"] = True
        results["total_records"] = num_records
        
        return results
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        results["performance_test"] = False
        results["error"] = str(e)
        return results

def run_complete_test():
    """运行完整测试"""
    print_header()
    
    all_results = {}
    spark = None
    
    try:
        # 1. 环境检查
        env_results = test_environment()
        all_results["environment"] = env_results
        
        # 2. PySpark导入测试
        import_results = test_pyspark_import()
        all_results["pyspark_import"] = import_results
        
        if not import_results.get("pyspark_import", False):
            print("\n❌ PySpark导入失败，无法继续后续测试")
            return all_results
        
        # 3. Spark会话测试
        session_results, spark = test_spark_session()
        all_results["spark_session"] = session_results
        
        if not session_results.get("session_creation", False):
            print("\n❌ Spark会话创建失败，无法继续后续测试")
            return all_results
        
        # 4. DataFrame操作测试
        df_results = test_dataframe_operations(spark)
        all_results["dataframe_operations"] = df_results
        
        # 5. SQL操作测试
        sql_results = test_sql_operations(spark)
        all_results["sql_operations"] = sql_results
        
        # 6. 文件I/O测试
        file_results = test_file_operations(spark)
        all_results["file_operations"] = file_results
        
        # 7. 性能测试
        perf_results = test_performance(spark)
        all_results["performance"] = perf_results
        
    finally:
        # 关闭Spark会话
        if spark:
            print("\n🔄 关闭Spark会话...")
            spark.stop()
            print("✅ Spark会话已关闭")
    
    # 保存测试结果
    import json
    os.makedirs("results", exist_ok=True)
    with open("results/spark_complete_test_results.json", "w") as f:
        json.dump(all_results, f, indent=2)
    
    # 显示测试总结
    print("\n" + "=" * 70)
    print("📊 测试总结")
    print("=" * 70)
    
    test_categories = [
        ("环境检查", "environment"),
        ("PySpark导入", "pyspark_import"),
        ("Spark会话", "spark_session"),
        ("DataFrame操作", "dataframe_operations"),
        ("SQL操作", "sql_operations"),
        ("文件I/O", "file_operations"),
        ("性能测试", "performance")
    ]
    
    passed_tests = 0
    total_tests = len(test_categories)
    
    for test_name, test_key in test_categories:
        if test_key in all_results:
            # 判断测试是否通过
            test_data = all_results[test_key]
            if test_key == "environment":
                passed = test_data.get("pyspark_version") is not None
            elif test_key == "pyspark_import":
                passed = test_data.get("pyspark_import", False)
            else:
                passed = not test_data.get("error") and any(
                    v for k, v in test_data.items() 
                    if k.endswith("_creation") or k.endswith("_test") or k == "session_creation"
                )
            
            status = "✅ 通过" if passed else "❌ 失败"
            print(f"{test_name:>15s}: {status}")
            
            if passed:
                passed_tests += 1
        else:
            print(f"{test_name:>15s}: ❌ 未执行")
    
    print("-" * 70)
    print(f"总体结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！您的Spark环境完全正常！")
    elif passed_tests >= total_tests * 0.7:
        print("⚠️ 大部分测试通过，Spark环境基本可用")
    else:
        print("❌ 多项测试失败，Spark环境存在问题")
    
    print(f"\n详细结果已保存到: results/spark_complete_test_results.json")
    print("=" * 70)
    
    return all_results

if __name__ == "__main__":
    run_complete_test()
