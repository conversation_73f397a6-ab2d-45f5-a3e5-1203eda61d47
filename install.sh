#!/bin/bash

# 基于Spark的GLUE文本分类项目安装脚本

echo "=== 基于Spark的GLUE文本分类项目安装脚本 ==="
echo ""

# 检查Python版本
echo "检查Python版本..."
python_version=$(python3 --version 2>&1 | grep -oP '\d+\.\d+')
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" = "$required_version" ]; then
    echo "✓ Python版本检查通过: $python_version"
else
    echo "✗ Python版本过低，需要3.8+，当前版本: $python_version"
    exit 1
fi

# 检查Java环境
echo ""
echo "检查Java环境..."
if command -v java &> /dev/null; then
    java_version=$(java -version 2>&1 | head -n1 | cut -d'"' -f2)
    echo "✓ Java已安装: $java_version"
else
    echo "✗ Java未安装，请先安装Java 8+"
    echo "Ubuntu安装命令: sudo apt-get install openjdk-8-jdk"
    exit 1
fi

# 创建虚拟环境
echo ""
echo "创建Python虚拟环境..."
if [ ! -d "venv" ]; then
    python3 -m venv venv
    echo "✓ 虚拟环境创建成功"
else
    echo "✓ 虚拟环境已存在"
fi

# 激活虚拟环境
echo ""
echo "激活虚拟环境..."
source venv/bin/activate
echo "✓ 虚拟环境已激活"

# 升级pip
echo ""
echo "升级pip..."
pip install --upgrade pip
echo "✓ pip升级完成"

# 安装依赖
echo ""
echo "安装Python依赖包..."
pip install -r requirements.txt
echo "✓ 依赖包安装完成"

# 创建必要目录
echo ""
echo "创建项目目录..."
mkdir -p outputs
mkdir -p saved_models
mkdir -p results
mkdir -p logs
echo "✓ 项目目录创建完成"

# 检查GLUE数据集
echo ""
echo "检查GLUE数据集..."
if [ -d "glue" ]; then
    echo "✓ GLUE数据集目录存在"
    
    # 检查主要任务数据
    tasks=("SST-2" "CoLA" "MRPC" "RTE" "QNLI")
    for task in "${tasks[@]}"; do
        if [ -d "glue/$task" ]; then
            echo "  ✓ $task 数据存在"
        else
            echo "  ✗ $task 数据缺失"
        fi
    done
else
    echo "✗ GLUE数据集目录不存在"
    echo "请将GLUE数据集放置在项目根目录的glue/文件夹中"
fi

# 测试安装
echo ""
echo "测试安装..."
python -c "
import sys
sys.path.append('src')
try:
    from config import Config
    from src.data_processor import GLUEDataProcessor
    print('✓ 模块导入测试通过')
except ImportError as e:
    print(f'✗ 模块导入失败: {e}')
    sys.exit(1)
"

# 显示使用说明
echo ""
echo "=== 安装完成 ==="
echo ""
echo "使用说明："
echo "1. 激活虚拟环境: source venv/bin/activate"
echo "2. 运行示例: python example_usage.py"
echo "3. 训练模型: python main.py --mode train --task SST-2"
echo "4. 查看帮助: python main.py --help"
echo ""
echo "注意事项："
echo "- 确保GLUE数据集已正确放置在glue/目录中"
echo "- 首次运行会下载预训练模型，需要网络连接"
echo "- 建议使用GPU加速训练（如果可用）"
echo ""
echo "项目文档："
echo "- README.md: 项目说明"
echo "- 实验报告.md: 详细实验报告"
echo "- example_usage.py: 使用示例"
echo ""
echo "祝您使用愉快！"
