#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pandas GLUE启动脚本 - 完全避开Spark和TensorFlow问题
"""
import sys
import os
import argparse
from datetime import datetime

def print_header():
    """打印标题"""
    print("=" * 60)
    print("🚀 Pandas GLUE 文本分类训练系统")
    print("=" * 60)
    print("使用Pandas模拟Spark数据处理 + PyTorch深度学习")
    print("完全避开Spark和TensorFlow兼容性问题")
    print("支持SST-2情感分析和RTE文本蕴含任务")
    print("=" * 60)
    
    # 显示环境信息
    try:
        import torch
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"GPU设备: {torch.cuda.get_device_name(0)}")
            print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
        
        import pandas as pd
        print(f"Pandas版本: {pd.__version__}")
        
        import numpy as np
        print(f"NumPy版本: {np.__version__}")
        
        import sklearn
        print(f"Scikit-learn版本: {sklearn.__version__}")
        
    except ImportError as e:
        print(f"环境检查失败: {e}")
    
    print("=" * 60)

def run_single_task(task_name="SST-2", num_samples=1000):
    """运行单任务训练"""
    print(f"\n🎯 运行单任务训练: {task_name}")
    print(f"样本数量: {num_samples}")
    
    try:
        from pandas_glue_trainer import run_glue_training
        
        # 运行训练
        results = run_glue_training(task_name, num_samples)
        
        if results:
            print(f"\n✅ {task_name} 训练成功!")
            return results
        else:
            print(f"\n❌ {task_name} 训练失败!")
            return None
        
    except Exception as e:
        print(f"❌ 单任务训练失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_demo():
    """运行演示"""
    print("\n🎪 运行Pandas GLUE演示")
    
    tasks = [
        ("SST-2", 800, "情感分析"),
        ("RTE", 600, "文本蕴含")
    ]
    
    results_summary = {}
    
    for task_name, samples, description in tasks:
        print(f"\n--- 演示任务: {task_name} ({description}) ---")
        
        result = run_single_task(task_name, samples)
        
        if result:
            results_summary[task_name] = {
                "accuracy": result["accuracy"],
                "f1": result["f1"],
                "matthews_corr": result["matthews_corr"],
                "training_time": result["training_time"],
                "status": "success"
            }
            print(f"✅ {task_name} 演示成功")
        else:
            results_summary[task_name] = {"status": "failed"}
            print(f"❌ {task_name} 演示失败")
    
    # 保存演示结果
    import json
    os.makedirs("results", exist_ok=True)
    with open("results/pandas_demo_results.json", "w") as f:
        json.dump(results_summary, f, indent=2)
    
    # 显示总结
    print("\n" + "=" * 60)
    print("🎪 演示完成!")
    print("\n演示结果总结:")
    
    for task, result in results_summary.items():
        if result["status"] == "success":
            print(f"  {task:6s}: 准确率={result['accuracy']:.4f}, "
                  f"F1={result['f1']:.4f}, Matthews={result['matthews_corr']:.4f}, "
                  f"时间={result['training_time']:.1f}s")
        else:
            print(f"  {task:6s}: 失败")
    
    print(f"\n详细结果保存在: results/pandas_demo_results.json")

def run_benchmark():
    """运行基准测试"""
    print("\n🏆 运行Pandas GLUE基准测试")
    
    tasks = [
        ("SST-2", 2000, "情感分析"),
        ("RTE", 1500, "文本蕴含")
    ]
    
    results_summary = {}
    
    for task_name, samples, description in tasks:
        print(f"\n--- 基准测试: {task_name} ({description}) ---")
        
        result = run_single_task(task_name, samples)
        
        if result:
            results_summary[task_name] = {
                "accuracy": result["accuracy"],
                "f1": result["f1"],
                "matthews_corr": result["matthews_corr"],
                "training_time": result["training_time"],
                "samples": samples,
                "status": "success"
            }
        else:
            results_summary[task_name] = {"status": "failed"}
    
    # 保存基准测试结果
    import json
    os.makedirs("results", exist_ok=True)
    with open("results/pandas_benchmark_results.json", "w") as f:
        json.dump(results_summary, f, indent=2)
    
    # 显示总结
    print("\n" + "=" * 60)
    print("🏆 基准测试完成!")
    print("\n基准测试结果:")
    
    for task, result in results_summary.items():
        if result["status"] == "success":
            print(f"  {task:6s}: 准确率={result['accuracy']:.4f}, "
                  f"F1={result['f1']:.4f}, Matthews={result['matthews_corr']:.4f}, "
                  f"时间={result['training_time']:.1f}s, 样本={result['samples']}")
        else:
            print(f"  {task:6s}: 失败")
    
    print(f"\n详细结果保存在: results/pandas_benchmark_results.json")

def run_comparison():
    """运行对比测试"""
    print("\n📊 运行对比测试")
    print("对比不同样本数量的性能表现")
    
    task_name = "SST-2"
    sample_sizes = [500, 1000, 1500, 2000]
    
    comparison_results = {}
    
    for samples in sample_sizes:
        print(f"\n--- 测试样本数: {samples} ---")
        
        result = run_single_task(task_name, samples)
        
        if result:
            comparison_results[str(samples)] = {
                "accuracy": result["accuracy"],
                "f1": result["f1"],
                "training_time": result["training_time"],
                "samples": samples
            }
        else:
            comparison_results[str(samples)] = {"status": "failed"}
    
    # 保存对比结果
    import json
    os.makedirs("results", exist_ok=True)
    with open("results/pandas_comparison_results.json", "w") as f:
        json.dump(comparison_results, f, indent=2)
    
    # 显示对比结果
    print("\n" + "=" * 60)
    print("📊 对比测试完成!")
    print("\n性能对比结果:")
    print(f"{'样本数':>8s} {'准确率':>8s} {'F1分数':>8s} {'训练时间':>10s}")
    print("-" * 40)
    
    for samples, result in comparison_results.items():
        if "accuracy" in result:
            print(f"{samples:>8s} {result['accuracy']:>8.4f} {result['f1']:>8.4f} {result['training_time']:>8.1f}s")
        else:
            print(f"{samples:>8s} {'失败':>8s}")
    
    print(f"\n详细结果保存在: results/pandas_comparison_results.json")

def show_results():
    """显示历史结果"""
    print("\n📊 查看训练结果")
    
    results_dir = "results"
    if not os.path.exists(results_dir):
        print("⚠️ 结果目录不存在")
        return
    
    result_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
    
    if not result_files:
        print("⚠️ 没有找到结果文件")
        return
    
    print(f"📁 找到 {len(result_files)} 个结果文件:")
    
    for file in sorted(result_files):
        file_path = os.path.join(results_dir, file)
        try:
            import json
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            print(f"\n📄 {file}:")
            
            if "task_name" in data:
                # 单任务结果
                print(f"   任务: {data['task_name']}")
                print(f"   准确率: {data.get('accuracy', 'N/A'):.4f}")
                print(f"   F1分数: {data.get('f1', 'N/A'):.4f}")
                print(f"   Matthews: {data.get('matthews_corr', 'N/A'):.4f}")
                print(f"   训练时间: {data.get('training_time', 'N/A'):.1f}秒")
                print(f"   设备: {data.get('device', 'N/A')}")
            else:
                # 多任务结果
                for task, result in data.items():
                    if isinstance(result, dict) and "accuracy" in result:
                        print(f"   {task}: 准确率={result['accuracy']:.4f}, F1={result.get('f1', 0):.4f}")
            
        except Exception as e:
            print(f"   ❌ 读取失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Pandas GLUE文本分类训练系统")
    parser.add_argument("--mode", choices=["single", "demo", "benchmark", "comparison", "results"], 
                       default="demo", help="运行模式")
    parser.add_argument("--task", default="SST-2", 
                       help="单任务模式的任务名称 (SST-2 或 RTE)")
    parser.add_argument("--samples", type=int, default=1000, 
                       help="样本数量")
    
    args = parser.parse_args()
    
    # 打印标题
    print_header()
    
    # 记录开始时间
    start_time = datetime.now()
    
    try:
        if args.mode == "single":
            run_single_task(args.task, args.samples)
        
        elif args.mode == "demo":
            run_demo()
        
        elif args.mode == "benchmark":
            run_benchmark()
        
        elif args.mode == "comparison":
            run_comparison()
        
        elif args.mode == "results":
            show_results()
        
        else:
            print("❌ 未知的运行模式")
            return
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断训练")
    
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 显示总用时
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        print(f"\n⏱️ 总用时: {total_time:.1f}秒")
        print("=" * 60)

if __name__ == "__main__":
    main()
