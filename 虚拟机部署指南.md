# 虚拟机部署指南

## 1. 准备工作

### 1.1 确认虚拟机环境
确保您的Ubuntu虚拟机已经安装了以下组件：
- Ubuntu 20.04+
- Hadoop (按照您提供的教程安装)
- Spark (按照您提供的教程安装)
- Anaconda3 (按照您提供的教程安装)
- PyCharm (按照您提供的教程安装)

### 1.2 检查环境变量
在虚拟机中运行以下命令检查环境：
```bash
# 检查Java
java -version

# 检查Hadoop
hadoop version

# 检查Spark
spark-submit --version

# 检查Python
python3 --version

# 检查conda
conda --version
```

## 2. 文件传输方法

### 方法1：使用SCP传输（推荐）
如果虚拟机开启了SSH服务：

1. **在Windows主机上打包项目**：
```bash
# 在项目目录下
tar -czf spark文本分类.tar.gz .
```

2. **传输到虚拟机**：
```bash
scp spark文本分类.tar.gz username@虚拟机IP:/home/<USER>/
```

3. **在虚拟机中解压**：
```bash
cd /home/<USER>/
tar -xzf spark文本分类.tar.gz
cd spark文本分类
```

### 方法2：使用共享文件夹
1. **设置VirtualBox共享文件夹**：
   - VirtualBox菜单 → 设备 → 共享文件夹
   - 添加共享文件夹，选择Windows上的项目目录
   - 设置为自动挂载

2. **在虚拟机中访问**：
```bash
# 挂载共享文件夹（如果未自动挂载）
sudo mount -t vboxsf 共享文件夹名称 /mnt/shared

# 复制项目到本地
cp -r /mnt/shared/spark文本分类 /home/<USER>/
cd /home/<USER>/spark文本分类
```

### 方法3：使用Git（如果有网络）
1. **将代码上传到GitHub**
2. **在虚拟机中克隆**：
```bash
git clone https://github.com/yourusername/spark文本分类.git
cd spark文本分类
```

### 方法4：使用U盘或光盘镜像
1. **创建ISO镜像**：
   - 将项目文件夹打包成ISO镜像
   - 在VirtualBox中挂载ISO

2. **在虚拟机中挂载**：
```bash
sudo mount /dev/cdrom /mnt/cdrom
cp -r /mnt/cdrom/spark文本分类 /home/<USER>/
```

## 3. 虚拟机环境配置

### 3.1 创建项目目录
```bash
cd /home/<USER>
mkdir -p workspace
cd workspace
# 将项目文件放在这里
```

### 3.2 设置Python环境
```bash
# 使用conda创建虚拟环境
conda create -n spark_glue python=3.8
conda activate spark_glue

# 或使用venv
python3 -m venv venv
source venv/bin/activate
```

### 3.3 安装依赖
```bash
# 进入项目目录
cd spark文本分类

# 安装依赖
pip install -r requirements.txt

# 如果网络较慢，使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

## 4. 配置Spark环境

### 4.1 设置环境变量
在 `~/.bashrc` 中添加：
```bash
# Spark配置
export SPARK_HOME=/opt/spark  # 根据实际安装路径调整
export PATH=$SPARK_HOME/bin:$PATH
export PYSPARK_PYTHON=python3
export PYSPARK_DRIVER_PYTHON=python3

# Java配置
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64  # 根据实际路径调整

# Hadoop配置（如果需要）
export HADOOP_HOME=/opt/hadoop  # 根据实际安装路径调整
export HADOOP_CONF_DIR=$HADOOP_HOME/etc/hadoop
```

重新加载配置：
```bash
source ~/.bashrc
```

### 4.2 测试Spark
```bash
# 测试Spark
spark-shell --version

# 测试PySpark
python3 -c "from pyspark.sql import SparkSession; print('PySpark工作正常')"
```

## 5. 数据准备

### 5.1 GLUE数据集
确保GLUE数据集已正确放置：
```bash
# 检查数据集结构
ls -la glue/
ls -la glue/SST-2/
ls -la glue/CoLA/
```

### 5.2 创建必要目录
```bash
mkdir -p outputs saved_models results logs
```

## 6. 运行测试

### 6.1 基础测试
```bash
# 激活环境
conda activate spark_glue
# 或
source venv/bin/activate

# 运行安装测试
python test_installation.py
```

### 6.2 快速功能测试
```bash
# 运行示例
python example_usage.py
```

### 6.3 单任务训练测试
```bash
# 训练一个小任务进行测试
python main.py --mode train --task RTE
```

## 7. 常见问题解决

### 7.1 内存不足
如果虚拟机内存不足，修改配置：
```python
# 在config.py中调整
BATCH_SIZE = 8  # 减小批次大小
SPARK_MEMORY = "2g"  # 减小Spark内存
```

### 7.2 Spark启动失败
```bash
# 检查Java环境
echo $JAVA_HOME
java -version

# 检查Spark配置
echo $SPARK_HOME
ls $SPARK_HOME/bin/

# 重启Spark
$SPARK_HOME/sbin/stop-all.sh
$SPARK_HOME/sbin/start-all.sh
```

### 7.3 网络问题
如果下载模型失败：
```bash
# 设置代理（如果有）
export http_proxy=http://proxy:port
export https_proxy=http://proxy:port

# 或使用离线模式（需要预先下载模型）
```

### 7.4 权限问题
```bash
# 给脚本执行权限
chmod +x install.sh
chmod +x *.py

# 如果需要sudo权限
sudo chown -R $USER:$USER /path/to/project
```

## 8. 性能优化建议

### 8.1 虚拟机配置
- **CPU**: 分配至少2-4个核心
- **内存**: 分配至少8GB RAM
- **存储**: 使用SSD，分配至少50GB空间

### 8.2 Spark配置优化
```bash
# 在spark-defaults.conf中添加
spark.executor.memory 2g
spark.driver.memory 2g
spark.executor.cores 2
spark.sql.adaptive.enabled true
spark.sql.adaptive.coalescePartitions.enabled true
```

### 8.3 Python优化
```bash
# 使用更快的BLAS库
conda install mkl mkl-service

# 设置线程数
export OMP_NUM_THREADS=4
export MKL_NUM_THREADS=4
```

## 9. 监控和调试

### 9.1 查看日志
```bash
# 查看应用日志
tail -f logs/*.log

# 查看Spark日志
tail -f $SPARK_HOME/logs/*.log
```

### 9.2 监控资源使用
```bash
# 监控CPU和内存
htop

# 监控磁盘使用
df -h

# 监控网络
iftop
```

### 9.3 Spark Web UI
访问 http://localhost:4040 查看Spark作业状态

## 10. 完整部署脚本

创建一个自动化部署脚本 `deploy_to_vm.sh`：

```bash
#!/bin/bash
echo "开始虚拟机部署..."

# 1. 检查环境
echo "检查环境..."
java -version || { echo "Java未安装"; exit 1; }
python3 --version || { echo "Python3未安装"; exit 1; }

# 2. 创建虚拟环境
echo "创建Python环境..."
python3 -m venv venv
source venv/bin/activate

# 3. 安装依赖
echo "安装依赖..."
pip install -r requirements.txt

# 4. 创建目录
echo "创建目录..."
mkdir -p outputs saved_models results logs

# 5. 测试安装
echo "测试安装..."
python test_installation.py

echo "部署完成！"
```

使用方法：
```bash
chmod +x deploy_to_vm.sh
./deploy_to_vm.sh
```
