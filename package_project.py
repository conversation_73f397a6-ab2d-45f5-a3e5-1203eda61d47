"""
项目打包脚本
将项目打包成便于传输到虚拟机的格式
"""
import os
import shutil
import zipfile
import tarfile
from datetime import datetime
import json

def create_project_info():
    """创建项目信息文件"""
    info = {
        "project_name": "Spark GLUE文本分类项目",
        "version": "1.0.0",
        "created_date": datetime.now().isoformat(),
        "description": "基于Apache Spark和大语言模型的GLUE文本分类系统",
        "requirements": {
            "python": ">=3.8",
            "java": ">=8",
            "spark": ">=3.4.0",
            "memory": ">=8GB",
            "storage": ">=50GB"
        },
        "included_files": [
            "源代码文件",
            "配置文件",
            "文档",
            "安装脚本",
            "测试脚本"
        ],
        "not_included": [
            "GLUE数据集 (需要单独下载)",
            "预训练模型 (首次运行时自动下载)",
            "训练输出文件"
        ],
        "installation_steps": [
            "1. 解压项目文件到虚拟机",
            "2. 运行 chmod +x deploy_to_vm.sh",
            "3. 运行 ./deploy_to_vm.sh",
            "4. 按照提示完成安装",
            "5. 运行 python test_installation.py 验证安装"
        ]
    }
    
    with open("PROJECT_INFO.json", "w", encoding="utf-8") as f:
        json.dump(info, f, indent=2, ensure_ascii=False)
    
    print("✓ 创建项目信息文件: PROJECT_INFO.json")

def get_files_to_package():
    """获取需要打包的文件列表"""
    files_to_include = [
        # 核心代码文件
        "config.py",
        "main.py",
        "example_usage.py",
        "test_installation.py",
        
        # 源代码目录
        "src/__init__.py",
        "src/data_processor.py",
        "src/model_manager.py",
        "src/trainer.py",
        "src/predictor.py",
        
        # 配置和依赖文件
        "requirements.txt",
        "setup.py",
        
        # 文档文件
        "README.md",
        "实验报告.md",
        "虚拟机部署指南.md",
        "LICENSE",
        
        # 脚本文件
        "install.sh",
        "deploy_to_vm.sh",
        
        # 项目信息
        "PROJECT_INFO.json",
        
        # Git配置
        ".gitignore"
    ]
    
    # 检查文件是否存在
    existing_files = []
    missing_files = []
    
    for file_path in files_to_include:
        if os.path.exists(file_path):
            existing_files.append(file_path)
        else:
            missing_files.append(file_path)
    
    if missing_files:
        print(f"⚠️  以下文件不存在，将跳过: {missing_files}")
    
    return existing_files

def create_zip_package(files_list, output_name):
    """创建ZIP压缩包"""
    zip_name = f"{output_name}.zip"
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in files_list:
            if os.path.isfile(file_path):
                zipf.write(file_path, file_path)
                print(f"  添加文件: {file_path}")
            elif os.path.isdir(file_path):
                for root, dirs, files in os.walk(file_path):
                    for file in files:
                        full_path = os.path.join(root, file)
                        arc_path = os.path.relpath(full_path)
                        zipf.write(full_path, arc_path)
                        print(f"  添加文件: {arc_path}")
    
    print(f"✓ 创建ZIP包: {zip_name}")
    return zip_name

def create_tar_package(files_list, output_name):
    """创建TAR.GZ压缩包"""
    tar_name = f"{output_name}.tar.gz"
    
    with tarfile.open(tar_name, 'w:gz') as tarf:
        for file_path in files_list:
            if os.path.exists(file_path):
                tarf.add(file_path, arcname=file_path)
                if os.path.isfile(file_path):
                    print(f"  添加文件: {file_path}")
                else:
                    print(f"  添加目录: {file_path}")
    
    print(f"✓ 创建TAR.GZ包: {tar_name}")
    return tar_name

def create_directory_package(files_list, output_name):
    """创建目录包"""
    dir_name = f"{output_name}_directory"
    
    # 删除已存在的目录
    if os.path.exists(dir_name):
        shutil.rmtree(dir_name)
    
    os.makedirs(dir_name)
    
    for file_path in files_list:
        if os.path.exists(file_path):
            dest_path = os.path.join(dir_name, file_path)
            dest_dir = os.path.dirname(dest_path)
            
            if dest_dir and not os.path.exists(dest_dir):
                os.makedirs(dest_dir)
            
            if os.path.isfile(file_path):
                shutil.copy2(file_path, dest_path)
                print(f"  复制文件: {file_path}")
            elif os.path.isdir(file_path):
                if os.path.exists(dest_path):
                    shutil.rmtree(dest_path)
                shutil.copytree(file_path, dest_path)
                print(f"  复制目录: {file_path}")
    
    print(f"✓ 创建目录包: {dir_name}")
    return dir_name

def create_readme_for_vm():
    """为虚拟机创建特殊的README"""
    vm_readme = """# 虚拟机部署说明

## 快速开始

1. **解压项目文件**
   ```bash
   # 如果是tar.gz格式
   tar -xzf spark文本分类项目.tar.gz
   cd spark文本分类项目
   
   # 如果是zip格式
   unzip spark文本分类项目.zip
   cd spark文本分类项目
   ```

2. **运行自动部署脚本**
   ```bash
   chmod +x deploy_to_vm.sh
   ./deploy_to_vm.sh
   ```

3. **验证安装**
   ```bash
   source venv/bin/activate
   python test_installation.py
   ```

4. **开始使用**
   ```bash
   # 运行示例
   python example_usage.py
   
   # 训练模型
   python main.py --mode train --task RTE
   ```

## 重要提醒

- 确保虚拟机已安装Java 8+和Spark
- 需要至少8GB内存和50GB存储空间
- GLUE数据集需要单独下载并放置在glue/目录中
- 首次运行会下载预训练模型，需要网络连接

## 获取GLUE数据集

访问 https://gluebenchmark.com/tasks 下载数据集，或使用以下命令：

```bash
# 下载GLUE数据集的脚本示例
wget https://dl.fbaipublicfiles.com/glue/data/glue_data.zip
unzip glue_data.zip
```

## 故障排除

如遇问题，请查看：
- 虚拟机部署指南.md
- README.md
- 实验报告.md

或运行诊断脚本：
```bash
python test_installation.py
```
"""
    
    with open("VM_README.md", "w", encoding="utf-8") as f:
        f.write(vm_readme)
    
    print("✓ 创建虚拟机专用README: VM_README.md")

def get_package_size(file_path):
    """获取文件大小"""
    size = os.path.getsize(file_path)
    
    # 转换为人类可读格式
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size < 1024.0:
            return f"{size:.1f} {unit}"
        size /= 1024.0
    return f"{size:.1f} TB"

def main():
    """主函数"""
    print("=" * 60)
    print("  Spark GLUE文本分类项目 - 打包工具")
    print("=" * 60)
    print()
    
    # 创建项目信息文件
    create_project_info()
    
    # 创建虚拟机专用README
    create_readme_for_vm()
    
    # 获取要打包的文件
    print("📦 准备打包文件...")
    files_to_package = get_files_to_package()
    
    # 添加新创建的文件
    files_to_package.extend(["PROJECT_INFO.json", "VM_README.md"])
    
    print(f"📋 共 {len(files_to_package)} 个文件/目录需要打包")
    print()
    
    # 生成包名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_name = f"spark文本分类项目_{timestamp}"
    
    packages_created = []
    
    # 创建不同格式的包
    print("🔄 开始打包...")
    print()
    
    # 1. 创建ZIP包（Windows友好）
    print("1. 创建ZIP压缩包...")
    zip_file = create_zip_package(files_to_package, base_name)
    packages_created.append((zip_file, get_package_size(zip_file)))
    print()
    
    # 2. 创建TAR.GZ包（Linux友好）
    print("2. 创建TAR.GZ压缩包...")
    tar_file = create_tar_package(files_to_package, base_name)
    packages_created.append((tar_file, get_package_size(tar_file)))
    print()
    
    # 3. 创建目录包（便于查看）
    print("3. 创建目录包...")
    dir_package = create_directory_package(files_to_package, base_name)
    dir_size = sum(os.path.getsize(os.path.join(dirpath, filename))
                   for dirpath, dirnames, filenames in os.walk(dir_package)
                   for filename in filenames)
    packages_created.append((dir_package, get_package_size(dir_size) if dir_size else "0 B"))
    print()
    
    # 清理临时文件
    temp_files = ["PROJECT_INFO.json", "VM_README.md"]
    for temp_file in temp_files:
        if os.path.exists(temp_file):
            os.remove(temp_file)
    
    # 输出结果
    print("=" * 60)
    print("🎉 打包完成！")
    print("=" * 60)
    print()
    print("📦 创建的包:")
    for package, size in packages_created:
        print(f"  • {package} ({size})")
    print()
    
    print("📋 传输到虚拟机的方法:")
    print("  1. SCP传输: scp 包名 user@vm_ip:/home/<USER>/")
    print("  2. 共享文件夹: 通过VirtualBox共享文件夹")
    print("  3. U盘传输: 复制到U盘后在虚拟机中挂载")
    print("  4. 网络传输: 上传到云盘或Git仓库")
    print()
    
    print("🚀 在虚拟机中的使用步骤:")
    print("  1. 解压项目包")
    print("  2. 运行: chmod +x deploy_to_vm.sh")
    print("  3. 运行: ./deploy_to_vm.sh")
    print("  4. 按照提示完成安装")
    print()
    
    print("📚 重要提醒:")
    print("  • GLUE数据集需要单独下载")
    print("  • 确保虚拟机有足够的内存和存储空间")
    print("  • 首次运行需要网络连接下载预训练模型")
    print()

if __name__ == "__main__":
    main()
