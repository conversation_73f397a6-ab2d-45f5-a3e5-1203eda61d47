{"CoLA": {"best_metric": 0.0, "total_time": 13.054965257644653, "training_history": [{"epoch": 1, "train_loss": 0.6485510151833296, "eval_metrics": {"accuracy": 0.65, "matthews_corrcoef": 0.0, "eval_loss": 0.6311878476824079}, "learning_rate": 0.0}]}, "SST-2": {"best_metric": 0.555, "total_time": 14.57837462425232, "training_history": [{"epoch": 1, "train_loss": 0.6779597382992506, "eval_metrics": {"accuracy": 0.555, "f1": 0.6898954703832753, "eval_loss": 0.6404805098261152}, "learning_rate": 0.0}]}, "MRPC": {"error": "num_samples should be a positive integer value, but got num_samples=0"}, "STS-B": {"error": "Found dtype Long but expected Float"}, "QQP": {"best_metric": 0.0, "total_time": 13.939993619918823, "training_history": [{"epoch": 1, "train_loss": 0.20740685472264886, "eval_metrics": {"accuracy": 1.0, "f1": 0.0, "eval_loss": 0.05721571615764073}, "learning_rate": 0.0}]}, "MNLI": {"best_metric": 0.36, "total_time": 15.627585411071777, "training_history": [{"epoch": 1, "train_loss": 1.158670000731945, "eval_metrics": {"accuracy": 0.36, "f1_macro": 0.27399235705530317, "eval_loss": 1.0950643505368913}, "learning_rate": 0.0}]}, "QNLI": {"error": "'NoneType' object cannot be interpreted as an integer"}, "RTE": {"best_metric": 0.535, "total_time": 17.507821559906006, "training_history": [{"epoch": 1, "train_loss": 0.6961432173848152, "eval_metrics": {"accuracy": 0.535, "f1": 0.34965034965034963, "eval_loss": 0.6860382216317313}, "learning_rate": 0.0}]}, "WNLI": {"best_metric": 0.4225352112676056, "total_time": 11.283204078674316, "training_history": [{"epoch": 1, "train_loss": 0.7325859069824219, "eval_metrics": {"accuracy": 0.4225352112676056, "f1": 0.22641509433962265, "eval_loss": 0.7011438806851705}, "learning_rate": 0.0}]}}