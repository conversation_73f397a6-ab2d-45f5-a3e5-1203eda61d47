#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Spark GLUE多任务训练器 - 支持多个GLUE任务的并行训练
"""
import os
import json
import pandas as pd
import torch
from concurrent.futures import ThreadPoolExecutor
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, when, lit, length, regexp_replace, concat
from pyspark.sql.types import StructType, StructField, StringType, IntegerType
from transformers import AutoTokenizer, AutoModelForSequenceClassification, Trainer, TrainingArguments
from torch.utils.data import Dataset
import numpy as np
from sklearn.metrics import accuracy_score, f1_score, matthews_corrcoef
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SparkGLUEMultiTaskProcessor:
    """Spark多任务GLUE数据处理器"""
    
    def __init__(self):
        self.spark = self._create_spark_session()
        
    def _create_spark_session(self):
        """创建优化的Spark会话"""
        logger.info("创建Spark会话...")
        
        spark = SparkSession.builder \
            .appName("GLUE_Multi_Task_Training") \
            .master("local[*]") \
            .config("spark.driver.memory", "6g") \
            .config("spark.executor.memory", "4g") \
            .config("spark.sql.adaptive.enabled", "true") \
            .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
            .config("spark.sql.adaptive.skewJoin.enabled", "true") \
            .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
            .config("spark.sql.execution.arrow.pyspark.enabled", "true") \
            .getOrCreate()
        
        spark.sparkContext.setLogLevel("WARN")
        logger.info(f"Spark会话创建成功: {spark.version}")
        return spark
    
    def create_task_data(self, task_name, num_samples=1000):
        """为指定任务创建数据"""
        logger.info(f"创建{task_name}任务数据...")
        
        if task_name == "SST-2":
            return self._create_sst2_data(num_samples)
        elif task_name == "RTE":
            return self._create_rte_data(num_samples)
        elif task_name == "CoLA":
            return self._create_cola_data(num_samples)
        elif task_name == "MRPC":
            return self._create_mrpc_data(num_samples)
        else:
            raise ValueError(f"不支持的任务: {task_name}")
    
    def _create_sst2_data(self, num_samples):
        """创建SST-2情感分析数据"""
        positive_templates = [
            "This movie is {} and {}!",
            "I {} this film, it's {}!",
            "{} performance and {} story!",
            "{} cinematography and {} acting!",
            "One of the {} movies I have ever seen!"
        ]
        
        negative_templates = [
            "This movie is {} and {}.",
            "I {} this film, it's {}.",
            "{} acting and {} storyline.",
            "{} direction and {} script.",
            "One of the {} films ever made."
        ]
        
        positive_words = ["fantastic", "amazing", "excellent", "brilliant", "outstanding", "wonderful", "incredible", "superb"]
        negative_words = ["terrible", "awful", "horrible", "boring", "disappointing", "bad", "poor", "worst"]
        
        data = []
        for i in range(num_samples):
            if i % 2 == 0:
                template = positive_templates[i % len(positive_templates)]
                words = np.random.choice(positive_words, 2, replace=False)
                sentence = template.format(words[0], words[1])
                label = 1
            else:
                template = negative_templates[i % len(negative_templates)]
                words = np.random.choice(negative_words, 2, replace=False)
                sentence = template.format(words[0], words[1])
                label = 0
            
            data.append((sentence, label))
        
        schema = StructType([
            StructField("sentence", StringType(), True),
            StructField("label", IntegerType(), True)
        ])
        
        return self.spark.createDataFrame(data, schema)
    
    def _create_rte_data(self, num_samples):
        """创建RTE文本蕴含数据"""
        entailment_pairs = [
            ("The cat is sleeping on the sofa.", "There is a cat on the furniture.", 1),
            ("John went to the grocery store.", "John is shopping for food.", 1),
            ("She is reading a science book.", "She is studying science.", 1),
            ("The dog is barking loudly.", "There is a dog making noise.", 1),
            ("The restaurant serves Italian food.", "You can eat pasta there.", 1),
            ("She works as a teacher.", "She educates students.", 1),
            ("The car is moving fast.", "The vehicle is in motion.", 1),
            ("He is playing the piano.", "He is making music.", 1)
        ]
        
        contradiction_pairs = [
            ("The weather is sunny today.", "It's raining outside.", 0),
            ("The car is painted red.", "The car is blue in color.", 0),
            ("Students are taking an exam.", "Students are playing games.", 0),
            ("The movie starts at 8 PM.", "The movie ends at 8 PM.", 0),
            ("The book is very thick.", "The book is very thin.", 0),
            ("The room is completely dark.", "The room is brightly lit.", 0),
            ("He is very tall.", "He is very short.", 0),
            ("The water is hot.", "The water is cold.", 0)
        ]
        
        all_pairs = entailment_pairs + contradiction_pairs
        
        data = []
        for i in range(num_samples):
            premise, hypothesis, label = all_pairs[i % len(all_pairs)]
            data.append((i, premise, hypothesis, label))
        
        schema = StructType([
            StructField("index", IntegerType(), True),
            StructField("sentence1", StringType(), True),
            StructField("sentence2", StringType(), True),
            StructField("label", IntegerType(), True)
        ])
        
        return self.spark.createDataFrame(data, schema)
    
    def _create_cola_data(self, num_samples):
        """创建CoLA语言可接受性数据"""
        acceptable_sentences = [
            "The cat sat on the mat.",
            "She gave him a book.",
            "They are going to the store.",
            "I think that he is coming.",
            "The dog chased the ball.",
            "We will meet at the park.",
            "She is reading a novel.",
            "He works in the office."
        ]
        
        unacceptable_sentences = [
            "Cat the sat mat on the.",
            "Gave she book a him.",
            "Going they store the to are.",
            "Think I he that coming is.",
            "Dog the ball the chased.",
            "Will we park the meet at.",
            "Reading she novel a is.",
            "Works he office the in."
        ]
        
        data = []
        for i in range(num_samples):
            if i % 2 == 0:
                sentence = acceptable_sentences[i % len(acceptable_sentences)]
                label = 1
            else:
                sentence = unacceptable_sentences[i % len(unacceptable_sentences)]
                label = 0
            
            data.append((sentence, label))
        
        schema = StructType([
            StructField("sentence", StringType(), True),
            StructField("label", IntegerType(), True)
        ])
        
        return self.spark.createDataFrame(data, schema)
    
    def _create_mrpc_data(self, num_samples):
        """创建MRPC句子对匹配数据"""
        paraphrase_pairs = [
            ("The cat is sleeping.", "The feline is resting.", 1),
            ("He is driving a car.", "He is operating a vehicle.", 1),
            ("She is cooking dinner.", "She is preparing the evening meal.", 1),
            ("The book is interesting.", "The novel is fascinating.", 1),
            ("They are playing soccer.", "They are playing football.", 1),
            ("The weather is nice.", "The climate is pleasant.", 1),
            ("He is studying hard.", "He is working diligently on his studies.", 1),
            ("The movie was exciting.", "The film was thrilling.", 1)
        ]
        
        non_paraphrase_pairs = [
            ("The cat is sleeping.", "The dog is barking.", 0),
            ("He is driving a car.", "She is reading a book.", 0),
            ("She is cooking dinner.", "They are watching TV.", 0),
            ("The book is interesting.", "The weather is cold.", 0),
            ("They are playing soccer.", "He is sleeping late.", 0),
            ("The weather is nice.", "The car is broken.", 0),
            ("He is studying hard.", "She is dancing gracefully.", 0),
            ("The movie was exciting.", "The food is delicious.", 0)
        ]
        
        all_pairs = paraphrase_pairs + non_paraphrase_pairs
        
        data = []
        for i in range(num_samples):
            sentence1, sentence2, label = all_pairs[i % len(all_pairs)]
            data.append((i, sentence1, sentence2, label))
        
        schema = StructType([
            StructField("index", IntegerType(), True),
            StructField("sentence1", StringType(), True),
            StructField("sentence2", StringType(), True),
            StructField("label", IntegerType(), True)
        ])
        
        return self.spark.createDataFrame(data, schema)
    
    def preprocess_task_data(self, df, task_name):
        """预处理任务数据"""
        logger.info(f"预处理{task_name}数据...")
        
        if task_name in ["SST-2", "CoLA"]:
            # 单句任务
            processed_df = df.select(
                regexp_replace(col("sentence"), "[^a-zA-Z0-9\\s]", "").alias("text"),
                col("label").alias("labels")
            ).filter(length(col("text")) > 3)
            
        elif task_name in ["RTE", "MRPC"]:
            # 句子对任务
            processed_df = df.select(
                col("index"),
                regexp_replace(
                    concat(col("sentence1"), lit(" [SEP] "), col("sentence2")),
                    "[^a-zA-Z0-9\\s\\[\\]]", ""
                ).alias("text"),
                col("label").alias("labels")
            ).filter(length(col("text")) > 10)
        
        # 添加统计信息
        processed_df = processed_df.withColumn("text_length", length(col("text")))
        
        return processed_df
    
    def get_data_statistics(self, df, task_name):
        """获取数据统计信息"""
        total_count = df.count()
        avg_length = df.agg({"text_length": "avg"}).collect()[0][0]
        label_dist = df.groupBy("labels").count().collect()
        
        stats = {
            "task": task_name,
            "total_samples": total_count,
            "avg_text_length": round(avg_length, 2),
            "label_distribution": {str(row["labels"]): row["count"] for row in label_dist}
        }
        
        logger.info(f"{task_name}统计: {stats}")
        return stats
    
    def close(self):
        """关闭Spark会话"""
        if self.spark:
            self.spark.stop()
            logger.info("Spark会话已关闭")

class GLUEMultiTaskTrainer:
    """多任务GLUE训练器"""
    
    def __init__(self, model_name="distilbert-base-uncased"):
        self.model_name = model_name
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        
    def train_single_task(self, task_name, train_df, dev_df, output_dir):
        """训练单个任务"""
        logger.info(f"开始训练{task_name}...")
        
        # 准备数据
        train_dataset = self._create_dataset(train_df)
        dev_dataset = self._create_dataset(dev_df)
        
        # 创建模型
        model = AutoModelForSequenceClassification.from_pretrained(
            self.model_name, num_labels=2
        ).to(self.device)
        
        # 训练参数
        training_args = TrainingArguments(
            output_dir=f"{output_dir}/{task_name}",
            num_train_epochs=2,
            per_device_train_batch_size=8,
            per_device_eval_batch_size=8,
            warmup_steps=50,
            weight_decay=0.01,
            learning_rate=2e-5,
            logging_steps=25,
            evaluation_strategy="epoch",
            save_strategy="epoch",
            load_best_model_at_end=True,
            report_to=None,
            fp16=True if self.device.type == 'cuda' else False,
        )
        
        # 创建训练器
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=dev_dataset,
            compute_metrics=self._compute_metrics,
        )
        
        # 训练
        start_time = datetime.now()
        train_result = trainer.train()
        end_time = datetime.now()
        
        # 评估
        eval_results = trainer.evaluate()
        
        # 保存模型
        trainer.save_model(f"{output_dir}/{task_name}/model")
        
        return {
            "task_name": task_name,
            "train_results": train_result.metrics,
            "eval_results": eval_results,
            "training_time": (end_time - start_time).total_seconds(),
            "model_path": f"{output_dir}/{task_name}/model"
        }
    
    def _create_dataset(self, df):
        """创建PyTorch数据集"""
        class GLUEDataset(Dataset):
            def __init__(self, texts, labels, tokenizer, max_length=128):
                self.texts = texts
                self.labels = labels
                self.tokenizer = tokenizer
                self.max_length = max_length
            
            def __len__(self):
                return len(self.texts)
            
            def __getitem__(self, idx):
                text = str(self.texts[idx])
                label = self.labels[idx]
                
                encoding = self.tokenizer(
                    text,
                    truncation=True,
                    padding='max_length',
                    max_length=self.max_length,
                    return_tensors='pt'
                )
                
                return {
                    'input_ids': encoding['input_ids'].flatten(),
                    'attention_mask': encoding['attention_mask'].flatten(),
                    'labels': torch.tensor(label, dtype=torch.long)
                }
        
        return GLUEDataset(
            df['text'].tolist(),
            df['labels'].tolist(),
            self.tokenizer
        )
    
    def _compute_metrics(self, eval_pred):
        """计算评估指标"""
        predictions, labels = eval_pred
        predictions = np.argmax(predictions, axis=1)
        
        return {
            "accuracy": accuracy_score(labels, predictions),
            "f1": f1_score(labels, predictions, average='weighted'),
            "matthews_corr": matthews_corrcoef(labels, predictions)
        }

def run_multi_task_training():
    """运行多任务训练"""
    print("=== Spark GLUE 多任务训练 ===")
    print("=" * 50)
    
    # 任务列表
    tasks = ["SST-2", "RTE", "CoLA", "MRPC"]
    
    # 初始化处理器
    processor = SparkGLUEMultiTaskProcessor()
    trainer = GLUEMultiTaskTrainer()
    
    all_results = {}
    
    try:
        for task in tasks:
            print(f"\n处理任务: {task}")
            
            # 创建数据
            df = processor.create_task_data(task, num_samples=800)
            
            # 预处理
            processed_df = processor.preprocess_task_data(df, task)
            
            # 获取统计信息
            stats = processor.get_data_statistics(processed_df, task)
            
            # 分割数据
            train_df, dev_df = processed_df.randomSplit([0.8, 0.2], seed=42)
            
            # 转换为Pandas
            train_pandas = train_df.toPandas()
            dev_pandas = dev_df.toPandas()
            
            # 训练模型
            result = trainer.train_single_task(
                task, train_pandas, dev_pandas, "results/multi_task"
            )
            
            result["data_stats"] = stats
            all_results[task] = result
            
            print(f"{task} 完成 - 准确率: {result['eval_results']['eval_accuracy']:.4f}")
    
    finally:
        processor.close()
    
    # 保存所有结果
    os.makedirs("results", exist_ok=True)
    with open("results/multi_task_results.json", "w") as f:
        json.dump(all_results, f, indent=2)
    
    # 显示总结
    print("\n" + "=" * 50)
    print("多任务训练完成！")
    print("\n任务结果总结:")
    
    for task, result in all_results.items():
        accuracy = result['eval_results']['eval_accuracy']
        f1 = result['eval_results']['eval_f1']
        time_taken = result['training_time']
        
        print(f"  {task:6s}: 准确率={accuracy:.4f}, F1={f1:.4f}, 时间={time_taken:.1f}s")
    
    return all_results

if __name__ == "__main__":
    run_multi_task_training()
