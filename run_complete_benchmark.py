#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的GLUE基准测试脚本
"""
import os
import json
import time
from datetime import datetime

def print_header():
    """打印标题"""
    print("=" * 70)
    print("🏆 完整GLUE基准测试")
    print("=" * 70)
    print("测试所有改进的GLUE任务，生成完整的性能报告")
    print("包括：SST-2情感分析、RTE文本蕴含")
    print("=" * 70)

def run_single_benchmark(task_name, num_samples, description):
    """运行单个基准测试"""
    print(f"\n🎯 基准测试: {task_name} ({description})")
    print(f"样本数量: {num_samples}")
    print("-" * 50)
    
    try:
        from improved_glue_trainer import run_improved_training
        
        start_time = time.time()
        result = run_improved_training(task_name, num_samples)
        end_time = time.time()
        
        if result:
            print(f"✅ {task_name} 基准测试成功!")
            print(f"   准确率: {result['accuracy']:.4f}")
            print(f"   F1分数: {result['f1']:.4f}")
            print(f"   Matthews: {result['matthews_corr']:.4f}")
            print(f"   训练时间: {result['training_time']:.1f}秒")
            print(f"   训练轮数: {result['epochs_trained']}")
            
            return {
                "task": task_name,
                "description": description,
                "status": "success",
                "accuracy": result['accuracy'],
                "f1": result['f1'],
                "matthews_corr": result['matthews_corr'],
                "training_time": result['training_time'],
                "epochs_trained": result['epochs_trained'],
                "num_samples": num_samples,
                "train_samples": result['train_samples'],
                "dev_samples": result['dev_samples']
            }
        else:
            print(f"❌ {task_name} 基准测试失败!")
            return {
                "task": task_name,
                "description": description,
                "status": "failed"
            }
            
    except Exception as e:
        print(f"❌ {task_name} 基准测试出错: {e}")
        return {
            "task": task_name,
            "description": description,
            "status": "error",
            "error": str(e)
        }

def test_spark_status():
    """测试Spark状态"""
    print("\n🔍 Spark环境状态检查")
    print("-" * 50)
    
    try:
        from pyspark.sql import SparkSession
        
        print("尝试创建Spark会话...")
        spark = SparkSession.builder \
            .appName("BenchmarkSparkTest") \
            .master("local[2]") \
            .config("spark.driver.memory", "1g") \
            .getOrCreate()
        
        print(f"✅ Spark会话创建成功!")
        print(f"   Spark版本: {spark.version}")
        print(f"   应用名称: {spark.sparkContext.appName}")
        
        # 简单测试
        data = [("test", 1), ("data", 2)]
        df = spark.createDataFrame(data, ["text", "value"])
        count = df.count()
        
        print(f"✅ DataFrame测试成功，行数: {count}")
        
        spark.stop()
        print("✅ Spark会话正常关闭")
        
        return {
            "spark_available": True,
            "spark_version": spark.version,
            "test_successful": True
        }
        
    except Exception as e:
        print(f"❌ Spark测试失败: {e}")
        return {
            "spark_available": False,
            "error": str(e),
            "test_successful": False
        }

def generate_performance_report(results, spark_status):
    """生成性能报告"""
    print("\n" + "=" * 70)
    print("📊 基准测试性能报告")
    print("=" * 70)
    
    # 环境信息
    try:
        import torch
        print(f"🖥️  硬件环境:")
        print(f"   PyTorch版本: {torch.__version__}")
        print(f"   CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"   GPU: {torch.cuda.get_device_name(0)}")
            print(f"   GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
        
        import pandas as pd
        print(f"   Pandas版本: {pd.__version__}")
        
        if spark_status["spark_available"]:
            print(f"   Spark版本: {spark_status['spark_version']} ✅")
        else:
            print(f"   Spark状态: 不可用 ❌")
            
    except Exception as e:
        print(f"环境信息获取失败: {e}")
    
    print(f"\n📈 任务性能结果:")
    print(f"{'任务':>8s} {'状态':>8s} {'准确率':>8s} {'F1分数':>8s} {'Matthews':>10s} {'时间(s)':>8s} {'轮数':>6s}")
    print("-" * 70)
    
    successful_tasks = 0
    total_tasks = len(results)
    total_time = 0
    
    for result in results:
        task = result["task"]
        if result["status"] == "success":
            accuracy = result["accuracy"]
            f1 = result["f1"]
            matthews = result["matthews_corr"]
            time_taken = result["training_time"]
            epochs = result["epochs_trained"]
            
            print(f"{task:>8s} {'✅':>8s} {accuracy:>8.4f} {f1:>8.4f} {matthews:>10.4f} {time_taken:>8.1f} {epochs:>6d}")
            
            successful_tasks += 1
            total_time += time_taken
        else:
            print(f"{task:>8s} {'❌':>8s} {'N/A':>8s} {'N/A':>8s} {'N/A':>10s} {'N/A':>8s} {'N/A':>6s}")
    
    print("-" * 70)
    print(f"📊 总体统计:")
    print(f"   成功任务: {successful_tasks}/{total_tasks}")
    print(f"   总训练时间: {total_time:.1f}秒")
    print(f"   平均每任务: {total_time/max(successful_tasks, 1):.1f}秒")
    
    if successful_tasks == total_tasks:
        print(f"🎉 所有任务完美完成！")
    elif successful_tasks >= total_tasks * 0.8:
        print(f"✅ 大部分任务成功完成")
    else:
        print(f"⚠️ 部分任务需要改进")

def run_complete_benchmark():
    """运行完整基准测试"""
    print_header()
    
    # 检查环境
    try:
        import torch
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"GPU: {torch.cuda.get_device_name(0)}")
    except ImportError:
        print("⚠️ PyTorch未安装")
    
    # 测试Spark状态
    spark_status = test_spark_status()
    
    # 定义基准测试任务
    benchmark_tasks = [
        ("SST-2", 2500, "情感分析 - 大规模"),
        ("RTE", 2000, "文本蕴含 - 大规模"),
        ("SST-2", 5000, "情感分析 - 超大规模"),
        ("RTE", 3000, "文本蕴含 - 超大规模")
    ]
    
    print(f"\n🚀 开始基准测试，共{len(benchmark_tasks)}个任务")
    
    all_results = []
    start_time = datetime.now()
    
    for i, (task_name, num_samples, description) in enumerate(benchmark_tasks, 1):
        print(f"\n{'='*20} 任务 {i}/{len(benchmark_tasks)} {'='*20}")
        
        result = run_single_benchmark(task_name, num_samples, description)
        all_results.append(result)
        
        # 短暂休息，让GPU降温
        if i < len(benchmark_tasks):
            print("⏸️ 短暂休息5秒...")
            time.sleep(5)
    
    end_time = datetime.now()
    total_benchmark_time = (end_time - start_time).total_seconds()
    
    # 生成报告
    generate_performance_report(all_results, spark_status)
    
    # 保存详细结果
    benchmark_report = {
        "timestamp": datetime.now().isoformat(),
        "total_benchmark_time": total_benchmark_time,
        "spark_status": spark_status,
        "tasks": all_results,
        "summary": {
            "total_tasks": len(all_results),
            "successful_tasks": sum(1 for r in all_results if r["status"] == "success"),
            "failed_tasks": sum(1 for r in all_results if r["status"] != "success")
        }
    }
    
    os.makedirs("results", exist_ok=True)
    with open("results/complete_benchmark_report.json", "w") as f:
        json.dump(benchmark_report, f, indent=2)
    
    print(f"\n📁 详细报告已保存: results/complete_benchmark_report.json")
    print(f"⏱️ 总基准测试时间: {total_benchmark_time:.1f}秒")
    print("=" * 70)

def run_quick_benchmark():
    """运行快速基准测试"""
    print("🚀 快速基准测试")
    print("=" * 50)
    
    quick_tasks = [
        ("SST-2", 1000, "情感分析 - 快速"),
        ("RTE", 800, "文本蕴含 - 快速")
    ]
    
    results = []
    for task_name, num_samples, description in quick_tasks:
        print(f"\n--- {task_name} ({description}) ---")
        result = run_single_benchmark(task_name, num_samples, description)
        results.append(result)
    
    # 简单报告
    print(f"\n📊 快速测试结果:")
    for result in results:
        if result["status"] == "success":
            print(f"  {result['task']:6s}: 准确率={result['accuracy']:.4f}, "
                  f"F1={result['f1']:.4f}, 时间={result['training_time']:.1f}s")
        else:
            print(f"  {result['task']:6s}: 失败")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "quick":
        run_quick_benchmark()
    else:
        run_complete_benchmark()

if __name__ == "__main__":
    main()
