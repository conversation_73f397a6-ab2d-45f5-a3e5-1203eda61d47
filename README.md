# GLUE基准测试系统

基于Apache Spark和BERT的大规模文本分类系统，支持完整的GLUE基准测试。

## 🎯 项目概述

本项目实现了一个完整的GLUE（General Language Understanding Evaluation）基准测试系统，采用以下技术栈：

- **深度学习模型**: BERT-base-uncased (1.1亿参数)
- **分布式计算**: Apache Spark 3.5.0
- **深度学习框架**: PyTorch 2.7.1
- **预训练模型**: HuggingFace Transformers
- **编程语言**: Python 3.10

## 🏆 支持的GLUE任务

| 任务 | 类型 | 描述 | 评估指标 |
|------|------|------|----------|
| CoLA | 分类 | 语言可接受性判断 | Matthews相关系数 |
| SST-2 | 分类 | 情感分析 | 准确率 |
| MRPC | 分类 | 释义识别 | F1分数 |
| STS-B | 回归 | 语义相似度 | Pearson/Spearman相关系数 |
| QQP | 分类 | 问题对等价性 | F1分数 |
| MNLI | 分类 | 自然语言推理 | 准确率 |
| QNLI | 分类 | 问答自然语言推理 | 准确率 |
| RTE | 分类 | 文本蕴含识别 | 准确率 |
| WNLI | 分类 | Winograd自然语言推理 | 准确率 |

## 🚀 快速开始

### 环境要求

- Python 3.8+
- CUDA 11.8+ (可选，用于GPU加速)
- 8GB+ RAM
- 10GB+ 磁盘空间

### 安装依赖

```bash
pip install -r requirements.txt
```

### 数据准备

1. 下载GLUE数据集到 `glue/` 目录
2. 确保数据目录结构如下：

```
glue/
├── CoLA/
│   ├── train.tsv
│   ├── dev.tsv
│   └── test.tsv
├── SST-2/
│   ├── train.tsv
│   ├── dev.tsv
│   └── test.tsv
└── ...
```

### 基本使用

#### 1. 训练单个任务

```bash
# 训练CoLA任务
python main.py --task CoLA --mode train --epochs 3

# 快速测试（少量数据）
python main.py --task CoLA --mode train --epochs 1 --max-samples 1000
```

#### 2. 预测单个任务

```bash
# 使用预训练模型预测
python main.py --task CoLA --mode predict

# 使用已训练模型预测
python main.py --task CoLA --mode predict --model-path models/CoLA/best_model_hf
```

#### 3. 训练所有任务

```bash
# 完整训练（需要较长时间）
python main.py --task all --mode train --epochs 3

# 快速测试所有任务
python main.py --task all --mode train --epochs 1 --max-samples 500
```

#### 4. 预测所有任务

```bash
python main.py --task all --mode predict
```

### 运行示例

```bash
# 运行使用示例
python example_usage.py
```

## 📊 性能表现

### 训练性能
- **单任务训练时间**: 1-30分钟（取决于数据集大小）
- **内存使用**: 4-8GB GPU内存
- **吞吐量**: 268+ 句子/秒

### 模型性能
- **模型大小**: 417.6 MB
- **参数数量**: 109,482,240
- **推理速度**: 毫秒级单句推理

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据处理模块   │    │   模型管理模块   │    │   训练器模块    │
│                │    │                │    │                │
│ • Spark集成    │    │ • BERT加载     │    │ • 分布式训练    │
│ • 数据清洗     │────▶│ • 分词器管理    │────▶│ • 优化器配置    │
│ • 格式转换     │    │ • 模型配置     │    │ • 评估指标     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       ▼
         │              ┌─────────────────┐    ┌─────────────────┐
         │              │   预测器模块    │    │   主程序模块    │
         │              │                │    │                │
         └──────────────▶│ • 批量预测     │◀───│ • 命令行接口    │
                        │ • 结果生成     │    │ • 工作流管理    │
                        │ • GLUE格式     │    │ • 错误处理     │
                        └─────────────────┘    └─────────────────┘
```

## 📁 项目结构

```
spark文本分类/
├── src/                    # 源代码
│   ├── __init__.py
│   ├── data_processor.py   # 数据处理模块
│   ├── model_manager.py    # 模型管理模块
│   ├── trainer.py          # 训练器模块
│   ├── predictor.py        # 预测器模块
│   └── utils.py           # 工具函数
├── glue/                  # GLUE数据集
├── models/                # 训练好的模型
├── results/               # 预测结果
├── logs/                  # 日志文件
├── main.py               # 主程序
├── example_usage.py      # 使用示例
├── config.py             # 配置文件
├── requirements.txt      # 依赖包
└── README.md            # 说明文档
```

## ⚙️ 配置说明

主要配置项在 `config.py` 中：

```python
# 模型配置
MODEL_NAME = "bert-base-uncased"
MAX_SEQ_LENGTH = 128

# 训练配置
BATCH_SIZE = 32
LEARNING_RATE = 2e-5
EPOCHS = 3

# Spark配置
SPARK_APP_NAME = "GLUE-Text-Classification"
SPARK_MASTER = "local[*]"
```

## 🔧 高级用法

### 自定义训练参数

```bash
python main.py --task CoLA --mode train \
    --epochs 5 \
    --learning-rate 3e-5 \
    --batch-size 16 \
    --max-samples 5000
```

### 分布式训练

```bash
python main.py --task all --mode train \
    --spark-master spark://master:7077 \
    --epochs 3
```

### 使用自定义模型

```python
from src.model_manager import GLUEModelManager
from src.predictor import GLUEPredictor

# 加载自定义模型
model_manager = GLUEModelManager("your-model-name")
model_manager.load_tokenizer()
model_manager.load_model_for_task("CoLA")

# 创建预测器
predictor = GLUEPredictor(model_manager, "CoLA")

# 预测
result = predictor.predict_single("Your test sentence.")
```

## 📈 提交到GLUE官网

1. 运行预测生成提交文件：
```bash
python main.py --task all --mode predict
```

2. 检查生成的提交文件在 `results/` 目录

3. 访问 [GLUE官网](https://gluebenchmark.com/) 提交结果

4. 查看排名和性能分析

## 🧪 测试

运行完整测试套件：

```bash
# 数据处理测试
python test_data_processor.py

# 模型管理测试
python test_model_minimal.py

# 训练预测测试
python test_trainer_predictor.py

# 端到端测试
python test_end_to_end.py
```

## 📝 开发说明

### 添加新任务

1. 在 `config.py` 中添加任务配置
2. 在 `data_processor.py` 中添加数据处理逻辑
3. 测试新任务的训练和预测

### 性能优化

- 调整批次大小以平衡速度和内存使用
- 使用混合精度训练（FP16）
- 增加Spark executor数量进行分布式处理

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

MIT License

## 👥 作者

GLUE Text Classification Team

## 🙏 致谢

- HuggingFace Transformers
- Apache Spark
- GLUE Benchmark
- PyTorch Team
