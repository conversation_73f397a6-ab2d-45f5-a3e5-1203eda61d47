# 基于Spark的GLUE文本分类项目

## 项目简介

本项目实现了基于Apache Spark和大语言模型的GLUE（General Language Understanding Evaluation）文本分类系统。项目集成了Hugging Face Transformers库，使用DistilBERT等预训练模型进行文本分类任务，并利用Spark的分布式计算能力处理大规模数据。

## 主要特性

- 🚀 **分布式处理**: 基于Apache Spark的大规模数据处理
- 🤖 **大语言模型**: 集成Hugging Face Transformers预训练模型
- 📊 **完整流水线**: 从数据预处理到模型训练、评估、预测的完整流程
- 🎯 **GLUE基准**: 支持GLUE基准数据集的9个文本分类任务
- 📈 **性能监控**: 详细的训练日志和性能指标
- 🔧 **易于配置**: 灵活的配置系统，支持不同模型和参数

## 支持的任务

| 任务 | 类型 | 描述 | 评估指标 |
|------|------|------|----------|
| SST-2 | 情感分析 | 斯坦福情感树库二分类 | Accuracy |
| CoLA | 语法判断 | 语言可接受性语料库 | Matthews Correlation |
| MRPC | 释义检测 | 微软研究释义语料库 | F1/Accuracy |
| RTE | 文本蕴含 | 识别文本蕴含 | Accuracy |
| QNLI | 问答推理 | 问题自然语言推理 | Accuracy |
| QQP | 问题相似性 | Quora问题对 | F1/Accuracy |
| MNLI | 自然语言推理 | 多体裁自然语言推理 | Accuracy |
| STS-B | 语义相似性 | 语义文本相似性基准 | Pearson Correlation |
| WNLI | 自然语言推理 | Winograd自然语言推理 | Accuracy |

## 环境要求

### 系统要求
- Ubuntu 20.04+ (推荐)
- Python 3.8+
- Java 8+ (Spark依赖)
- 8GB+ RAM (推荐16GB)
- 50GB+ 可用存储空间

### 软件依赖
- Apache Spark 3.4.0+
- PyTorch 2.0.0+
- Transformers 4.30.0+
- PySpark 3.4.0+

## 安装指南

### 1. 克隆项目
```bash
git clone <repository-url>
cd spark文本分类
```

### 2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 配置Spark环境
确保已安装Java和Spark，并设置环境变量：
```bash
export JAVA_HOME=/path/to/java
export SPARK_HOME=/path/to/spark
export PATH=$SPARK_HOME/bin:$PATH
```

### 5. 准备GLUE数据集
将GLUE数据集放置在项目根目录的`glue/`文件夹中，结构如下：
```
glue/
├── SST-2/
│   ├── train.tsv
│   ├── dev.tsv
│   └── test.tsv
├── CoLA/
│   ├── train.tsv
│   ├── dev.tsv
│   └── test.tsv
└── ...
```

## 快速开始

### 1. 运行示例
```bash
python example_usage.py
```

### 2. 训练单个任务
```bash
python main.py --mode train --task SST-2
```

### 3. 预测单个任务
```bash
python main.py --mode predict --task SST-2
```

### 4. 训练所有任务
```bash
python main.py --mode train
```

### 5. 完整流水线（训练+预测）
```bash
python main.py --mode both --task SST-2
```

## 项目结构

```
spark文本分类/
├── config.py              # 配置文件
├── main.py                # 主程序入口
├── example_usage.py       # 使用示例
├── requirements.txt       # Python依赖
├── README.md              # 项目说明
├── 实验报告.md            # 实验报告
├── src/                   # 源代码目录
│   ├── __init__.py
│   ├── data_processor.py  # Spark数据处理模块
│   ├── model_manager.py   # 模型管理模块
│   ├── trainer.py         # 训练模块
│   └── predictor.py       # 预测模块
├── glue/                  # GLUE数据集目录
├── outputs/               # 训练输出目录
├── saved_models/          # 保存的模型目录
├── results/               # 实验结果目录
└── logs/                  # 日志文件目录
```

## 配置说明

主要配置在`config.py`文件中：

```python
# 模型配置
MODEL_NAME = "distilbert-base-uncased"  # 预训练模型
MAX_LENGTH = 128                        # 最大序列长度
BATCH_SIZE = 16                         # 批次大小
LEARNING_RATE = 2e-5                    # 学习率
NUM_EPOCHS = 3                          # 训练轮数

# Spark配置
SPARK_APP_NAME = "GLUE_Text_Classification"
SPARK_MASTER = "local[*]"               # 使用所有可用核心
SPARK_MEMORY = "4g"                     # Spark内存配置
```

## 使用说明

### 命令行参数

```bash
python main.py [OPTIONS]

选项:
  --mode {train,predict,both}  运行模式 (默认: both)
  --task TASK                  指定单个任务名称
  --tasks TASK1 TASK2 ...      指定多个任务名称
  --model-path PATH            模型路径 (用于预测)
  --config PATH                配置文件路径
```

### 编程接口

```python
from src.trainer import GLUETrainer
from src.predictor import GLUEPredictor

# 训练模型
trainer = GLUETrainer("SST-2")
results = trainer.run_full_pipeline()

# 预测
predictor = GLUEPredictor("SST-2")
predictions = predictor.predict_and_submit()
```

## 输出文件

### 训练输出
- `outputs/{task_name}/`: 训练过程文件
- `saved_models/{task_name}/`: 保存的模型文件
- `results/{task_name}_training_results.json`: 训练结果
- `logs/{task_name}_training.log`: 训练日志

### 预测输出
- `results/{task_name}_submission.tsv`: GLUE提交格式文件
- `results/{task_name}_prediction_stats.json`: 预测统计信息

## 性能优化

### Spark优化
- 调整`SPARK_MEMORY`配置
- 设置合适的并行度
- 使用SSD存储提升I/O性能

### 模型优化
- 使用GPU加速训练（如果可用）
- 调整批次大小和学习率
- 启用混合精度训练

### 内存优化
- 使用数据流水线减少内存占用
- 及时释放不需要的变量
- 监控内存使用情况

## 故障排除

### 常见问题

1. **Spark启动失败**
   - 检查Java环境变量
   - 确认Spark安装正确
   - 检查端口是否被占用

2. **内存不足**
   - 减少批次大小
   - 调整Spark内存配置
   - 使用更小的模型

3. **CUDA错误**
   - 检查GPU驱动和CUDA版本
   - 设置`CUDA_VISIBLE_DEVICES`
   - 降级到CPU训练

4. **数据加载错误**
   - 检查数据文件路径
   - 确认文件格式正确
   - 检查文件编码

## 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证 - 详见[LICENSE](LICENSE)文件

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件至：[<EMAIL>]

## 致谢

- [Hugging Face Transformers](https://github.com/huggingface/transformers)
- [Apache Spark](https://spark.apache.org/)
- [GLUE Benchmark](https://gluebenchmark.com/)
- [PyTorch](https://pytorch.org/)
