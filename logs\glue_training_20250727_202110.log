2025-07-27 20:21:10,693 - utils - INFO - 日志已配置，日志文件: E:\spark文本分类\logs\glue_training_20250727_202110.log
2025-07-27 20:21:14,256 - main - INFO - Spark会话创建成功，版本: 3.5.0
2025-07-27 20:21:14,258 - main - INFO - GLUE基准测试系统初始化完成
2025-07-27 20:21:14,258 - main - INFO - 开始训练任务: CoLA
2025-07-27 20:21:14,259 - main - INFO - 加载训练和验证数据...
2025-07-27 20:21:14,259 - data_processor - INFO - 加载 CoLA 任务的 train 数据: E:\spark文本分类\glue\CoLA\train.tsv
2025-07-27 20:21:17,461 - data_processor - INFO - 加载 CoLA 任务的 dev 数据: E:\spark文本分类\glue\CoLA\dev.tsv
2025-07-27 20:21:18,190 - main - INFO - 数据加载完成: 训练集=100, 验证集=20
2025-07-27 20:21:18,191 - main - INFO - 初始化模型...
2025-07-27 20:21:18,191 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 20:21:18,191 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 20:21:24,151 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 20:21:24,151 - model_manager - INFO - 为任务 CoLA 加载模型，标签数量: 2
2025-07-27 20:21:25,131 - model_manager - INFO - 模型加载成功:
2025-07-27 20:21:25,131 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 20:21:25,131 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 20:21:25,131 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 20:21:25,132 - trainer - INFO - 训练器初始化完成: 任务=CoLA, 输出目录=E:\spark文本分类\models\CoLA
2025-07-27 20:21:25,132 - trainer - INFO - 准备训练数据...
2025-07-27 20:21:25,288 - trainer - INFO - 数据准备完成: 训练集=100, 验证集=20
2025-07-27 20:21:25,288 - main - INFO - 开始训练，参数: {'epochs': 1, 'learning_rate': 2e-05}
2025-07-27 20:21:25,288 - trainer - INFO - 开始训练: 轮数=1, 学习率=2e-05
2025-07-27 20:21:25,289 - model_manager - INFO - 优化器创建成功: lr=2e-05, weight_decay=0.01
2025-07-27 20:21:25,289 - model_manager - INFO - 调度器创建成功: warmup_steps=0, total_steps=4
2025-07-27 20:21:25,290 - trainer - INFO - 开始第 1/1 轮训练
2025-07-27 20:21:26,811 - trainer - INFO - Epoch 1/1:
2025-07-27 20:21:26,811 - trainer - INFO -   训练损失: 0.6362
2025-07-27 20:21:26,812 - trainer - INFO -   accuracy: 0.7500
2025-07-27 20:21:26,812 - trainer - INFO -   matthews_corrcoef: 0.0000
2025-07-27 20:21:26,812 - trainer - INFO -   eval_loss: 0.5903
2025-07-27 20:21:26,812 - trainer - INFO - 训练完成! 总时间: 1s
2025-07-27 20:21:26,813 - trainer - INFO - 训练历史已保存: E:\spark文本分类\models\CoLA\training_history.json
2025-07-27 20:21:26,813 - main - INFO - 任务 CoLA 训练完成!
2025-07-27 20:21:26,813 - main - INFO - 最佳指标: 0.0000
2025-07-27 20:21:26,813 - main - INFO - 训练时间: 1.52秒
2025-07-27 20:21:26,817 - main - INFO - 开始预测任务: CoLA
2025-07-27 20:21:26,817 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 20:21:26,818 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 20:21:27,933 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 20:21:27,934 - model_manager - INFO - 为任务 CoLA 加载模型，标签数量: 2
2025-07-27 20:21:28,944 - model_manager - INFO - 模型加载成功:
2025-07-27 20:21:28,944 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 20:21:28,944 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 20:21:28,944 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 20:21:28,944 - main - INFO - 使用预训练模型进行预测
2025-07-27 20:21:28,944 - main - INFO - 加载测试数据...
2025-07-27 20:21:28,945 - data_processor - INFO - 加载 CoLA 任务的 test 数据: E:\spark文本分类\glue\CoLA\test.tsv
2025-07-27 20:21:29,202 - main - INFO - 测试数据加载完成: 1063 样本
2025-07-27 20:21:29,203 - predictor - INFO - 预测器初始化完成: 任务=CoLA, 设备=cuda
2025-07-27 20:21:29,289 - main - INFO - 准备预测文本: 1063 个样本
2025-07-27 20:21:33,358 - predictor - INFO - 提交文件已生成: E:\spark文本分类\results\CoLA_submission.tsv
2025-07-27 20:21:33,358 - main - INFO - 任务 CoLA 预测完成!
2025-07-27 20:21:33,358 - main - INFO - 提交文件: E:\spark文本分类\results\CoLA_submission.tsv
2025-07-27 20:21:33,537 - main - INFO - Spark会话已关闭
2025-07-27 20:21:33,557 - py4j.clientserver - INFO - Closing down clientserver connection
