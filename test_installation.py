"""
虚拟机环境测试脚本
验证项目是否正确安装和配置
"""
import os
import sys
import traceback
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_python_version():
    """测试Python版本"""
    logger.info("测试Python版本...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        logger.info(f"✓ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        logger.error(f"✗ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        return False

def test_imports():
    """测试模块导入"""
    logger.info("测试模块导入...")
    
    required_modules = [
        'torch',
        'transformers', 
        'pyspark',
        'pandas',
        'numpy',
        'sklearn',
        'datasets'
    ]
    
    failed_imports = []
    
    for module in required_modules:
        try:
            __import__(module)
            logger.info(f"✓ {module}")
        except ImportError as e:
            logger.error(f"✗ {module}: {e}")
            failed_imports.append(module)
    
    return len(failed_imports) == 0

def test_project_structure():
    """测试项目结构"""
    logger.info("测试项目结构...")
    
    required_files = [
        'config.py',
        'main.py',
        'requirements.txt',
        'src/__init__.py',
        'src/data_processor.py',
        'src/model_manager.py',
        'src/trainer.py',
        'src/predictor.py'
    ]
    
    required_dirs = [
        'src',
        'glue'
    ]
    
    missing_files = []
    missing_dirs = []
    
    # 检查文件
    for file_path in required_files:
        if os.path.exists(file_path):
            logger.info(f"✓ {file_path}")
        else:
            logger.error(f"✗ {file_path}")
            missing_files.append(file_path)
    
    # 检查目录
    for dir_path in required_dirs:
        if os.path.isdir(dir_path):
            logger.info(f"✓ {dir_path}/")
        else:
            logger.error(f"✗ {dir_path}/")
            missing_dirs.append(dir_path)
    
    return len(missing_files) == 0 and len(missing_dirs) == 0

def test_config_loading():
    """测试配置加载"""
    logger.info("测试配置加载...")
    
    try:
        from config import Config
        config = Config()
        
        # 检查关键配置
        assert hasattr(config, 'MODEL_NAME')
        assert hasattr(config, 'TASKS')
        assert hasattr(config, 'TASK_CONFIGS')
        
        logger.info(f"✓ 配置加载成功")
        logger.info(f"  模型: {config.MODEL_NAME}")
        logger.info(f"  任务数量: {len(config.TASKS)}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 配置加载失败: {e}")
        return False

def test_spark_basic():
    """测试Spark基础功能"""
    logger.info("测试Spark基础功能...")
    
    try:
        from pyspark.sql import SparkSession
        
        # 创建简单的Spark会话
        spark = SparkSession.builder \
            .appName("TestApp") \
            .master("local[1]") \
            .config("spark.executor.memory", "1g") \
            .config("spark.driver.memory", "1g") \
            .getOrCreate()
        
        # 简单测试
        data = [("Alice", 25), ("Bob", 30)]
        df = spark.createDataFrame(data, ["name", "age"])
        count = df.count()
        
        spark.stop()
        
        logger.info(f"✓ Spark测试成功，数据行数: {count}")
        return True
        
    except Exception as e:
        logger.error(f"✗ Spark测试失败: {e}")
        return False

def test_glue_data():
    """测试GLUE数据集"""
    logger.info("测试GLUE数据集...")
    
    glue_dir = 'glue'
    if not os.path.exists(glue_dir):
        logger.warning("✗ GLUE数据集目录不存在")
        return False
    
    from config import Config
    config = Config()
    
    available_tasks = []
    missing_tasks = []
    
    for task in config.TASKS:
        task_dir = os.path.join(glue_dir, task)
        if os.path.exists(task_dir):
            # 检查是否有训练文件
            train_file = os.path.join(task_dir, 'train.tsv')
            if os.path.exists(train_file):
                available_tasks.append(task)
                logger.info(f"✓ {task}")
            else:
                missing_tasks.append(task)
                logger.warning(f"? {task} (目录存在但缺少train.tsv)")
        else:
            missing_tasks.append(task)
            logger.warning(f"✗ {task}")
    
    logger.info(f"可用任务: {len(available_tasks)}/{len(config.TASKS)}")
    
    return len(available_tasks) > 0

def create_test_directories():
    """创建测试所需的目录"""
    logger.info("创建必要目录...")
    
    dirs = ['outputs', 'saved_models', 'results', 'logs']
    
    for dir_name in dirs:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            logger.info(f"✓ 创建目录: {dir_name}")
        else:
            logger.info(f"✓ 目录已存在: {dir_name}")

def main():
    """主测试函数"""
    logger.info("=" * 50)
    logger.info("开始虚拟机环境测试")
    logger.info("=" * 50)
    
    tests = [
        ("Python版本", test_python_version),
        ("模块导入", test_imports),
        ("项目结构", test_project_structure),
        ("配置加载", test_config_loading),
        ("Spark基础功能", test_spark_basic),
        ("GLUE数据集", test_glue_data),
    ]
    
    # 创建必要目录
    create_test_directories()
    
    # 运行测试
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info("-" * 30)
        try:
            if test_func():
                passed += 1
            else:
                logger.error(f"测试失败: {test_name}")
        except Exception as e:
            logger.error(f"测试异常: {test_name} - {e}")
    
    # 输出结果
    logger.info("=" * 50)
    logger.info(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！虚拟机环境配置成功！")
        logger.info("")
        logger.info("下一步:")
        logger.info("1. 运行示例: python example_usage.py")
        logger.info("2. 训练模型: python main.py --mode train --task RTE")
        logger.info("3. 查看文档: README.md")
        return True
    else:
        logger.error("❌ 部分测试失败，请检查配置")
        logger.error("")
        logger.error("解决方案:")
        logger.error("1. 检查依赖安装: pip install -r requirements.txt")
        logger.error("2. 检查Java环境: java -version")
        logger.error("3. 检查GLUE数据集是否正确放置")
        logger.error("4. 检查Spark环境变量")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
