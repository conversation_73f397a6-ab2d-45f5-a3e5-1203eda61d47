#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pandas GLUE训练器 - 模拟Spark数据处理 + PyTorch深度学习
避开所有Spark和TensorFlow问题
"""
import os
import json
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from sklearn.metrics import accuracy_score, f1_score, matthews_corrcoef
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split
from datetime import datetime
import logging
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PandasDataProcessor:
    """Pandas数据处理器 - 模拟Spark功能"""
    
    def __init__(self):
        logger.info("初始化Pandas数据处理器（模拟Spark功能）")
        
    def create_sst2_data(self, num_samples=1000):
        """创建SST-2情感分析数据"""
        logger.info(f"创建SST-2数据集: {num_samples}样本")
        
        # 正面情感模板和词汇
        positive_templates = [
            "This movie is {} and {}!",
            "I {} this film, it's {}!",
            "{} performance and {} story!",
            "{} cinematography and {} acting!",
            "One of the {} movies I have ever seen!",
            "The {} plot and {} characters!",
            "{} direction and {} screenplay!",
            "Amazing {} and incredible {}!",
            "Wonderful {} with {} execution!",
            "Brilliant {} and {} performances!"
        ]
        
        negative_templates = [
            "This movie is {} and {}.",
            "I {} this film, it's {}.",
            "{} acting and {} storyline.",
            "{} direction and {} script.",
            "One of the {} films ever made.",
            "The {} plot and {} characters.",
            "{} cinematography and {} dialogue.",
            "Terrible {} and awful {}.",
            "Disappointing {} with {} execution.",
            "Boring {} and {} performances."
        ]
        
        positive_words = [
            "fantastic", "amazing", "excellent", "brilliant", "outstanding", 
            "wonderful", "incredible", "superb", "magnificent", "spectacular",
            "love", "adore", "enjoy", "appreciate", "best", "greatest", "perfect",
            "marvelous", "exceptional", "extraordinary", "phenomenal", "stunning"
        ]
        
        negative_words = [
            "terrible", "awful", "horrible", "boring", "disappointing", 
            "bad", "poor", "worst", "hate", "dislike", "dreadful", "pathetic",
            "disgusting", "annoying", "frustrating", "unbearable", "painful",
            "mediocre", "uninspiring", "forgettable", "tedious", "irritating"
        ]
        
        # 生成数据
        data = []
        for i in range(num_samples):
            if i % 2 == 0:
                # 正面情感
                template = positive_templates[i % len(positive_templates)]
                words = np.random.choice(positive_words, 2, replace=False)
                sentence = template.format(words[0], words[1])
                label = 1
            else:
                # 负面情感
                template = negative_templates[i % len(negative_templates)]
                words = np.random.choice(negative_words, 2, replace=False)
                sentence = template.format(words[0], words[1])
                label = 0
            
            data.append({"sentence": sentence, "label": label})
        
        df = pd.DataFrame(data)
        logger.info(f"✅ SST-2数据创建完成: {len(df)}条记录")
        return df
    
    def create_rte_data(self, num_samples=800):
        """创建RTE文本蕴含数据"""
        logger.info(f"创建RTE数据集: {num_samples}样本")
        
        # 蕴含关系对（前提 → 假设）
        entailment_pairs = [
            ("The cat is sleeping on the sofa.", "There is a cat on the furniture.", 1),
            ("John went to the grocery store.", "John is shopping for food.", 1),
            ("She is reading a science book.", "She is studying science.", 1),
            ("The dog is barking loudly.", "There is a dog making noise.", 1),
            ("The restaurant serves Italian food.", "You can eat pasta there.", 1),
            ("She works as a teacher.", "She educates students.", 1),
            ("The car is moving fast.", "The vehicle is in motion.", 1),
            ("He is playing the piano.", "He is making music.", 1),
            ("The flowers are blooming.", "Plants are growing.", 1),
            ("Students are studying hard.", "People are learning.", 1),
            ("The bird is flying high.", "An animal is in the air.", 1),
            ("She is cooking dinner.", "Someone is preparing food.", 1),
            ("The train arrived on time.", "Transportation was punctual.", 1),
            ("He bought a new computer.", "Someone made a purchase.", 1),
            ("The children are playing outside.", "Kids are having fun outdoors.", 1)
        ]
        
        # 矛盾关系对
        contradiction_pairs = [
            ("The weather is sunny today.", "It's raining outside.", 0),
            ("The car is painted red.", "The car is blue in color.", 0),
            ("Students are taking an exam.", "Students are playing games.", 0),
            ("The movie starts at 8 PM.", "The movie ends at 8 PM.", 0),
            ("The book is very thick.", "The book is very thin.", 0),
            ("The room is completely dark.", "The room is brightly lit.", 0),
            ("He is very tall.", "He is very short.", 0),
            ("The water is hot.", "The water is cold.", 0),
            ("The door is open.", "The door is closed.", 0),
            ("She is happy.", "She is sad.", 0),
            ("The store is crowded.", "The store is empty.", 0),
            ("The music is loud.", "The music is quiet.", 0),
            ("He is running fast.", "He is walking slowly.", 0),
            ("The food is delicious.", "The food tastes terrible.", 0),
            ("The building is new.", "The building is ancient.", 0)
        ]
        
        all_pairs = entailment_pairs + contradiction_pairs
        
        # 生成数据
        data = []
        for i in range(num_samples):
            premise, hypothesis, label = all_pairs[i % len(all_pairs)]
            data.append({
                "index": i,
                "sentence1": premise,
                "sentence2": hypothesis,
                "label": label
            })
        
        df = pd.DataFrame(data)
        logger.info(f"✅ RTE数据创建完成: {len(df)}条记录")
        return df
    
    def preprocess_data(self, df, task_name):
        """预处理数据（模拟Spark数据处理）"""
        logger.info(f"预处理{task_name}数据...")
        
        if task_name == "SST-2":
            # 单句情感分析
            df['text'] = df['sentence'].apply(lambda x: re.sub(r'[^a-zA-Z0-9\s]', '', str(x)))
            df = df[df['text'].str.len() > 5]  # 过滤太短的文本
            df['labels'] = df['label']
            
        elif task_name == "RTE":
            # 文本蕴含，组合两个句子
            df['text'] = df['sentence1'] + " [SEP] " + df['sentence2']
            df['text'] = df['text'].apply(lambda x: re.sub(r'[^a-zA-Z0-9\s\[\]]', '', str(x)))
            df = df[df['text'].str.len() > 10]
            df['labels'] = df['label']
        
        # 添加文本长度统计
        df['text_length'] = df['text'].str.len()
        
        # 显示统计信息
        total_count = len(df)
        avg_length = df['text_length'].mean()
        
        logger.info(f"✅ 预处理完成: {total_count}条记录，平均长度: {avg_length:.1f}")
        
        return df[['text', 'labels', 'text_length']]
    
    def split_data(self, df, train_ratio=0.8):
        """分割训练和验证数据"""
        logger.info("分割训练和验证数据...")
        
        train_df, dev_df = train_test_split(
            df, 
            test_size=1-train_ratio, 
            random_state=42, 
            stratify=df['labels']
        )
        
        logger.info(f"✅ 数据分割完成: 训练集{len(train_df)}条，验证集{len(dev_df)}条")
        
        return train_df, dev_df
    
    def get_statistics(self, df, task_name):
        """获取数据统计信息"""
        stats = {
            "task": task_name,
            "total_samples": len(df),
            "avg_text_length": round(df['text_length'].mean(), 2),
            "label_distribution": df['labels'].value_counts().to_dict()
        }
        
        logger.info(f"{task_name}统计: {stats}")
        return stats

class GLUETextClassifier(nn.Module):
    """GLUE文本分类器"""
    
    def __init__(self, input_dim, hidden_dim=128, num_classes=2, dropout=0.3):
        super().__init__()
        self.fc1 = nn.Linear(input_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim // 2)
        self.fc3 = nn.Linear(hidden_dim // 2, num_classes)
        self.dropout = nn.Dropout(dropout)
        self.relu = nn.ReLU()
        self.batch_norm1 = nn.BatchNorm1d(hidden_dim)
        self.batch_norm2 = nn.BatchNorm1d(hidden_dim // 2)
        
    def forward(self, x):
        x = self.fc1(x)
        x = self.batch_norm1(x)
        x = self.relu(x)
        x = self.dropout(x)
        
        x = self.fc2(x)
        x = self.batch_norm2(x)
        x = self.relu(x)
        x = self.dropout(x)
        
        x = self.fc3(x)
        return x

class GLUETrainer:
    """GLUE训练器"""
    
    def __init__(self):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.vectorizer = TfidfVectorizer(
            max_features=5000, 
            stop_words='english',
            ngram_range=(1, 2),  # 使用1-gram和2-gram
            min_df=2,
            max_df=0.95
        )
        
        logger.info(f"初始化训练器，使用设备: {self.device}")
    
    def prepare_data(self, train_df, dev_df):
        """准备训练数据"""
        logger.info("准备训练数据...")
        
        # 提取文本和标签
        train_texts = train_df['text'].tolist()
        train_labels = train_df['labels'].tolist()
        dev_texts = dev_df['text'].tolist()
        dev_labels = dev_df['labels'].tolist()
        
        # 使用TF-IDF向量化
        X_train = self.vectorizer.fit_transform(train_texts)
        X_dev = self.vectorizer.transform(dev_texts)
        
        # 转换为密集矩阵
        X_train = X_train.toarray()
        X_dev = X_dev.toarray()
        
        # 转换为PyTorch张量
        X_train = torch.FloatTensor(X_train).to(self.device)
        X_dev = torch.FloatTensor(X_dev).to(self.device)
        y_train = torch.LongTensor(train_labels).to(self.device)
        y_dev = torch.LongTensor(dev_labels).to(self.device)
        
        logger.info(f"✅ 数据准备完成: 训练{len(train_labels)}条，验证{len(dev_labels)}条")
        logger.info(f"特征维度: {X_train.shape[1]}")
        
        return X_train, y_train, X_dev, y_dev
    
    def train_model(self, X_train, y_train, X_dev, y_dev, epochs=10):
        """训练模型"""
        logger.info("开始训练模型...")
        
        # 创建模型
        input_dim = X_train.shape[1]
        model = GLUETextClassifier(input_dim, hidden_dim=256, num_classes=2).to(self.device)
        
        # 优化器和损失函数
        optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
        scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=3, gamma=0.7)
        criterion = nn.CrossEntropyLoss()
        
        # 训练循环
        best_acc = 0
        best_model_state = None
        
        model.train()
        for epoch in range(epochs):
            optimizer.zero_grad()
            outputs = model(X_train)
            loss = criterion(outputs, y_train)
            loss.backward()
            optimizer.step()
            scheduler.step()
            
            # 计算训练准确率
            with torch.no_grad():
                train_pred = torch.argmax(outputs, dim=1)
                train_acc = (train_pred == y_train).float().mean()
                
                # 验证集评估
                model.eval()
                dev_outputs = model(X_dev)
                dev_pred = torch.argmax(dev_outputs, dim=1)
                dev_acc = (dev_pred == y_dev).float().mean()
                model.train()
                
                # 保存最佳模型
                if dev_acc > best_acc:
                    best_acc = dev_acc
                    best_model_state = model.state_dict().copy()
            
            logger.info(f"Epoch {epoch+1}/{epochs}, 损失: {loss.item():.4f}, "
                       f"训练准确率: {train_acc:.4f}, 验证准确率: {dev_acc:.4f}")
        
        # 加载最佳模型进行最终评估
        model.load_state_dict(best_model_state)
        model.eval()
        
        with torch.no_grad():
            dev_outputs = model(X_dev)
            dev_pred = torch.argmax(dev_outputs, dim=1)
            dev_acc = (dev_pred == y_dev).float().mean()
            
            # 计算更多指标
            y_true = y_dev.cpu().numpy()
            y_pred = dev_pred.cpu().numpy()
            
            f1 = f1_score(y_true, y_pred, average='weighted')
            matthews = matthews_corrcoef(y_true, y_pred)
        
        logger.info(f"✅ 训练完成! 最佳验证准确率: {dev_acc:.4f}, F1: {f1:.4f}, Matthews: {matthews:.4f}")
        
        return {
            "model": model,
            "accuracy": dev_acc.item(),
            "f1": f1,
            "matthews_corr": matthews,
            "final_loss": loss.item()
        }

def run_glue_training(task_name="SST-2", num_samples=1000):
    """运行GLUE训练"""
    print(f"=== Pandas GLUE 训练: {task_name} ===")
    print(f"样本数量: {num_samples}")
    print("=" * 50)
    
    # 检查环境
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
    print(f"Pandas版本: {pd.__version__}")
    print(f"NumPy版本: {np.__version__}")
    
    processor = PandasDataProcessor()
    trainer = GLUETrainer()
    
    start_time = datetime.now()
    
    try:
        # 1. 创建数据
        if task_name == "SST-2":
            df = processor.create_sst2_data(num_samples)
        elif task_name == "RTE":
            df = processor.create_rte_data(num_samples)
        else:
            raise ValueError(f"不支持的任务: {task_name}")
        
        # 2. 预处理数据
        processed_df = processor.preprocess_data(df, task_name)
        
        # 3. 获取统计信息
        stats = processor.get_statistics(processed_df, task_name)
        
        # 4. 分割数据
        train_df, dev_df = processor.split_data(processed_df)
        
        # 5. 准备训练数据
        X_train, y_train, X_dev, y_dev = trainer.prepare_data(train_df, dev_df)
        
        # 6. 训练模型
        results = trainer.train_model(X_train, y_train, X_dev, y_dev)
        
        end_time = datetime.now()
        training_time = (end_time - start_time).total_seconds()
        
        # 7. 保存结果
        final_results = {
            "task_name": task_name,
            "timestamp": datetime.now().isoformat(),
            "training_time": training_time,
            "accuracy": results["accuracy"],
            "f1": results["f1"],
            "matthews_corr": results["matthews_corr"],
            "final_loss": results["final_loss"],
            "data_stats": stats,
            "num_samples": num_samples,
            "train_samples": len(train_df),
            "dev_samples": len(dev_df),
            "device": str(trainer.device)
        }
        
        os.makedirs("results", exist_ok=True)
        with open(f"results/pandas_glue_{task_name}_results.json", "w") as f:
            json.dump(final_results, f, indent=2)
        
        # 显示结果
        print("\n" + "=" * 50)
        print("🎉 训练完成!")
        print(f"任务: {task_name}")
        print(f"准确率: {results['accuracy']:.4f}")
        print(f"F1分数: {results['f1']:.4f}")
        print(f"Matthews相关系数: {results['matthews_corr']:.4f}")
        print(f"训练时间: {training_time:.1f}秒")
        print(f"使用设备: {trainer.device}")
        print(f"结果保存: results/pandas_glue_{task_name}_results.json")
        
        return final_results
        
    except Exception as e:
        logger.error(f"训练失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    import sys
    
    task = sys.argv[1] if len(sys.argv) > 1 else "SST-2"
    samples = int(sys.argv[2]) if len(sys.argv) > 2 else 1000
    
    run_glue_training(task, samples)
