"""
测试修复后的训练器
"""
import os
import sys

# 设置环境变量
os.environ['USE_TF'] = 'NO'
os.environ['USE_TORCH'] = 'YES'

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from main import GLUEBenchmark
from utils import setup_logging, set_seed
import config

def test_single_task():
    """测试单个任务"""
    print("🧪 测试修复后的训练器")
    
    # 设置日志
    logger = setup_logging(config.LOG_DIR, "INFO")
    set_seed(config.RANDOM_SEED)
    
    benchmark = GLUEBenchmark()
    
    try:
        # 测试CoLA任务（小规模）
        print("测试CoLA任务...")
        result = benchmark.train_task(
            task="CoLA",
            epochs=1,
            max_samples=100
        )
        
        print(f"✅ CoLA训练成功!")
        print(f"最佳指标: {result['best_metric']:.4f}")
        
        # 测试预测
        submission_path = benchmark.predict_task("CoLA")
        print(f"✅ CoLA预测成功: {submission_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        benchmark.cleanup()

if __name__ == "__main__":
    success = test_single_task()
    if success:
        print("\n🎉 修复成功！可以继续运行完整的GLUE测试")
    else:
        print("\n❌ 还有问题需要修复")
