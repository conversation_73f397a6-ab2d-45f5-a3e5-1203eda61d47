"""
测试数据处理模块
"""
import os
import sys
import logging
from pyspark.sql import SparkSession

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_processor import GLUEDataProcessor
from utils import setup_logging, set_seed
import config

def create_spark_session():
    """创建Spark会话"""
    try:
        spark = SparkSession.builder \
            .appName(config.SPARK_APP_NAME) \
            .master(config.SPARK_MASTER) \
            .config("spark.sql.adaptive.enabled", "true") \
            .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
            .config("spark.driver.memory", "2g") \
            .config("spark.executor.memory", "2g") \
            .getOrCreate()
        
        print(f"Spark会话创建成功，版本: {spark.version}")
        return spark
    except Exception as e:
        print(f"创建Spark会话失败: {e}")
        return None

def test_data_loading():
    """测试数据加载功能"""
    print("=" * 50)
    print("测试数据加载功能")
    print("=" * 50)
    
    # 设置随机种子
    set_seed(config.RANDOM_SEED)
    
    # 设置日志
    logger = setup_logging(config.LOG_DIR)
    
    # 创建Spark会话
    spark = create_spark_session()
    if spark is None:
        print("无法创建Spark会话，测试终止")
        return False
    
    try:
        # 创建数据处理器
        processor = GLUEDataProcessor(spark, config.DATA_DIR)
        print(f"数据处理器创建成功，数据目录: {config.DATA_DIR}")
        
        # 检查数据目录
        if not os.path.exists(config.DATA_DIR):
            print(f"数据目录不存在: {config.DATA_DIR}")
            return False
        
        # 列出可用的任务
        available_tasks = []
        for task in config.GLUE_TASKS:
            task_dir = os.path.join(config.DATA_DIR, task)
            if os.path.exists(task_dir):
                available_tasks.append(task)
                print(f"✓ 找到任务目录: {task}")
            else:
                print(f"✗ 任务目录不存在: {task}")
        
        if not available_tasks:
            print("没有找到任何可用的任务数据")
            return False
        
        # 测试加载第一个可用任务的数据
        test_task = available_tasks[0]
        print(f"\n测试加载任务: {test_task}")
        
        try:
            # 尝试加载训练数据
            train_df = processor.load_task_data(test_task, "train")
            print(f"✓ 成功加载 {test_task} 训练数据")
            print(f"  - 数据行数: {train_df.count()}")
            print(f"  - 数据列数: {len(train_df.columns)}")
            print(f"  - 列名: {train_df.columns}")
            
            # 显示前几行数据
            print("\n前5行数据:")
            train_df.show(5, truncate=False)
            
            # 尝试加载验证数据
            try:
                dev_df = processor.load_task_data(test_task, "dev")
                print(f"✓ 成功加载 {test_task} 验证数据")
                print(f"  - 数据行数: {dev_df.count()}")
            except Exception as e:
                print(f"✗ 加载验证数据失败: {e}")
            
            # 获取任务统计信息
            print(f"\n获取 {test_task} 任务统计信息:")
            stats = processor.get_task_statistics(test_task)
            for key, value in stats.items():
                print(f"  - {key}: {value}")
            
        except Exception as e:
            print(f"✗ 加载 {test_task} 数据失败: {e}")
            return False
        
        print("\n数据加载测试完成！")
        return True
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 关闭Spark会话
        if spark:
            spark.stop()
            print("Spark会话已关闭")

def test_multiple_tasks():
    """测试多个任务的数据加载"""
    print("\n" + "=" * 50)
    print("测试多个任务的数据加载")
    print("=" * 50)
    
    spark = create_spark_session()
    if spark is None:
        return False
    
    try:
        processor = GLUEDataProcessor(spark, config.DATA_DIR)
        
        # 测试所有可用任务
        for task in config.GLUE_TASKS:
            task_dir = os.path.join(config.DATA_DIR, task)
            if os.path.exists(task_dir):
                print(f"\n测试任务: {task}")
                try:
                    # 检查训练文件
                    train_files = []
                    for file_name in os.listdir(task_dir):
                        if file_name.endswith(('.tsv', '.txt')):
                            train_files.append(file_name)
                    
                    print(f"  可用文件: {train_files}")
                    
                    # 尝试加载数据
                    if 'train.tsv' in train_files or 'msr_paraphrase_train.txt' in train_files:
                        df = processor.load_task_data(task, "train")
                        print(f"  ✓ 训练数据: {df.count()} 行")
                    
                except Exception as e:
                    print(f"  ✗ 加载失败: {e}")
            else:
                print(f"✗ {task}: 目录不存在")
        
        return True
        
    except Exception as e:
        print(f"多任务测试失败: {e}")
        return False
    
    finally:
        if spark:
            spark.stop()

if __name__ == "__main__":
    print("开始测试数据处理模块...")
    
    # 检查基本环境
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print(f"配置的数据目录: {config.DATA_DIR}")
    
    # 运行测试
    success1 = test_data_loading()
    success2 = test_multiple_tasks()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
