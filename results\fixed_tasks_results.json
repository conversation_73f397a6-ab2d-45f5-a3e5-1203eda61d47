{"STS-B": {"task": "STS-B", "status": "success", "attempt": 1, "train_time": 179.147935628891, "best_metric": 0.8941854714894351, "training_history": [{"epoch": 1, "train_loss": 3.828204433334635, "eval_metrics": {"pearson_correlation": 0.8467119934129848, "spearman_correlation": 0.8361447317979788, "combined_score": 0.8414283626054817, "eval_loss": 0.7473045383629046}, "learning_rate": 7.401574803149607e-06}, {"epoch": 2, "train_loss": 0.7570601140565061, "eval_metrics": {"pearson_correlation": 0.8895656545275132, "spearman_correlation": 0.8937313408517986, "combined_score": 0.8916484976896559, "eval_loss": 0.5718409560228649}, "learning_rate": 3.7007874015748035e-06}, {"epoch": 3, "train_loss": 0.5823819299644613, "eval_metrics": {"pearson_correlation": 0.8919452865188567, "spearman_correlation": 0.8964256564600133, "combined_score": 0.8941854714894351, "eval_loss": 0.5564256671227907}, "learning_rate": 0.0}], "submission_file": "E:\\spark文本分类\\results\\STS-B_submission.tsv", "params_used": {"epochs": 3, "max_samples": 3000, "learning_rate": 1e-05}}, "MRPC": {"task": "MRPC", "status": "failed", "error": "num_samples should be a positive integer value, but got num_samples=0", "attempts": 3}, "QNLI": {"task": "QNLI", "status": "success", "attempt": 1, "train_time": 353.09514260292053, "best_metric": 0.483, "training_history": [{"epoch": 1, "train_loss": 0.12088341524106729, "eval_metrics": {"accuracy": 0.471, "f1": 0.6403806934058464, "eval_loss": 2.865941233932972}, "learning_rate": 1.4811320754716983e-05}, {"epoch": 2, "train_loss": 0.01957052043983179, "eval_metrics": {"accuracy": 0.483, "f1": 0.6456477039067855, "eval_loss": 3.216757118701935}, "learning_rate": 7.405660377358491e-06}, {"epoch": 3, "train_loss": 0.00913111239102236, "eval_metrics": {"accuracy": 0.479, "f1": 0.6438824333561176, "eval_loss": 3.4534579142928123}, "learning_rate": 0.0}], "submission_file": "E:\\spark文本分类\\results\\QNLI_submission.tsv", "params_used": {"epochs": 3, "max_samples": 5000, "learning_rate": 2e-05}}, "WNLI": {"task": "WNLI", "status": "success", "attempt": 1, "train_time": 70.84202790260315, "best_metric": 0.4225352112676056, "training_history": [{"epoch": 1, "train_loss": 0.7120655335878071, "eval_metrics": {"accuracy": 0.4225352112676056, "f1": 0.594059405940594, "eval_loss": 0.7222005724906921}, "learning_rate": 8.837209302325582e-06}, {"epoch": 2, "train_loss": 0.6999559465207552, "eval_metrics": {"accuracy": 0.3380281690140845, "f1": 0.37333333333333335, "eval_loss": 0.717329760392507}, "learning_rate": 6.627906976744186e-06}, {"epoch": 3, "train_loss": 0.6931399044237638, "eval_metrics": {"accuracy": 0.323943661971831, "f1": 0.38461538461538464, "eval_loss": 0.722031037012736}, "learning_rate": 4.418604651162791e-06}, {"epoch": 4, "train_loss": 0.6945428001253229, "eval_metrics": {"accuracy": 0.36619718309859156, "f1": 0.3076923076923077, "eval_loss": 0.7214810848236084}, "learning_rate": 2.2093023255813954e-06}, {"epoch": 5, "train_loss": 0.700791459334524, "eval_metrics": {"accuracy": 0.323943661971831, "f1": 0.1724137931034483, "eval_loss": 0.7217797636985779}, "learning_rate": 0.0}], "submission_file": "E:\\spark文本分类\\results\\WNLI_submission.tsv", "params_used": {"epochs": 5, "max_samples": 600, "learning_rate": 1e-05}}}