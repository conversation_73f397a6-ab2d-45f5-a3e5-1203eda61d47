"""
配置文件 - 包含项目的所有配置参数
"""
import os

class Config:
    # 数据路径配置
    DATA_ROOT = "glue"
    TASKS = ["SST-2", "CoLA", "MRPC", "RTE", "QNLI", "QQP", "MNLI", "STS-B", "WNLI"]
    
    # 模型配置
    MODEL_NAME = "distilbert-base-uncased"  # 可选: bert-base-uncased, roberta-base
    MAX_LENGTH = 128
    BATCH_SIZE = 16
    LEARNING_RATE = 2e-5
    NUM_EPOCHS = 3
    WARMUP_STEPS = 500
    
    # Spark配置
    SPARK_APP_NAME = "GLUE_Text_Classification"
    SPARK_MASTER = "local[*]"  # 使用所有可用核心
    SPARK_MEMORY = "4g"
    
    # 输出路径
    OUTPUT_DIR = "outputs"
    MODEL_SAVE_DIR = "saved_models"
    RESULTS_DIR = "results"
    LOGS_DIR = "logs"
    
    # 训练配置
    SAVE_STEPS = 500
    EVAL_STEPS = 500
    LOGGING_STEPS = 100
    SEED = 42
    
    # GLUE任务特定配置
    TASK_CONFIGS = {
        "SST-2": {
            "num_labels": 2,
            "metric": "accuracy",
            "text_column": "sentence",
            "label_column": "label"
        },
        "CoLA": {
            "num_labels": 2,
            "metric": "matthews_correlation",
            "text_column": "sentence",
            "label_column": "label"
        },
        "MRPC": {
            "num_labels": 2,
            "metric": "f1",
            "text_column": ["sentence1", "sentence2"],
            "label_column": "label"
        },
        "RTE": {
            "num_labels": 2,
            "metric": "accuracy",
            "text_column": ["sentence1", "sentence2"],
            "label_column": "label"
        },
        "QNLI": {
            "num_labels": 2,
            "metric": "accuracy",
            "text_column": ["question", "sentence"],
            "label_column": "label"
        },
        "QQP": {
            "num_labels": 2,
            "metric": "f1",
            "text_column": ["question1", "question2"],
            "label_column": "label"
        },
        "MNLI": {
            "num_labels": 3,
            "metric": "accuracy",
            "text_column": ["premise", "hypothesis"],
            "label_column": "label"
        },
        "STS-B": {
            "num_labels": 1,
            "metric": "pearson",
            "text_column": ["sentence1", "sentence2"],
            "label_column": "score"
        },
        "WNLI": {
            "num_labels": 2,
            "metric": "accuracy",
            "text_column": ["sentence1", "sentence2"],
            "label_column": "label"
        }
    }
    
    @classmethod
    def get_task_config(cls, task_name):
        """获取特定任务的配置"""
        return cls.TASK_CONFIGS.get(task_name, {})
    
    @classmethod
    def create_directories(cls):
        """创建必要的目录"""
        dirs = [cls.OUTPUT_DIR, cls.MODEL_SAVE_DIR, cls.RESULTS_DIR, cls.LOGS_DIR]
        for dir_path in dirs:
            os.makedirs(dir_path, exist_ok=True)
