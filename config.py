"""
配置文件 - GLUE基准测试项目配置
"""
import os

# ==================== 基本配置 ====================
# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

# 数据目录
DATA_DIR = os.path.join(PROJECT_ROOT, "glue")

# 模型保存目录
MODEL_SAVE_DIR = os.path.join(PROJECT_ROOT, "models")

# 结果输出目录
RESULTS_DIR = os.path.join(PROJECT_ROOT, "results")

# 日志目录
LOG_DIR = os.path.join(PROJECT_ROOT, "logs")

# ==================== 模型配置 ====================
# 预训练模型路径或名称
MODEL_NAME = "bert-base-uncased"  # 可以是本地路径或HuggingFace模型名

# 最大序列长度
MAX_SEQ_LENGTH = 128

# ==================== 训练配置 ====================
# 批次大小
BATCH_SIZE = 32

# 学习率
LEARNING_RATE = 2e-5

# 训练轮数
EPOCHS = 3

# 权重衰减
WEIGHT_DECAY = 0.01

# 梯度裁剪
MAX_GRAD_NORM = 1.0

# 保存检查点的步数
SAVE_STEPS = 500

# 评估步数
EVAL_STEPS = 500

# 日志步数
LOGGING_STEPS = 100

# ==================== Spark配置 ====================
# Spark应用名称
SPARK_APP_NAME = "GLUE-Text-Classification"

# Spark主节点
SPARK_MASTER = "local[*]"  # 本地模式，使用所有CPU核心

# Spark配置
SPARK_CONFIG = {
    "spark.sql.adaptive.enabled": "true",
    "spark.sql.adaptive.coalescePartitions.enabled": "true",
    "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
    "spark.sql.execution.arrow.pyspark.enabled": "true",
    "spark.driver.memory": "4g",
    "spark.executor.memory": "4g",
    "spark.driver.maxResultSize": "2g"
}

# ==================== GLUE任务配置 ====================
GLUE_TASKS = [
    "CoLA",    # 语言可接受性判断
    "SST-2",   # 情感分析
    "MRPC",    # 释义识别
    "STS-B",   # 语义相似度
    "QQP",     # 问题对等价性
    "MNLI",    # 自然语言推理
    "QNLI",    # 问答自然语言推理
    "RTE",     # 文本蕴含识别
    "WNLI"     # Winograd自然语言推理
]

# 任务类型映射
TASK_TYPES = {
    "CoLA": "classification",
    "SST-2": "classification", 
    "MRPC": "classification",
    "STS-B": "regression",
    "QQP": "classification",
    "MNLI": "classification",
    "QNLI": "classification",
    "RTE": "classification",
    "WNLI": "classification"
}

# 任务标签数量
NUM_LABELS = {
    "CoLA": 2,
    "SST-2": 2,
    "MRPC": 2,
    "STS-B": 1,  # 回归任务
    "QQP": 2,
    "MNLI": 3,
    "QNLI": 2,
    "RTE": 2,
    "WNLI": 2
}

# 评估指标
TASK_METRICS = {
    "CoLA": "matthews_corrcoef",
    "SST-2": "accuracy",
    "MRPC": "f1",
    "STS-B": "pearson_spearman",
    "QQP": "f1",
    "MNLI": "accuracy",
    "QNLI": "accuracy", 
    "RTE": "accuracy",
    "WNLI": "accuracy"
}

# ==================== 其他配置 ====================
# 随机种子
RANDOM_SEED = 42

# 是否使用GPU
USE_GPU = True

# 设备
DEVICE = "cuda" if USE_GPU else "cpu"

# 是否启用混合精度训练
USE_FP16 = False

# 是否保存最佳模型
SAVE_BEST_MODEL = True

# 早停耐心值
EARLY_STOPPING_PATIENCE = 3

# 创建必要的目录
for directory in [MODEL_SAVE_DIR, RESULTS_DIR, LOG_DIR]:
    os.makedirs(directory, exist_ok=True)
