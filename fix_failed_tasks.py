"""
专门修复之前失败的GLUE任务
"""
import os
import sys
import json
import time

# 设置环境变量
os.environ['USE_TF'] = 'NO'
os.environ['USE_TORCH'] = 'YES'

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from main import GLUEBenchmark
from utils import setup_logging, set_seed
import config

def run_task_with_retry(task: str, max_retries: int = 2) -> dict:
    """运行任务，失败时重试"""
    
    # 任务特定的参数配置
    task_configs = {
        "STS-B": [
            {"epochs": 3, "max_samples": 3000, "learning_rate": 1e-5},
            {"epochs": 2, "max_samples": 1500, "learning_rate": 5e-6},
            {"epochs": 1, "max_samples": 500, "learning_rate": 1e-5}
        ],
        "MRPC": [
            {"epochs": 4, "max_samples": 3000, "learning_rate": 2e-5},
            {"epochs": 3, "max_samples": 1500, "learning_rate": 1e-5},
            {"epochs": 2, "max_samples": 800, "learning_rate": 1e-5}
        ],
        "QNLI": [
            {"epochs": 3, "max_samples": 5000, "learning_rate": 2e-5},
            {"epochs": 2, "max_samples": 3000, "learning_rate": 1e-5},
            {"epochs": 1, "max_samples": 1000, "learning_rate": 1e-5}
        ],
        "WNLI": [
            {"epochs": 5, "max_samples": 600, "learning_rate": 1e-5},
            {"epochs": 4, "max_samples": 400, "learning_rate": 5e-6},
            {"epochs": 3, "max_samples": 200, "learning_rate": 1e-5}
        ]
    }
    
    configs = task_configs.get(task, [{"epochs": 2, "max_samples": 1000, "learning_rate": 2e-5}])
    
    for attempt in range(max_retries + 1):
        config_idx = min(attempt, len(configs) - 1)
        params = configs[config_idx]
        
        print(f"\n{'='*60}")
        print(f"修复任务: {task} (尝试 {attempt + 1}/{max_retries + 1})")
        print(f"参数: {params}")
        print(f"{'='*60}")
        
        benchmark = GLUEBenchmark()
        
        try:
            # 训练
            print(f"🚀 开始训练 {task}...")
            start_time = time.time()
            
            train_results = benchmark.train_task(
                task=task,
                epochs=params["epochs"],
                learning_rate=params["learning_rate"],
                max_samples=params["max_samples"]
            )
            
            train_time = time.time() - start_time
            
            # 预测
            print(f"🔮 开始预测 {task}...")
            submission_path = benchmark.predict_task(task)
            
            # 分析结果
            if train_results['training_history']:
                final_metrics = train_results['training_history'][-1]['eval_metrics']
                
                print(f"✅ {task} 成功!")
                print(f"   最佳指标: {train_results['best_metric']:.4f}")
                print(f"   训练时间: {train_time:.2f}秒")
                
                # 显示具体指标
                if task == "STS-B":
                    pearson = final_metrics.get('pearson_correlation', 0)
                    spearman = final_metrics.get('spearman_correlation', 0)
                    print(f"   Pearson相关系数: {pearson:.4f}")
                    print(f"   Spearman相关系数: {spearman:.4f}")
                elif task == "MRPC":
                    print(f"   准确率: {final_metrics.get('accuracy', 0)*100:.2f}%")
                    print(f"   F1分数: {final_metrics.get('f1', 0)*100:.2f}%")
                else:
                    print(f"   准确率: {final_metrics.get('accuracy', 0)*100:.2f}%")
                
                return {
                    'task': task,
                    'status': 'success',
                    'attempt': attempt + 1,
                    'train_time': train_time,
                    'best_metric': train_results['best_metric'],
                    'training_history': train_results['training_history'],
                    'submission_file': submission_path,
                    'params_used': params
                }
            
        except Exception as e:
            print(f"❌ {task} 尝试 {attempt + 1} 失败: {e}")
            if attempt < max_retries:
                print(f"🔄 将使用更保守的参数重试...")
            else:
                print(f"💥 {task} 所有尝试都失败了")
                return {
                    'task': task,
                    'status': 'failed',
                    'error': str(e),
                    'attempts': max_retries + 1
                }
        
        finally:
            benchmark.cleanup()
    
    return {
        'task': task,
        'status': 'failed',
        'error': 'All attempts failed',
        'attempts': max_retries + 1
    }

def main():
    """主函数"""
    print("🔧 修复失败的GLUE任务")
    print("=" * 80)
    
    # 设置日志
    logger = setup_logging(config.LOG_DIR, "INFO")
    set_seed(config.RANDOM_SEED)
    
    # 需要修复的任务（按优先级排序）
    failed_tasks = ["STS-B", "MRPC", "QNLI", "WNLI"]
    
    print(f"需要修复的任务: {failed_tasks}")
    
    results = {}
    
    for task in failed_tasks:
        try:
            result = run_task_with_retry(task, max_retries=2)
            results[task] = result
            
            # 保存中间结果
            with open(os.path.join(config.RESULTS_DIR, "fixed_tasks_results.json"), 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)
            
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断修复过程")
            break
        except Exception as e:
            print(f"❌ 修复 {task} 时出现异常: {e}")
            results[task] = {'status': 'failed', 'error': str(e)}
    
    # 生成修复报告
    print(f"\n{'='*80}")
    print("🔧 修复结果总结")
    print(f"{'='*80}")
    
    # BERT-base基准结果
    bert_baseline = {
        "STS-B": 89.13,
        "MRPC": 89.00,
        "QNLI": 91.47,
        "WNLI": 56.34
    }
    
    print(f"{'任务':<8} {'状态':<10} {'我们的结果':<12} {'BERT基准':<12} {'差异':<10}")
    print("-" * 60)
    
    successful_fixes = 0
    
    for task in failed_tasks:
        if task in results:
            result = results[task]
            
            if result.get('status') == 'success':
                # 提取最终指标
                if 'training_history' in result and result['training_history']:
                    final_metrics = result['training_history'][-1]['eval_metrics']
                    
                    if task == "STS-B":
                        our_score = final_metrics.get('pearson_correlation', 0) * 100
                    elif task == "MRPC":
                        our_score = final_metrics.get('f1', 0) * 100
                    else:
                        our_score = final_metrics.get('accuracy', 0) * 100
                    
                    baseline = bert_baseline.get(task, 0)
                    diff = our_score - baseline
                    
                    status = "✅ 成功"
                    successful_fixes += 1
                    
                    print(f"{task:<8} {status:<10} {our_score:<12.2f} {baseline:<12.2f} {diff:+<10.2f}")
                else:
                    print(f"{task:<8} {'❓ 异常':<10} {'N/A':<12} {bert_baseline.get(task, 0):<12.2f} {'N/A':<10}")
            else:
                print(f"{task:<8} {'❌ 失败':<10} {'N/A':<12} {bert_baseline.get(task, 0):<12.2f} {'N/A':<10}")
        else:
            print(f"{task:<8} {'⚫ 未测':<10} {'N/A':<12} {bert_baseline.get(task, 0):<12.2f} {'N/A':<10}")
    
    print("-" * 60)
    print(f"修复成功: {successful_fixes}/{len(failed_tasks)}")
    
    # 显示具体的修复建议
    print(f"\n💡 进一步改进建议:")
    
    for task in failed_tasks:
        if task in results and results[task].get('status') == 'success':
            result = results[task]
            if 'training_history' in result:
                final_metrics = result['training_history'][-1]['eval_metrics']
                
                if task == "STS-B":
                    pearson = final_metrics.get('pearson_correlation', 0) * 100
                    if pearson < 80:
                        print(f"- {task}: 考虑使用更多训练数据或调整损失函数")
                elif task == "QNLI":
                    acc = final_metrics.get('accuracy', 0) * 100
                    if acc < 80:
                        print(f"- {task}: 可能需要更长的训练时间或更大的学习率")
                elif task == "WNLI":
                    acc = final_metrics.get('accuracy', 0) * 100
                    if acc < 50:
                        print(f"- {task}: 这是一个困难的任务，考虑使用更大的模型")
    
    # 保存最终报告
    final_report = {
        "fixed_results": results,
        "summary": {
            "successful_fixes": successful_fixes,
            "total_tasks": len(failed_tasks),
            "success_rate": successful_fixes / len(failed_tasks) * 100
        },
        "baseline": bert_baseline
    }
    
    with open(os.path.join(config.RESULTS_DIR, "task_fix_report.json"), 'w', encoding='utf-8') as f:
        json.dump(final_report, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n📁 修复报告已保存:")
    print(f"- 详细结果: results/fixed_tasks_results.json")
    print(f"- 修复报告: results/task_fix_report.json")
    
    if successful_fixes > 0:
        print(f"\n🎉 成功修复了 {successful_fixes} 个任务!")
        print(f"现在可以检查 results/ 目录中的提交文件")
    else:
        print(f"\n😔 没有成功修复任何任务，可能需要更深入的调试")

if __name__ == "__main__":
    main()
