"""
配置管理模块
"""
import json
import os
from pathlib import Path

class Config:
    """配置类"""
    
    def __init__(self, config_path="configs/config.json"):
        self.config_path = config_path
        self.config = self.load_config()
        
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_path):
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            return self.get_default_config()
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            "project": {
                "name": "Spark_GLUE_Local_Test",
                "version": "1.0.0"
            },
            "paths": {
                "data_root": "data/glue",
                "model_dir": "models",
                "results_dir": "results",
                "logs_dir": "logs"
            },
            "model": {
                "name": "distilbert-base-uncased",
                "max_length": 128,
                "batch_size": 8,
                "learning_rate": 2e-5,
                "num_epochs": 2,
                "warmup_steps": 100
            },
            "spark": {
                "app_name": "GLUE_Local_Test",
                "master": "local[*]",
                "driver_memory": "4g",
                "executor_memory": "2g"
            },
            "training": {
                "tasks": ["SST-2", "RTE"],
                "save_steps": 100,
                "eval_steps": 100,
                "logging_steps": 50,
                "seed": 42,
                "use_gpu": True
            }
        }
    
    def get(self, key_path, default=None):
        """获取配置值"""
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def get_model_config(self):
        """获取模型配置"""
        return self.config.get("model", {})
    
    def get_spark_config(self):
        """获取Spark配置"""
        return self.config.get("spark", {})
    
    def get_training_config(self):
        """获取训练配置"""
        return self.config.get("training", {})
    
    def get_paths(self):
        """获取路径配置"""
        return self.config.get("paths", {})
    
    def create_directories(self):
        """创建必要的目录"""
        paths = self.get_paths()
        
        for path_key, path_value in paths.items():
            if path_value:
                os.makedirs(path_value, exist_ok=True)
                print(f"✅ 创建目录: {path_value}")

# 全局配置实例
config = Config()
