2025-07-27 19:06:15,771 - utils - INFO - 日志已配置，日志文件: E:\spark文本分类\logs\glue_training_20250727_190615.log
2025-07-27 19:06:19,671 - main - INFO - Spark会话创建成功，版本: 3.5.0
2025-07-27 19:06:19,673 - main - INFO - GLUE基准测试系统初始化完成
2025-07-27 19:06:19,673 - main - INFO - 开始训练任务: CoLA
2025-07-27 19:06:19,673 - main - INFO - 加载训练和验证数据...
2025-07-27 19:06:19,673 - data_processor - INFO - 加载 CoLA 任务的 train 数据: E:\spark文本分类\glue\CoLA\train.tsv
2025-07-27 19:06:23,332 - data_processor - INFO - 加载 CoLA 任务的 dev 数据: E:\spark文本分类\glue\CoLA\dev.tsv
2025-07-27 19:06:24,076 - main - INFO - 数据加载完成: 训练集=3000, 验证集=600
2025-07-27 19:06:24,076 - main - INFO - 初始化模型...
2025-07-27 19:06:24,076 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:06:24,076 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:06:27,812 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:06:27,813 - model_manager - INFO - 为任务 CoLA 加载模型，标签数量: 2
2025-07-27 19:06:28,479 - model_manager - INFO - 模型加载成功:
2025-07-27 19:06:28,480 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:06:28,480 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:06:28,480 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:06:28,480 - trainer - INFO - 训练器初始化完成: 任务=CoLA, 输出目录=E:\spark文本分类\models\CoLA
2025-07-27 19:06:28,481 - trainer - INFO - 准备训练数据...
2025-07-27 19:06:28,721 - trainer - INFO - 数据准备完成: 训练集=3000, 验证集=600
2025-07-27 19:06:28,721 - main - INFO - 开始训练，参数: {'epochs': 2, 'learning_rate': 2e-05}
2025-07-27 19:06:28,721 - trainer - INFO - 开始训练: 轮数=2, 学习率=2e-05
2025-07-27 19:06:28,723 - model_manager - INFO - 优化器创建成功: lr=2e-05, weight_decay=0.01
2025-07-27 19:06:28,724 - model_manager - INFO - 调度器创建成功: warmup_steps=18, total_steps=188
2025-07-27 19:06:28,724 - trainer - INFO - 开始第 1/2 轮训练
2025-07-27 19:07:10,630 - trainer - INFO - Epoch 1/2:
2025-07-27 19:07:10,630 - trainer - INFO -   训练损失: 0.6062
2025-07-27 19:07:10,631 - trainer - INFO -   accuracy: 0.7417
2025-07-27 19:07:10,631 - trainer - INFO -   matthews_corrcoef: 0.3284
2025-07-27 19:07:10,632 - trainer - INFO -   eval_loss: 0.5412
2025-07-27 19:07:13,095 - model_manager - INFO - 模型已保存到: E:\spark文本分类\models\CoLA\best_model_hf
2025-07-27 19:07:13,095 - trainer - INFO - 保存最佳模型: matthews_corrcoef=0.3284
2025-07-27 19:07:13,096 - trainer - INFO - 开始第 2/2 轮训练
2025-07-27 19:07:15,856 - trainer - INFO - Step 100: loss=0.4871, progress=53.2%
2025-07-27 19:08:05,279 - trainer - INFO - Epoch 2/2:
2025-07-27 19:08:05,280 - trainer - INFO -   训练损失: 0.4498
2025-07-27 19:08:05,280 - trainer - INFO -   accuracy: 0.7717
2025-07-27 19:08:05,280 - trainer - INFO -   matthews_corrcoef: 0.4227
2025-07-27 19:08:05,280 - trainer - INFO -   eval_loss: 0.5343
2025-07-27 19:08:06,966 - model_manager - INFO - 模型已保存到: E:\spark文本分类\models\CoLA\best_model_hf
2025-07-27 19:08:06,966 - trainer - INFO - 保存最佳模型: matthews_corrcoef=0.4227
2025-07-27 19:08:06,966 - trainer - INFO - 训练完成! 总时间: 1m 38s
2025-07-27 19:08:06,967 - trainer - INFO - 训练历史已保存: E:\spark文本分类\models\CoLA\training_history.json
2025-07-27 19:08:06,968 - main - INFO - 任务 CoLA 训练完成!
2025-07-27 19:08:06,968 - main - INFO - 最佳指标: 0.4227
2025-07-27 19:08:06,968 - main - INFO - 训练时间: 98.24秒
2025-07-27 19:08:06,971 - main - INFO - 开始预测任务: CoLA
2025-07-27 19:08:06,971 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:08:06,971 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:08:13,628 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:08:13,628 - model_manager - INFO - 为任务 CoLA 加载模型，标签数量: 2
2025-07-27 19:08:14,373 - model_manager - INFO - 模型加载成功:
2025-07-27 19:08:14,373 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:08:14,374 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:08:14,374 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:08:14,374 - main - INFO - 使用预训练模型进行预测
2025-07-27 19:08:14,374 - main - INFO - 加载测试数据...
2025-07-27 19:08:14,374 - data_processor - INFO - 加载 CoLA 任务的 test 数据: E:\spark文本分类\glue\CoLA\test.tsv
2025-07-27 19:08:14,625 - main - INFO - 测试数据加载完成: 1063 样本
2025-07-27 19:08:14,626 - predictor - INFO - 预测器初始化完成: 任务=CoLA, 设备=cuda
2025-07-27 19:08:14,712 - main - INFO - 准备预测文本: 1063 个样本
2025-07-27 19:08:20,052 - predictor - INFO - 提交文件已生成: E:\spark文本分类\results\CoLA_submission.tsv
2025-07-27 19:08:20,052 - main - INFO - 任务 CoLA 预测完成!
2025-07-27 19:08:20,052 - main - INFO - 提交文件: E:\spark文本分类\results\CoLA_submission.tsv
2025-07-27 19:08:20,177 - main - INFO - Spark会话已关闭
2025-07-27 19:08:20,324 - main - INFO - Spark会话创建成功，版本: 3.5.0
2025-07-27 19:08:20,325 - main - INFO - GLUE基准测试系统初始化完成
2025-07-27 19:08:20,325 - main - INFO - 开始训练任务: RTE
2025-07-27 19:08:20,325 - main - INFO - 加载训练和验证数据...
2025-07-27 19:08:20,326 - data_processor - INFO - 加载 RTE 任务的 train 数据: E:\spark文本分类\glue\RTE\train.tsv
2025-07-27 19:08:20,510 - data_processor - INFO - 加载 RTE 任务的 dev 数据: E:\spark文本分类\glue\RTE\dev.tsv
2025-07-27 19:08:20,929 - main - INFO - 数据加载完成: 训练集=2490, 验证集=277
2025-07-27 19:08:20,930 - main - INFO - 初始化模型...
2025-07-27 19:08:20,930 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:08:20,930 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:08:22,292 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:08:22,292 - model_manager - INFO - 为任务 RTE 加载模型，标签数量: 2
2025-07-27 19:08:22,884 - model_manager - INFO - 模型加载成功:
2025-07-27 19:08:22,885 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:08:22,885 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:08:22,885 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:08:22,885 - trainer - INFO - 训练器初始化完成: 任务=RTE, 输出目录=E:\spark文本分类\models\RTE
2025-07-27 19:08:22,885 - trainer - INFO - 准备训练数据...
2025-07-27 19:08:23,202 - trainer - INFO - 数据准备完成: 训练集=2490, 验证集=277
2025-07-27 19:08:23,203 - main - INFO - 开始训练，参数: {'epochs': 2, 'learning_rate': 2e-05}
2025-07-27 19:08:23,203 - trainer - INFO - 开始训练: 轮数=2, 学习率=2e-05
2025-07-27 19:08:23,204 - model_manager - INFO - 优化器创建成功: lr=2e-05, weight_decay=0.01
2025-07-27 19:08:23,204 - model_manager - INFO - 调度器创建成功: warmup_steps=15, total_steps=156
2025-07-27 19:08:23,204 - trainer - INFO - 开始第 1/2 轮训练
2025-07-27 19:09:10,268 - trainer - INFO - Epoch 1/2:
2025-07-27 19:09:10,268 - trainer - INFO -   训练损失: 0.6802
2025-07-27 19:09:10,268 - trainer - INFO -   accuracy: 0.6173
2025-07-27 19:09:10,269 - trainer - INFO -   f1: 0.6919
2025-07-27 19:09:10,269 - trainer - INFO -   eval_loss: 0.6472
2025-07-27 19:09:11,912 - model_manager - INFO - 模型已保存到: E:\spark文本分类\models\RTE\best_model_hf
2025-07-27 19:09:11,913 - trainer - INFO - 保存最佳模型: accuracy=0.6173
2025-07-27 19:09:11,913 - trainer - INFO - 开始第 2/2 轮训练
2025-07-27 19:09:25,371 - trainer - INFO - Step 100: loss=0.5947, progress=64.1%
2025-07-27 19:10:01,909 - trainer - INFO - Epoch 2/2:
2025-07-27 19:10:01,909 - trainer - INFO -   训练损失: 0.5720
2025-07-27 19:10:01,909 - trainer - INFO -   accuracy: 0.6498
2025-07-27 19:10:01,909 - trainer - INFO -   f1: 0.6940
2025-07-27 19:10:01,909 - trainer - INFO -   eval_loss: 0.6337
2025-07-27 19:10:03,604 - model_manager - INFO - 模型已保存到: E:\spark文本分类\models\RTE\best_model_hf
2025-07-27 19:10:03,604 - trainer - INFO - 保存最佳模型: accuracy=0.6498
2025-07-27 19:10:03,605 - trainer - INFO - 训练完成! 总时间: 1m 40s
2025-07-27 19:10:03,605 - trainer - INFO - 训练历史已保存: E:\spark文本分类\models\RTE\training_history.json
2025-07-27 19:10:03,606 - main - INFO - 任务 RTE 训练完成!
2025-07-27 19:10:03,606 - main - INFO - 最佳指标: 0.6498
2025-07-27 19:10:03,606 - main - INFO - 训练时间: 100.40秒
2025-07-27 19:10:03,609 - main - INFO - 开始预测任务: RTE
2025-07-27 19:10:03,609 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:10:03,609 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:10:06,628 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:10:06,628 - model_manager - INFO - 为任务 RTE 加载模型，标签数量: 2
2025-07-27 19:10:07,450 - model_manager - INFO - 模型加载成功:
2025-07-27 19:10:07,450 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:10:07,451 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:10:07,451 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:10:07,451 - main - INFO - 使用预训练模型进行预测
2025-07-27 19:10:07,452 - main - INFO - 加载测试数据...
2025-07-27 19:10:07,452 - data_processor - INFO - 加载 RTE 任务的 test 数据: E:\spark文本分类\glue\RTE\test.tsv
2025-07-27 19:10:07,743 - main - INFO - 测试数据加载完成: 2999 样本
2025-07-27 19:10:07,743 - predictor - INFO - 预测器初始化完成: 任务=RTE, 设备=cuda
2025-07-27 19:10:07,970 - main - INFO - 准备预测文本: 2999 个样本
2025-07-27 19:10:26,787 - predictor - INFO - 提交文件已生成: E:\spark文本分类\results\RTE_submission.tsv
2025-07-27 19:10:26,787 - main - INFO - 任务 RTE 预测完成!
2025-07-27 19:10:26,788 - main - INFO - 提交文件: E:\spark文本分类\results\RTE_submission.tsv
2025-07-27 19:10:27,003 - main - INFO - Spark会话已关闭
2025-07-27 19:10:27,141 - main - INFO - Spark会话创建成功，版本: 3.5.0
2025-07-27 19:10:27,142 - main - INFO - GLUE基准测试系统初始化完成
2025-07-27 19:10:27,142 - main - INFO - 开始训练任务: WNLI
2025-07-27 19:10:27,142 - main - INFO - 加载训练和验证数据...
2025-07-27 19:10:27,143 - data_processor - INFO - 加载 WNLI 任务的 train 数据: E:\spark文本分类\glue\WNLI\train.tsv
2025-07-27 19:10:27,281 - data_processor - INFO - 加载 WNLI 任务的 dev 数据: E:\spark文本分类\glue\WNLI\dev.tsv
2025-07-27 19:10:27,663 - main - INFO - 数据加载完成: 训练集=635, 验证集=71
2025-07-27 19:10:27,664 - main - INFO - 初始化模型...
2025-07-27 19:10:27,664 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:10:27,664 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:10:28,711 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:10:28,711 - model_manager - INFO - 为任务 WNLI 加载模型，标签数量: 2
2025-07-27 19:10:29,359 - model_manager - INFO - 模型加载成功:
2025-07-27 19:10:29,360 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:10:29,360 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:10:29,360 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:10:29,360 - trainer - INFO - 训练器初始化完成: 任务=WNLI, 输出目录=E:\spark文本分类\models\WNLI
2025-07-27 19:10:29,360 - trainer - INFO - 准备训练数据...
2025-07-27 19:10:29,526 - trainer - INFO - 数据准备完成: 训练集=635, 验证集=71
2025-07-27 19:10:29,527 - main - INFO - 开始训练，参数: {'epochs': 2, 'learning_rate': 2e-05}
2025-07-27 19:10:29,527 - trainer - INFO - 开始训练: 轮数=2, 学习率=2e-05
2025-07-27 19:10:29,528 - model_manager - INFO - 优化器创建成功: lr=2e-05, weight_decay=0.01
2025-07-27 19:10:29,529 - model_manager - INFO - 调度器创建成功: warmup_steps=4, total_steps=40
2025-07-27 19:10:29,529 - trainer - INFO - 开始第 1/2 轮训练
2025-07-27 19:10:42,292 - trainer - INFO - Epoch 1/2:
2025-07-27 19:10:42,293 - trainer - INFO -   训练损失: 0.7004
2025-07-27 19:10:42,293 - trainer - INFO -   accuracy: 0.4366
2025-07-27 19:10:42,294 - trainer - INFO -   f1: 0.2857
2025-07-27 19:10:42,294 - trainer - INFO -   eval_loss: 0.7056
2025-07-27 19:10:43,809 - model_manager - INFO - 模型已保存到: E:\spark文本分类\models\WNLI\best_model_hf
2025-07-27 19:10:43,810 - trainer - INFO - 保存最佳模型: accuracy=0.4366
2025-07-27 19:10:43,810 - trainer - INFO - 开始第 2/2 轮训练
2025-07-27 19:10:56,671 - trainer - INFO - Epoch 2/2:
2025-07-27 19:10:56,671 - trainer - INFO -   训练损失: 0.7003
2025-07-27 19:10:56,672 - trainer - INFO -   accuracy: 0.3662
2025-07-27 19:10:56,672 - trainer - INFO -   f1: 0.2105
2025-07-27 19:10:56,672 - trainer - INFO -   eval_loss: 0.7047
2025-07-27 19:10:56,672 - trainer - INFO - 训练完成! 总时间: 27s
2025-07-27 19:10:56,673 - trainer - INFO - 训练历史已保存: E:\spark文本分类\models\WNLI\training_history.json
2025-07-27 19:10:56,673 - main - INFO - 任务 WNLI 训练完成!
2025-07-27 19:10:56,673 - main - INFO - 最佳指标: 0.4366
2025-07-27 19:10:56,673 - main - INFO - 训练时间: 27.14秒
2025-07-27 19:10:56,676 - main - INFO - 开始预测任务: WNLI
2025-07-27 19:10:56,676 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:10:56,676 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:10:57,890 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:10:57,891 - model_manager - INFO - 为任务 WNLI 加载模型，标签数量: 2
2025-07-27 19:10:58,638 - model_manager - INFO - 模型加载成功:
2025-07-27 19:10:58,638 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:10:58,638 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:10:58,639 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:10:58,639 - main - INFO - 使用预训练模型进行预测
2025-07-27 19:10:58,639 - main - INFO - 加载测试数据...
2025-07-27 19:10:58,639 - data_processor - INFO - 加载 WNLI 任务的 test 数据: E:\spark文本分类\glue\WNLI\test.tsv
2025-07-27 19:10:58,841 - main - INFO - 测试数据加载完成: 146 样本
2025-07-27 19:10:58,844 - predictor - INFO - 预测器初始化完成: 任务=WNLI, 设备=cuda
2025-07-27 19:10:58,903 - main - INFO - 准备预测文本: 146 个样本
2025-07-27 19:10:59,712 - predictor - INFO - 提交文件已生成: E:\spark文本分类\results\WNLI_submission.tsv
2025-07-27 19:10:59,713 - main - INFO - 任务 WNLI 预测完成!
2025-07-27 19:10:59,713 - main - INFO - 提交文件: E:\spark文本分类\results\WNLI_submission.tsv
2025-07-27 19:11:00,207 - main - INFO - Spark会话已关闭
2025-07-27 19:11:00,345 - main - INFO - Spark会话创建成功，版本: 3.5.0
2025-07-27 19:11:00,347 - main - INFO - GLUE基准测试系统初始化完成
2025-07-27 19:11:00,347 - main - INFO - 开始训练任务: MRPC
2025-07-27 19:11:00,347 - main - INFO - 加载训练和验证数据...
2025-07-27 19:11:00,348 - data_processor - INFO - 加载 MRPC 任务的 train 数据: E:\spark文本分类\glue\MRPC\msr_paraphrase_train.txt
2025-07-27 19:11:00,456 - data_processor - INFO - 加载 MRPC 任务的 dev 数据: E:\spark文本分类\glue\MRPC\msr_paraphrase_test.txt
2025-07-27 19:11:00,748 - main - INFO - 数据加载完成: 训练集=3000, 验证集=600
2025-07-27 19:11:00,749 - main - INFO - 初始化模型...
2025-07-27 19:11:00,749 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:11:00,749 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:11:01,702 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:11:01,703 - model_manager - INFO - 为任务 MRPC 加载模型，标签数量: 2
2025-07-27 19:11:02,283 - model_manager - INFO - 模型加载成功:
2025-07-27 19:11:02,283 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:11:02,283 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:11:02,283 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:11:02,283 - trainer - INFO - 训练器初始化完成: 任务=MRPC, 输出目录=E:\spark文本分类\models\MRPC
2025-07-27 19:11:02,285 - trainer - INFO - 准备训练数据...
2025-07-27 19:11:02,392 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,392 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,392 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,393 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,393 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,393 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,393 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,393 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,393 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,394 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,394 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,394 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,394 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,394 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,394 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,396 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,396 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,396 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,396 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,396 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,397 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,397 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,397 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,397 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,397 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,397 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,397 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,399 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,399 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,399 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,399 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,399 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,400 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,400 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,400 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,400 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,401 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,401 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,401 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,401 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,401 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,401 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,401 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,401 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,401 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,403 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,403 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,403 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,403 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,403 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,403 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,403 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,404 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,404 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,404 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,404 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,404 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,404 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,406 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,406 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,406 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,406 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,406 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,407 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,407 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,407 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,407 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,407 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,407 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,407 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,408 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,408 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,408 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,408 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,408 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,408 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,409 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,409 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,409 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,409 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,409 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,410 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,410 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,410 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,410 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,411 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,411 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,411 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,411 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,411 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,412 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,412 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,412 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,412 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,412 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,412 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,415 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,415 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,415 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,415 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,415 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,416 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,416 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,416 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,416 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,417 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,417 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,417 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,417 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,417 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,417 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,417 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,419 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,419 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,419 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,419 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,419 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,420 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,420 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,420 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,420 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,420 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,420 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,420 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,421 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,421 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,421 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,421 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,421 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,421 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,422 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,422 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,422 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,422 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,422 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,422 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,422 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,424 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,424 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,424 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,424 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,424 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,424 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,424 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,425 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,425 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,425 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,425 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,425 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,425 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,426 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,426 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,426 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,426 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,426 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,426 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,427 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,427 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,427 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,427 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,427 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,427 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,428 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,428 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,428 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,428 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,428 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,428 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,429 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,429 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,429 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,429 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,429 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,429 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,430 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,430 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,430 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,430 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,430 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,430 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,431 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,431 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,431 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,431 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,431 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,431 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,432 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,432 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,432 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,432 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,432 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,432 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,433 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,433 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,433 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,433 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,434 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,434 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,434 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,434 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,434 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,434 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,435 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,435 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,435 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,435 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,435 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,436 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,436 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,436 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,436 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,436 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,436 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,441 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,441 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,441 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,441 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,441 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,441 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,441 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,444 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,444 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,444 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,447 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,447 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,447 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,447 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,447 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,447 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,447 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,448 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,448 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,448 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,448 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,448 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,452 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,452 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,452 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,452 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,452 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,453 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,453 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,453 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,453 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,453 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,453 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,454 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,454 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,454 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,454 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,456 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,456 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,456 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,456 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,456 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,457 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,457 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,457 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,457 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,457 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,459 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,459 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,459 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,460 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,460 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,460 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,460 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,460 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,460 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,464 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,464 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,464 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,464 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,464 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,464 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,464 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,465 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,465 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,465 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,465 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,465 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,465 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,467 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,467 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,467 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,467 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,467 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,467 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,470 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,470 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,470 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,470 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,470 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,471 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,471 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,471 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,471 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,471 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,471 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,471 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,473 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,473 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,473 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,473 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,473 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,473 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,474 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,474 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,474 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,474 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,474 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,474 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,474 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,474 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,475 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,475 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,475 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,475 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,475 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,475 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,475 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,475 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,475 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,476 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,476 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,476 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,476 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,476 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,476 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,476 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,477 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,477 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,477 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,477 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,477 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,477 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,478 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,478 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,478 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,478 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,478 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,478 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,478 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,480 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,480 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,480 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,480 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,480 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,480 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,480 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,480 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,480 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,480 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,481 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,481 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,481 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,481 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,481 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,482 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,482 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,482 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,482 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,482 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,482 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,482 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,482 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,483 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,483 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,483 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,483 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,483 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,483 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,483 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,483 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,483 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,483 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,485 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,485 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,485 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,485 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,485 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,485 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,485 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,486 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,486 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,486 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,486 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,486 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,486 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,486 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,486 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,486 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,487 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,487 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,487 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,487 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,487 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,487 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,487 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,487 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,487 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,488 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,488 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,488 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,488 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,488 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,488 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,489 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,489 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,489 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,489 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,489 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,489 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,490 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,490 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,490 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,490 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,490 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,490 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,490 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,490 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,490 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,491 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,491 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,491 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,491 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,491 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,491 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,491 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,492 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,492 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,492 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,492 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,492 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,492 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,492 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,492 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,492 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,492 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,493 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,493 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,493 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,493 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,493 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,493 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,493 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,493 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,494 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,494 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,494 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,494 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,495 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,495 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,495 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,495 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,495 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,495 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,495 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,495 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,496 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,496 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,496 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,496 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,496 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,496 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,497 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,497 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,497 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,497 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,497 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,497 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,497 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,498 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,498 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,498 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,498 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,498 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,498 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,499 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,499 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,499 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,499 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,499 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,499 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,500 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,500 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,500 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,500 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,500 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,500 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,501 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,501 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,502 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,502 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,502 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,502 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,502 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,502 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,502 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,502 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,502 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,503 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,503 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,503 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,503 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,503 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,503 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,503 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,503 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,503 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,503 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,505 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,505 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,505 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,505 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,505 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,505 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,505 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,506 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,506 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,506 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,506 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,506 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,506 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,506 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,506 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,507 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,507 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,507 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,507 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,507 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,507 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,507 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,508 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,508 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,508 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,508 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,508 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,508 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,508 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,508 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,508 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,508 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,508 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,508 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,508 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,510 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,510 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,510 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,510 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,510 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,510 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,514 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,514 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,514 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,514 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,514 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,514 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,514 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,514 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,514 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,516 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,516 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,516 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,516 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,516 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,516 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,516 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,516 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,516 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,519 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,519 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,519 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,519 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,519 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,519 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,519 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,519 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,524 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,524 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,524 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,524 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,524 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,524 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,524 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,524 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,524 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,524 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,526 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,526 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,526 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,526 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,526 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,526 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,526 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,526 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,526 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,526 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,526 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,527 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,527 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,527 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,527 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,527 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,527 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,527 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,527 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,527 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,528 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,528 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,528 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,528 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,528 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,528 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,528 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,528 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,528 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,529 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,529 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,529 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,529 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,529 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,529 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,529 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,529 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,530 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,530 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,530 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,530 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,530 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,530 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,530 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,531 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,531 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,531 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,531 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,531 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,531 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,531 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,532 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,532 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,532 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,532 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,532 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,532 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,532 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,532 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,532 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,532 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,532 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,532 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,533 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,533 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,533 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,533 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,533 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,533 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,533 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,533 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,533 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,533 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,533 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,533 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,533 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,533 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,533 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,535 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,535 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,535 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,535 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,535 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,535 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,535 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,535 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,536 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,536 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,536 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,536 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,536 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,536 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,536 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,537 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,537 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,537 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,537 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,537 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,537 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,537 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,537 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,538 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,538 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,538 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,538 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,539 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,539 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,539 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,539 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,539 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,539 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,540 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,540 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,540 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,540 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,540 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,540 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,540 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,540 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,540 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,540 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,540 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,540 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,540 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,540 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,541 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,541 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,541 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,541 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,541 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,541 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,541 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,541 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,541 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,541 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,541 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,541 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,541 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,541 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,541 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,541 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,542 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,542 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,542 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,542 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,542 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,542 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,542 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,543 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,543 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,543 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,543 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,543 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,543 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,543 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,544 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,544 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,544 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,544 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,545 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,545 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,545 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,545 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,545 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,545 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,545 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,545 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,546 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,546 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,546 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,546 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,546 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,546 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,546 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,546 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,546 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,546 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,546 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,546 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,546 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,546 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,546 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,546 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,546 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,547 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,547 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,547 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,547 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,547 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,547 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,547 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,547 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,547 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,548 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,548 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,548 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,549 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,549 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,549 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,549 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,549 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,550 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,550 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,550 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,551 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,551 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,551 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,551 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,551 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,551 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,551 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,551 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,551 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,552 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,552 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,552 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,552 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,552 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,552 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,552 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,553 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,553 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,553 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,553 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,553 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,553 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,553 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,553 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,553 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,553 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,553 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,554 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,554 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,554 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,554 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,554 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,554 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,554 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,554 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,554 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,556 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,556 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,556 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,556 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,556 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,556 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,556 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,557 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,557 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,557 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,557 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,557 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,557 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,557 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,558 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,558 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,558 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,558 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,558 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,558 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,558 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,559 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,559 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,559 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,559 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,559 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,559 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,559 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,560 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,560 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,560 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,560 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,560 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,560 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,560 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,560 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,560 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,560 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,561 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,561 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,561 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,561 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,561 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,561 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,561 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,561 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,562 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,562 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,562 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,562 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,562 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,562 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,562 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,562 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,562 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,562 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,562 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,563 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,563 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,563 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,563 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,563 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,564 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,564 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,564 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,564 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,564 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,565 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,565 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,565 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,565 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,565 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,565 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,565 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,565 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,565 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,566 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,566 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,566 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,566 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,566 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,566 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,566 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,566 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,566 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,567 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,567 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,567 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,567 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,567 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,567 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,567 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,567 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,567 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,567 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,567 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,568 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,568 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,568 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,569 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,569 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,569 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,569 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,569 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,569 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,569 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,570 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,570 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,570 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,570 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,570 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,570 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,570 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,570 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,570 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,570 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,570 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,570 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,570 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,570 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,571 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,571 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,571 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,571 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,571 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,571 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,571 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,571 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,571 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,571 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,571 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,571 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,571 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,572 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,572 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,572 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,572 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,572 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,572 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,572 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,574 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,574 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,574 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,574 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,574 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,574 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,575 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,575 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,575 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,576 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,576 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,576 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,576 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,576 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,576 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,576 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,576 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,576 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,577 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,577 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,577 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,577 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,577 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,577 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,577 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,577 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,577 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,577 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,577 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,578 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,578 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,578 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,578 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,578 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,578 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,578 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,578 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,578 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,578 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,579 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,579 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,580 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,580 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,581 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,581 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,581 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,581 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,582 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,582 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,582 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,582 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,582 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,582 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,583 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,583 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,583 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,583 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,583 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,583 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,583 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,583 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,583 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,583 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,583 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,583 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,583 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,585 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,585 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,585 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,585 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,585 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,585 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,585 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,585 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,585 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,586 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,586 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,586 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,586 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,586 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,586 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,587 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,587 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,587 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,587 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,587 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,587 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,587 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,588 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,588 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,588 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,588 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,588 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,588 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,588 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,588 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,588 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,588 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,588 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,588 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,589 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,589 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,589 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,589 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,589 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,589 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,589 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,589 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,589 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,589 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,589 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,589 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,590 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,590 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,590 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,590 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,590 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,590 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,590 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,590 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,591 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,591 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,591 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,591 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,591 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,591 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,591 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,592 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,592 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,592 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,592 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,592 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,593 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,593 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,593 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,593 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,594 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,594 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,595 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,595 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,595 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,595 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,595 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,595 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,595 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,595 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,595 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,595 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,596 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,596 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,596 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,596 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,596 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,597 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,597 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,598 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,598 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,598 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,598 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,598 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,600 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,600 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,600 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,600 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,600 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,601 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,601 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,601 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,601 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,601 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,601 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,602 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,602 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,602 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,602 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,602 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,602 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,603 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,603 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,603 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,603 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,603 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,603 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,603 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,603 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,603 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,604 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,604 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,604 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,604 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,606 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,606 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,606 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,606 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,606 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,607 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,607 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,607 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,607 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,608 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,608 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,608 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,608 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,608 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,608 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,609 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,609 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,609 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,609 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,609 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,610 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,610 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,610 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,610 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,611 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,611 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,611 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,611 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,611 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,611 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,612 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,612 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,612 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,612 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,613 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,613 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,613 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,613 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,613 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,613 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,613 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,613 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,614 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,614 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,614 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,614 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,614 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,614 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,614 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,614 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,614 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,615 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,615 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,615 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,615 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,615 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,615 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,615 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,617 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,617 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,617 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,617 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,617 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,617 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,617 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,617 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,617 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,618 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,619 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,619 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,619 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,619 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,619 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,619 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,619 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,619 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,619 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,619 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,619 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,619 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,619 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,619 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,619 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,619 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,620 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,620 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,620 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,620 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,620 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,620 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,620 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,620 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,620 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,620 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,622 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,622 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,622 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,622 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,622 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,622 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,622 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,623 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,623 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,623 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,623 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,623 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,623 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,623 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,623 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,623 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,624 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,624 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,624 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,624 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,624 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,624 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,624 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,624 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,624 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,624 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,624 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,625 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,625 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,625 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,625 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,625 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,625 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,625 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,625 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,625 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,625 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,627 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,627 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,627 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,627 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,627 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,627 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,628 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,628 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,628 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,628 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,629 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,629 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,629 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,629 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,629 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,629 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,629 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,629 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,630 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,630 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,630 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,630 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,630 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,630 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,630 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,630 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,630 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,630 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,630 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,630 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,630 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,630 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,630 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,630 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,630 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,631 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,631 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,631 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,631 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,631 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,631 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,631 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,631 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,631 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,631 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,632 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,632 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,632 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,632 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,632 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,632 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,632 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,632 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,633 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,633 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,633 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,633 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,633 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,634 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,634 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,635 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,635 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,636 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,636 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,636 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,636 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,636 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,636 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,636 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,636 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,636 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,636 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,636 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,636 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,636 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,637 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,637 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,637 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,637 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,637 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,637 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,637 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,637 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,637 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,637 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,637 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,637 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,637 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,638 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,638 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,638 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,638 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,638 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,638 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,638 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,638 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,638 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,638 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,638 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,638 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,638 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,638 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,638 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,638 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,639 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,639 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,639 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,639 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,639 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,639 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,639 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,639 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,639 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,639 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,640 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,640 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,640 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,641 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,641 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,641 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,641 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,641 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,641 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,641 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,641 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,643 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,643 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,643 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,643 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,644 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,644 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,644 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,644 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,644 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,644 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,644 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,644 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,644 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,644 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,645 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,645 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,645 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,645 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,645 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,645 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,645 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,645 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,645 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,646 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,646 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,646 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,646 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,646 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,646 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,647 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,647 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,647 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,647 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,647 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,647 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,648 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,648 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,648 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,648 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,648 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,648 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,649 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,649 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,649 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,649 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,649 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,649 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,649 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,649 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,649 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,649 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,650 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,650 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,650 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,650 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,650 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,650 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,651 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,651 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,651 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,651 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,651 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,651 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,652 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,652 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,652 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,652 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,654 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,654 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,654 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,654 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,654 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,655 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,655 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,655 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,655 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,655 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,655 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,655 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,655 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,655 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,655 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,655 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,655 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,655 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,655 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,655 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,655 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,655 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,655 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,656 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,656 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,656 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,656 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,656 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,657 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,657 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,657 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,657 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,657 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,657 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,657 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,657 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,657 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,658 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,658 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,658 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,658 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,658 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,658 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,658 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,658 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,659 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,659 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,659 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,659 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,659 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,659 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,659 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,659 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,659 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,659 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,660 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,660 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,660 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,660 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,660 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,660 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,660 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,660 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,660 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,660 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,660 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,660 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,660 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,660 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,660 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,660 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,661 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,661 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,661 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,661 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,661 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,661 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,661 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,661 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,662 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,662 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,662 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,662 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,662 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,662 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,662 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,662 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,662 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,663 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,663 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,663 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,663 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,663 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,663 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,664 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,664 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,665 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,665 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,665 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,666 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,666 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,666 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,666 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,666 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,666 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,668 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,668 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,668 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,668 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,668 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,668 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,668 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,668 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,668 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,669 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,669 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,669 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,669 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,669 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,669 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,670 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,670 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,670 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,670 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,670 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,671 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,671 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,671 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,671 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,671 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,672 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,672 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,672 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,672 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,672 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,672 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,672 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,673 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,673 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,673 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,673 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,673 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,673 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,673 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,673 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,673 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,675 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,675 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,675 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,675 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,675 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,675 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,675 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,676 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,676 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,676 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,676 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,676 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,676 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,676 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,676 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,677 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,677 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,677 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,677 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,677 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,677 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,677 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,677 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,677 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,677 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,677 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,677 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,678 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,678 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,678 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,678 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,678 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,678 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,678 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,679 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,679 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,679 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,679 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,679 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,679 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,679 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,680 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,680 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,680 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,680 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,680 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,680 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,680 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,680 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,681 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,681 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,681 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,681 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,681 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,681 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,681 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,681 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,681 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,682 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,682 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,682 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,682 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,682 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,682 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,682 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,682 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,684 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,684 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,684 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,684 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,684 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,684 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,684 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,685 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,686 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,686 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,686 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,686 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,686 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,686 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,686 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,686 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,686 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,686 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,686 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,686 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,686 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,686 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,686 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,686 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,686 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,686 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,687 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,687 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,687 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,687 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,687 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,687 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,687 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,687 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,687 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,688 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,688 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,689 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,689 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,689 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,689 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,689 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,691 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,691 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,691 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,691 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,691 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,692 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,692 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,692 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,692 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,692 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,692 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,692 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,692 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,693 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,693 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,693 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,693 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,693 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,693 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,693 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,693 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,693 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,693 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,693 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,694 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,694 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,694 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,694 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,694 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,694 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,694 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,696 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,696 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,696 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,696 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,696 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,696 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,697 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,697 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,697 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,697 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,697 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,697 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,697 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,698 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,698 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,698 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,698 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,698 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,698 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,698 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,699 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,699 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,699 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,699 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,699 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,699 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,699 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,700 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,700 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,700 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,700 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,700 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,700 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,701 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,701 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,701 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,701 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,701 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,701 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,702 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,702 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,702 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,702 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,702 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,702 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,702 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,702 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,702 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,702 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,702 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,702 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,703 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,703 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,703 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,703 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,703 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,703 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,703 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,703 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,703 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,703 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,703 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,703 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,704 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,704 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,704 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,704 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,704 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,704 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,704 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,704 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,705 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,705 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,705 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,705 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,705 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,705 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,705 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,705 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,706 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,706 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,706 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,706 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,706 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,706 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,708 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,708 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,708 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,708 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,709 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,709 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,709 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,709 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,709 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,709 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,709 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,710 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,710 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,710 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,710 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,710 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,710 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,710 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,710 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,710 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,710 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,710 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,710 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,710 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,711 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,711 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,711 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,711 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,711 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,711 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,711 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,711 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,711 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,711 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,711 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,712 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,712 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,712 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,712 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,712 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,712 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,713 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,713 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,713 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,713 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,713 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,713 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,714 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,714 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,714 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,714 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,714 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,714 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,715 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,715 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,715 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,715 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,715 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,715 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,716 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,716 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,716 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,716 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,716 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,716 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,716 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,716 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,716 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,716 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,716 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,716 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,717 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,717 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,717 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,717 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,717 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,717 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,717 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,717 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,718 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,718 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,718 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,718 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,719 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,719 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,719 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,719 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,719 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,721 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,721 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,722 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,722 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,722 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,722 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,722 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,722 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,722 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,722 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,722 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,723 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,723 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,723 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,723 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,723 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,723 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,723 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,723 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,723 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,723 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,723 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,723 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,723 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,723 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,723 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,723 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,723 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,723 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,724 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,724 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,724 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,724 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,724 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,724 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,724 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,726 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,726 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,726 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,726 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,726 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,726 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,726 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,726 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,727 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,727 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,727 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,727 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,727 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,727 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,727 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,728 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,728 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,728 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,728 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,728 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,728 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,728 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,728 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,728 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,728 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,728 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,729 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,729 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,729 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,729 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,729 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,729 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,729 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,729 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,729 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,729 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,729 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,729 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,729 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,729 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,729 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,729 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,729 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,730 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,730 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,730 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,730 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,730 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,730 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,730 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,730 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,730 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,731 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,731 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,731 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,731 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,731 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,731 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,732 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,732 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,732 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,732 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,732 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,732 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,732 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,732 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,732 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,732 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,732 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,732 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,733 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,733 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,733 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,733 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,733 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,733 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,733 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,733 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,733 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,733 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,733 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,733 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,733 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,733 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,733 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,733 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,734 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,734 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,734 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,734 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,734 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,734 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,734 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,734 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,736 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,736 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,736 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,736 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,736 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,737 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,737 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,737 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,738 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,738 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,738 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,738 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,739 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,739 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,739 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,739 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,739 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,740 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,740 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,740 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,740 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,740 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,740 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,740 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,741 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,741 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,741 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,741 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,741 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,741 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,741 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,742 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,742 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,742 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,742 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,742 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,742 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,743 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,743 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,743 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,743 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,743 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,743 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,743 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,744 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,744 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,744 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,744 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,744 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,744 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,744 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,744 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,744 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,744 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,744 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,744 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,744 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,744 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,745 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,745 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,745 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,745 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,745 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,745 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,745 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,745 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,745 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,745 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,745 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,745 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,745 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,745 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,746 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,746 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,746 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,746 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,746 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,746 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,746 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,746 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,746 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,746 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,746 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,746 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,746 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,746 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,746 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,746 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,747 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,747 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,747 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,747 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,747 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,747 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,747 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,747 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,747 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,747 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,748 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,748 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,748 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,748 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,748 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,748 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,748 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,748 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,748 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,750 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,750 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,750 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,750 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,750 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,750 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,751 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,751 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,751 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,751 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,751 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,752 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,752 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,752 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,752 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,752 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,752 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,752 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,752 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,752 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,752 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,752 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,752 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,752 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,752 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,753 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,753 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,753 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,753 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,753 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,753 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,753 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,753 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,753 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,753 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,753 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,753 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,753 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,753 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,753 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,755 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,755 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,755 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,755 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,755 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,755 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,755 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,755 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,755 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,756 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,756 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,756 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,756 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,756 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,757 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,757 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,757 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,757 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,757 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,757 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,758 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,758 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,758 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,758 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,758 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,758 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,758 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,759 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,759 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,759 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,759 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,759 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,759 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,759 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,760 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,760 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,760 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,760 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,760 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,760 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,760 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,761 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,761 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,761 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,761 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,762 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,762 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,763 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,763 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,763 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,763 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,763 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,763 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,763 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,763 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,763 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,763 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,763 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,763 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,763 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,763 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,763 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,763 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,764 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,764 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,764 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,764 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,764 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,764 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,765 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,765 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,765 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,766 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,766 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,766 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,766 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,766 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,766 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,766 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,766 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,767 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,767 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,767 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,767 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,767 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,767 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,769 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,769 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,769 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,769 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,769 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,769 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,770 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,770 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,770 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,770 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,770 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,770 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,770 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,771 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,771 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,771 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,771 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,771 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,771 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,771 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,771 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,772 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,772 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,772 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,772 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,772 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,772 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,772 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,772 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,773 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,773 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,773 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,773 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,773 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,773 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,773 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,773 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,773 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,773 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,774 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,774 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,774 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,774 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,774 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,774 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,774 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,775 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,775 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,775 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,775 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,775 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,775 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,775 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,776 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,776 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,776 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,776 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,776 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,776 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,776 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,776 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,777 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,777 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,777 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,777 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,777 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,777 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,777 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,777 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,777 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,778 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,778 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,778 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,778 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,778 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,778 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,778 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,778 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,778 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,780 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,780 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,780 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,780 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,780 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,780 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,780 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,781 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,781 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,781 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,781 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,782 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,782 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,782 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,782 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,782 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,782 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,782 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,783 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,783 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,783 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,783 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,783 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,783 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,783 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,783 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,784 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,784 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,784 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,784 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,784 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,784 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,784 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,784 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,784 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,785 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,785 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,785 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,785 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,785 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,785 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,785 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,785 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,785 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,785 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,786 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,786 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,786 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,786 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,786 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,786 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,786 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,786 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,787 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,787 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,787 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,787 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,787 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,787 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,787 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,788 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,788 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,788 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,788 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,788 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,788 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,788 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,788 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,788 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,788 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,788 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,789 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,789 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,789 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,789 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,789 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,789 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,789 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,789 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,789 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,789 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,790 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,790 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,790 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,790 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,790 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,790 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,790 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,790 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,790 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,790 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,790 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,790 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,790 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,790 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,790 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,790 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,791 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,791 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,791 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,791 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,791 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,792 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,792 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,792 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,792 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,792 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,792 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,792 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,793 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,793 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,793 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,793 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,794 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,794 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,794 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,794 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,794 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,794 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,794 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,794 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,794 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,794 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,794 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,796 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,796 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,796 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,796 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,796 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,796 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,796 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,796 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,796 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,797 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,797 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,797 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,797 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,798 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,798 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,798 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,798 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,798 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,798 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,798 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,798 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,798 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,800 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,800 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,800 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,800 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,800 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,800 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,800 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,800 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,800 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,800 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,800 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,800 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,800 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,800 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,800 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,800 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,800 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,801 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,801 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,801 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,801 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,801 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,801 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,801 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,801 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,801 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,801 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,801 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,801 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,801 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,801 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,802 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,802 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,802 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,802 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,802 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,802 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,802 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,802 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,802 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,802 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,802 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,802 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,802 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,802 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,803 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,803 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,803 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,803 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,803 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,803 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,803 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,803 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,803 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,804 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,804 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,804 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,805 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,805 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,805 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,805 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,805 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,805 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,805 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,806 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,806 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,806 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,806 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,806 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,806 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,806 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,807 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,807 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,807 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,807 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,807 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,807 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,807 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,807 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,808 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,808 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,808 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,808 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,808 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,808 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,809 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,809 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,809 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,809 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,809 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,810 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,810 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,810 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,810 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,810 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,810 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,812 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,812 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,812 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,812 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,812 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,812 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,812 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,812 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,813 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,813 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,813 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,813 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,813 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,813 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,813 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,813 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,813 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,814 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,814 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,814 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,814 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,814 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,814 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,814 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,814 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,815 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,815 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,815 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,815 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,815 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,816 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,816 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,816 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,816 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,818 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,819 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,819 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,819 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,819 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,819 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,819 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,819 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,819 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,819 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,819 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,819 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,819 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,819 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,819 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,819 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,820 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,820 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,820 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,820 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,820 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,820 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,820 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,821 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,821 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,821 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,821 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,821 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,821 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,822 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,822 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,822 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,822 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,822 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,822 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,824 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,824 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,824 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,824 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,824 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,824 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,824 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,824 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,824 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,824 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,824 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,825 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,825 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,825 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,825 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,874 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,874 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,874 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,874 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,876 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,876 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,877 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,877 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,877 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,877 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,878 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,878 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,879 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,879 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,879 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,879 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,880 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,880 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,880 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,880 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,880 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,880 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,880 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,882 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,882 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,882 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,882 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,883 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,883 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,883 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,883 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,884 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,884 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,885 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,885 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,885 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,886 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,886 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,886 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,886 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,886 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,887 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,887 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,887 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,889 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,889 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,889 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,889 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,890 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,890 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,890 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,891 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,891 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,891 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,891 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,891 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,891 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,891 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,893 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,893 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,893 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,894 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,894 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,894 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,894 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,895 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,895 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,895 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,897 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,897 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,897 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,897 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,898 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,898 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,898 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,898 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,899 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,899 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,900 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,900 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,900 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,901 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,901 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,901 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,901 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,901 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,902 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,902 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,902 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,902 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,902 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,903 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,903 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,903 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,903 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,904 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,904 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,904 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,904 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,905 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,905 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,905 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,905 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,906 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,906 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,906 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,906 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,908 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,908 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,908 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,908 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,908 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,909 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,909 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,909 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,909 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,909 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,909 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,909 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,909 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,910 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,910 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,910 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,910 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,910 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,911 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,911 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,911 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,911 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,912 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,912 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,912 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,912 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,912 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,912 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,914 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,914 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,914 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,914 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,914 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,915 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,915 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,915 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,915 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,915 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,915 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,915 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,916 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,916 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,916 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,916 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,917 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,917 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,917 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,917 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,918 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,918 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,918 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,918 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,918 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,918 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,919 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,919 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,919 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,919 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,919 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,919 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,921 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,921 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,921 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,921 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,922 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,922 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,922 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,922 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,922 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,923 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,923 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,923 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,923 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,923 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,923 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,923 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,923 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,924 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,924 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,924 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,924 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,924 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,924 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,925 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,925 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,925 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,925 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,925 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,927 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,927 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,927 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,927 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,927 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,927 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,928 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,928 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,928 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,928 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,928 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,928 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,928 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,929 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,929 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,929 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,929 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,929 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,929 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,930 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,930 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,930 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,930 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,930 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,931 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,931 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,931 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,931 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,932 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,932 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,932 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,932 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,932 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,932 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,933 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,933 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,933 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,933 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,933 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,933 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,933 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,933 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,933 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,935 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,935 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,935 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,935 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,935 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,935 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,935 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,936 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,936 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,936 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,936 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,937 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,937 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,937 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,937 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,937 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,938 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,938 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,938 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,938 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,939 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,939 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,939 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,939 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,939 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,940 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,940 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,940 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,940 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,940 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,940 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,941 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,941 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,941 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,942 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,942 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,942 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,942 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,942 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,942 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,943 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,943 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,943 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,943 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,943 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,943 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,943 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,944 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,944 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,944 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,944 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,944 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,945 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,945 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,945 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,945 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,945 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,946 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,946 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,946 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,946 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,946 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,947 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,947 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,947 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,947 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,947 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,947 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,947 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,947 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,948 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,948 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,948 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,948 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,948 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,949 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,949 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,949 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,949 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,949 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,949 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,950 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,950 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,950 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,950 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,950 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,950 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,951 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,951 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,951 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,951 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,951 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,951 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,953 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,953 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,953 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,953 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,953 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,953 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,953 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,953 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,953 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,953 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,953 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,953 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,954 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,954 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,954 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,954 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,954 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,954 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,954 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,954 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,954 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,954 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,954 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,954 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,954 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,955 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,955 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,955 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,955 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,955 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,955 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,955 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,955 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,957 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,957 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,957 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,957 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,957 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,957 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,958 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,958 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,958 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,958 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,958 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,958 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,958 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,958 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,959 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,959 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,959 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,959 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,959 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,959 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,959 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,960 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,960 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,960 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,960 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,960 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,960 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,960 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,961 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,961 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,961 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,961 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,961 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,961 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,961 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,961 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,962 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,962 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,962 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,962 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,962 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,962 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,962 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,962 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,963 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,963 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,963 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,963 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,963 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,963 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,963 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,963 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,963 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,963 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,965 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,965 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,965 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,965 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,965 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,965 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,965 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,965 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,965 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,965 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,965 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,965 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,966 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,966 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,967 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,967 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,967 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,967 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,967 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,968 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,968 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,968 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,968 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,968 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,968 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,968 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,970 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,970 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,970 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,970 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,970 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,970 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,970 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,970 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,971 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,971 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,971 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,971 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,971 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,971 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,971 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,971 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,971 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,972 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,972 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,972 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,972 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,972 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,972 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,973 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,973 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,973 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,973 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,973 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,973 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,973 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,973 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,974 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,974 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,974 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,974 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,974 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,974 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,974 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,974 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,975 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,975 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,975 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,976 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,976 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,976 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,976 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,976 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,976 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,976 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,976 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,976 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,976 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,976 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,977 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,977 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,977 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,977 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,977 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,977 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,977 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,977 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,977 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,977 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,977 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,977 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,977 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,977 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,978 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,978 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,978 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,978 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,978 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,978 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,978 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,979 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,979 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,979 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,979 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,979 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,979 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,979 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,979 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,980 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,980 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,980 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,980 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,980 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,980 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,980 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,981 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,981 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,981 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,981 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,981 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,981 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,981 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,981 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,981 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,983 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,983 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,983 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,983 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,983 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,983 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,983 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,983 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,983 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,983 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,984 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,984 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,984 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,984 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,984 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,984 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,984 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,984 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,984 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,985 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,985 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,985 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,985 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,985 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,986 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,986 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,986 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,986 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,986 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,986 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,986 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,987 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,987 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,987 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,987 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,987 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,987 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,987 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,988 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,988 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,988 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,988 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,988 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,988 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,988 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,988 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,988 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,988 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,988 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,989 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,989 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,989 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,989 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,989 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,989 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,989 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,990 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,990 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:11:02,990 - main - ERROR - 训练任务 MRPC 失败: num_samples should be a positive integer value, but got num_samples=0
2025-07-27 19:11:03,374 - main - INFO - Spark会话已关闭
2025-07-27 19:11:03,510 - main - INFO - Spark会话创建成功，版本: 3.5.0
2025-07-27 19:11:03,511 - main - INFO - GLUE基准测试系统初始化完成
2025-07-27 19:11:03,511 - main - INFO - 开始训练任务: STS-B
2025-07-27 19:11:03,511 - main - INFO - 加载训练和验证数据...
2025-07-27 19:11:03,512 - data_processor - INFO - 加载 STS-B 任务的 train 数据: E:\spark文本分类\glue\STS-B\train.tsv
2025-07-27 19:11:03,661 - data_processor - INFO - 加载 STS-B 任务的 dev 数据: E:\spark文本分类\glue\STS-B\dev.tsv
2025-07-27 19:11:04,021 - main - INFO - 数据加载完成: 训练集=3000, 验证集=600
2025-07-27 19:11:04,021 - main - INFO - 初始化模型...
2025-07-27 19:11:04,021 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:11:04,022 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:11:04,993 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:11:04,994 - model_manager - INFO - 为任务 STS-B 加载模型，标签数量: 1
2025-07-27 19:11:05,694 - model_manager - INFO - 模型加载成功:
2025-07-27 19:11:05,695 - model_manager - INFO -   - 总参数: 109,483,009
2025-07-27 19:11:05,695 - model_manager - INFO -   - 可训练参数: 109,483,009
2025-07-27 19:11:05,695 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:11:05,697 - trainer - INFO - 训练器初始化完成: 任务=STS-B, 输出目录=E:\spark文本分类\models\STS-B
2025-07-27 19:11:05,697 - trainer - INFO - 准备训练数据...
2025-07-27 19:11:05,941 - trainer - INFO - 数据准备完成: 训练集=3000, 验证集=600
2025-07-27 19:11:05,942 - main - INFO - 开始训练，参数: {'epochs': 2, 'learning_rate': 2e-05}
2025-07-27 19:11:05,942 - trainer - INFO - 开始训练: 轮数=2, 学习率=2e-05
2025-07-27 19:11:05,943 - model_manager - INFO - 优化器创建成功: lr=2e-05, weight_decay=0.01
2025-07-27 19:11:05,943 - model_manager - INFO - 调度器创建成功: warmup_steps=18, total_steps=188
2025-07-27 19:11:05,943 - trainer - INFO - 开始第 1/2 轮训练
2025-07-27 19:12:09,375 - trainer - INFO - Epoch 1/2:
2025-07-27 19:12:09,375 - trainer - INFO -   训练损失: 0.0139
2025-07-27 19:12:09,376 - trainer - INFO -   pearson_correlation: nan
2025-07-27 19:12:09,376 - trainer - INFO -   spearman_correlation: nan
2025-07-27 19:12:09,376 - trainer - INFO -   combined_score: nan
2025-07-27 19:12:09,377 - trainer - INFO -   eval_loss: 0.0001
2025-07-27 19:12:09,377 - trainer - INFO - 开始第 2/2 轮训练
2025-07-27 19:12:13,450 - trainer - INFO - Step 100: loss=0.0033, progress=53.2%
2025-07-27 19:13:18,362 - trainer - INFO - Epoch 2/2:
2025-07-27 19:13:18,362 - trainer - INFO -   训练损失: 0.0027
2025-07-27 19:13:18,362 - trainer - INFO -   pearson_correlation: nan
2025-07-27 19:13:18,362 - trainer - INFO -   spearman_correlation: nan
2025-07-27 19:13:18,362 - trainer - INFO -   combined_score: nan
2025-07-27 19:13:18,363 - trainer - INFO -   eval_loss: 0.0001
2025-07-27 19:13:18,363 - trainer - INFO - 训练完成! 总时间: 2m 12s
2025-07-27 19:13:18,363 - trainer - INFO - 训练历史已保存: E:\spark文本分类\models\STS-B\training_history.json
2025-07-27 19:13:18,363 - main - INFO - 任务 STS-B 训练完成!
2025-07-27 19:13:18,364 - main - INFO - 最佳指标: 0.0000
2025-07-27 19:13:18,364 - main - INFO - 训练时间: 132.42秒
2025-07-27 19:13:18,367 - main - INFO - 开始预测任务: STS-B
2025-07-27 19:13:18,367 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:13:18,367 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:13:21,670 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:13:21,671 - model_manager - INFO - 为任务 STS-B 加载模型，标签数量: 1
2025-07-27 19:13:22,313 - model_manager - INFO - 模型加载成功:
2025-07-27 19:13:22,313 - model_manager - INFO -   - 总参数: 109,483,009
2025-07-27 19:13:22,313 - model_manager - INFO -   - 可训练参数: 109,483,009
2025-07-27 19:13:22,313 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:13:22,313 - main - INFO - 使用预训练模型进行预测
2025-07-27 19:13:22,314 - main - INFO - 加载测试数据...
2025-07-27 19:13:22,314 - data_processor - INFO - 加载 STS-B 任务的 test 数据: E:\spark文本分类\glue\STS-B\test.tsv
2025-07-27 19:13:22,514 - main - INFO - 测试数据加载完成: 1376 样本
2025-07-27 19:13:22,515 - predictor - INFO - 预测器初始化完成: 任务=STS-B, 设备=cuda
2025-07-27 19:13:22,606 - main - INFO - 准备预测文本: 1376 个样本
2025-07-27 19:13:32,177 - predictor - INFO - 提交文件已生成: E:\spark文本分类\results\STS-B_submission.tsv
2025-07-27 19:13:32,177 - main - INFO - 任务 STS-B 预测完成!
2025-07-27 19:13:32,177 - main - INFO - 提交文件: E:\spark文本分类\results\STS-B_submission.tsv
2025-07-27 19:13:32,723 - main - INFO - Spark会话已关闭
2025-07-27 19:13:32,875 - main - INFO - Spark会话创建成功，版本: 3.5.0
2025-07-27 19:13:32,876 - main - INFO - GLUE基准测试系统初始化完成
2025-07-27 19:13:32,877 - main - INFO - 开始训练任务: SST-2
2025-07-27 19:13:32,877 - main - INFO - 加载训练和验证数据...
2025-07-27 19:13:32,877 - data_processor - INFO - 加载 SST-2 任务的 train 数据: E:\spark文本分类\glue\SST-2\train.tsv
2025-07-27 19:13:32,987 - data_processor - INFO - 加载 SST-2 任务的 dev 数据: E:\spark文本分类\glue\SST-2\dev.tsv
2025-07-27 19:13:33,253 - main - INFO - 数据加载完成: 训练集=3000, 验证集=600
2025-07-27 19:13:33,253 - main - INFO - 初始化模型...
2025-07-27 19:13:33,253 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:13:33,253 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:13:34,221 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:13:34,222 - model_manager - INFO - 为任务 SST-2 加载模型，标签数量: 2
2025-07-27 19:13:34,796 - model_manager - INFO - 模型加载成功:
2025-07-27 19:13:34,796 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:13:34,796 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:13:34,798 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:13:34,798 - trainer - INFO - 训练器初始化完成: 任务=SST-2, 输出目录=E:\spark文本分类\models\SST-2
2025-07-27 19:13:34,798 - trainer - INFO - 准备训练数据...
2025-07-27 19:13:34,908 - trainer - INFO - 数据准备完成: 训练集=3000, 验证集=600
2025-07-27 19:13:34,908 - main - INFO - 开始训练，参数: {'epochs': 2, 'learning_rate': 2e-05}
2025-07-27 19:13:34,908 - trainer - INFO - 开始训练: 轮数=2, 学习率=2e-05
2025-07-27 19:13:34,910 - model_manager - INFO - 优化器创建成功: lr=2e-05, weight_decay=0.01
2025-07-27 19:13:34,910 - model_manager - INFO - 调度器创建成功: warmup_steps=18, total_steps=188
2025-07-27 19:13:34,910 - trainer - INFO - 开始第 1/2 轮训练
2025-07-27 19:14:39,727 - trainer - INFO - Epoch 1/2:
2025-07-27 19:14:39,743 - trainer - INFO -   训练损失: 0.4707
2025-07-27 19:14:39,744 - trainer - INFO -   accuracy: 0.8767
2025-07-27 19:14:39,744 - trainer - INFO -   f1: 0.8833
2025-07-27 19:14:39,744 - trainer - INFO -   eval_loss: 0.3137
2025-07-27 19:14:42,019 - model_manager - INFO - 模型已保存到: E:\spark文本分类\models\SST-2\best_model_hf
2025-07-27 19:14:42,020 - trainer - INFO - 保存最佳模型: accuracy=0.8767
2025-07-27 19:14:42,020 - trainer - INFO - 开始第 2/2 轮训练
2025-07-27 19:14:45,531 - trainer - INFO - Step 100: loss=0.2609, progress=53.2%
2025-07-27 19:15:46,149 - trainer - INFO - Epoch 2/2:
2025-07-27 19:15:46,149 - trainer - INFO -   训练损失: 0.2383
2025-07-27 19:15:46,150 - trainer - INFO -   accuracy: 0.8917
2025-07-27 19:15:46,150 - trainer - INFO -   f1: 0.8940
2025-07-27 19:15:46,150 - trainer - INFO -   eval_loss: 0.2772
2025-07-27 19:15:48,740 - model_manager - INFO - 模型已保存到: E:\spark文本分类\models\SST-2\best_model_hf
2025-07-27 19:15:48,741 - trainer - INFO - 保存最佳模型: accuracy=0.8917
2025-07-27 19:15:48,741 - trainer - INFO - 训练完成! 总时间: 2m 13s
2025-07-27 19:15:48,743 - trainer - INFO - 训练历史已保存: E:\spark文本分类\models\SST-2\training_history.json
2025-07-27 19:15:48,743 - main - INFO - 任务 SST-2 训练完成!
2025-07-27 19:15:48,743 - main - INFO - 最佳指标: 0.8917
2025-07-27 19:15:48,745 - main - INFO - 训练时间: 133.83秒
2025-07-27 19:15:48,750 - main - INFO - 开始预测任务: SST-2
2025-07-27 19:15:48,750 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:15:48,751 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:15:53,675 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:15:53,675 - model_manager - INFO - 为任务 SST-2 加载模型，标签数量: 2
2025-07-27 19:15:54,713 - model_manager - INFO - 模型加载成功:
2025-07-27 19:15:54,713 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:15:54,714 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:15:54,714 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:15:54,715 - main - INFO - 使用预训练模型进行预测
2025-07-27 19:15:54,715 - main - INFO - 加载测试数据...
2025-07-27 19:15:54,716 - data_processor - INFO - 加载 SST-2 任务的 test 数据: E:\spark文本分类\glue\SST-2\test.tsv
2025-07-27 19:15:55,020 - main - INFO - 测试数据加载完成: 1821 样本
2025-07-27 19:15:55,021 - predictor - INFO - 预测器初始化完成: 任务=SST-2, 设备=cuda
2025-07-27 19:15:55,114 - main - INFO - 准备预测文本: 1821 个样本
2025-07-27 19:16:06,666 - predictor - INFO - 提交文件已生成: E:\spark文本分类\results\SST-2_submission.tsv
2025-07-27 19:16:06,666 - main - INFO - 任务 SST-2 预测完成!
2025-07-27 19:16:06,667 - main - INFO - 提交文件: E:\spark文本分类\results\SST-2_submission.tsv
2025-07-27 19:16:07,088 - main - INFO - Spark会话已关闭
2025-07-27 19:16:07,318 - main - INFO - Spark会话创建成功，版本: 3.5.0
2025-07-27 19:16:07,320 - main - INFO - GLUE基准测试系统初始化完成
2025-07-27 19:16:07,320 - main - INFO - 开始训练任务: QNLI
2025-07-27 19:16:07,321 - main - INFO - 加载训练和验证数据...
2025-07-27 19:16:07,321 - data_processor - INFO - 加载 QNLI 任务的 train 数据: E:\spark文本分类\glue\QNLI\train.tsv
2025-07-27 19:16:07,549 - data_processor - INFO - 加载 QNLI 任务的 dev 数据: E:\spark文本分类\glue\QNLI\dev.tsv
2025-07-27 19:16:08,199 - main - INFO - 数据加载完成: 训练集=3000, 验证集=600
2025-07-27 19:16:08,200 - main - INFO - 初始化模型...
2025-07-27 19:16:08,200 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:16:08,200 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:16:09,275 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:16:09,276 - model_manager - INFO - 为任务 QNLI 加载模型，标签数量: 2
2025-07-27 19:16:10,122 - model_manager - INFO - 模型加载成功:
2025-07-27 19:16:10,122 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:16:10,123 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:16:10,123 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:16:10,124 - trainer - INFO - 训练器初始化完成: 任务=QNLI, 输出目录=E:\spark文本分类\models\QNLI
2025-07-27 19:16:10,124 - trainer - INFO - 准备训练数据...
2025-07-27 19:16:10,428 - trainer - INFO - 数据准备完成: 训练集=3000, 验证集=600
2025-07-27 19:16:10,429 - main - INFO - 开始训练，参数: {'epochs': 2, 'learning_rate': 2e-05}
2025-07-27 19:16:10,430 - trainer - INFO - 开始训练: 轮数=2, 学习率=2e-05
2025-07-27 19:16:10,433 - model_manager - INFO - 优化器创建成功: lr=2e-05, weight_decay=0.01
2025-07-27 19:16:10,433 - model_manager - INFO - 调度器创建成功: warmup_steps=18, total_steps=188
2025-07-27 19:16:10,434 - trainer - INFO - 开始第 1/2 轮训练
2025-07-27 19:17:14,813 - trainer - INFO - Epoch 1/2:
2025-07-27 19:17:14,813 - trainer - INFO -   训练损失: 0.1141
2025-07-27 19:17:14,814 - trainer - INFO -   accuracy: 0.4650
2025-07-27 19:17:14,814 - trainer - INFO -   f1: 0.6348
2025-07-27 19:17:14,816 - trainer - INFO -   eval_loss: 3.3809
2025-07-27 19:17:17,426 - model_manager - INFO - 模型已保存到: E:\spark文本分类\models\QNLI\best_model_hf
2025-07-27 19:17:17,427 - trainer - INFO - 保存最佳模型: accuracy=0.4650
2025-07-27 19:17:17,427 - trainer - INFO - 开始第 2/2 轮训练
2025-07-27 19:17:21,110 - trainer - INFO - Step 100: loss=0.0318, progress=53.2%
2025-07-27 19:18:23,774 - trainer - INFO - Epoch 2/2:
2025-07-27 19:18:23,775 - trainer - INFO -   训练损失: 0.0115
2025-07-27 19:18:23,776 - trainer - INFO -   accuracy: 0.4650
2025-07-27 19:18:23,776 - trainer - INFO -   f1: 0.6348
2025-07-27 19:18:23,776 - trainer - INFO -   eval_loss: 3.5197
2025-07-27 19:18:23,776 - trainer - INFO - 训练完成! 总时间: 2m 13s
2025-07-27 19:18:23,777 - trainer - INFO - 训练历史已保存: E:\spark文本分类\models\QNLI\training_history.json
2025-07-27 19:18:23,778 - main - INFO - 任务 QNLI 训练完成!
2025-07-27 19:18:23,778 - main - INFO - 最佳指标: 0.4650
2025-07-27 19:18:23,779 - main - INFO - 训练时间: 133.34秒
2025-07-27 19:18:23,787 - main - INFO - 开始预测任务: QNLI
2025-07-27 19:18:23,788 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:18:23,789 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:18:28,178 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:18:28,178 - model_manager - INFO - 为任务 QNLI 加载模型，标签数量: 2
2025-07-27 19:18:29,279 - model_manager - INFO - 模型加载成功:
2025-07-27 19:18:29,284 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:18:29,286 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:18:29,287 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:18:29,287 - main - INFO - 使用预训练模型进行预测
2025-07-27 19:18:29,287 - main - INFO - 加载测试数据...
2025-07-27 19:18:29,288 - data_processor - INFO - 加载 QNLI 任务的 test 数据: E:\spark文本分类\glue\QNLI\test.tsv
2025-07-27 19:18:29,649 - main - INFO - 测试数据加载完成: 5463 样本
2025-07-27 19:18:29,651 - predictor - INFO - 预测器初始化完成: 任务=QNLI, 设备=cuda
2025-07-27 19:18:29,826 - main - INFO - 准备预测文本: 5463 个样本
2025-07-27 19:19:08,370 - predictor - INFO - 提交文件已生成: E:\spark文本分类\results\QNLI_submission.tsv
2025-07-27 19:19:08,371 - main - INFO - 任务 QNLI 预测完成!
2025-07-27 19:19:08,371 - main - INFO - 提交文件: E:\spark文本分类\results\QNLI_submission.tsv
2025-07-27 19:19:08,672 - main - INFO - Spark会话已关闭
2025-07-27 19:19:08,924 - main - INFO - Spark会话创建成功，版本: 3.5.0
2025-07-27 19:19:08,927 - main - INFO - GLUE基准测试系统初始化完成
2025-07-27 19:19:08,927 - main - INFO - 开始训练任务: QQP
2025-07-27 19:19:08,928 - main - INFO - 加载训练和验证数据...
2025-07-27 19:19:08,928 - data_processor - INFO - 加载 QQP 任务的 train 数据: E:\spark文本分类\glue\QQP\train.tsv
2025-07-27 19:19:09,122 - data_processor - INFO - 加载 QQP 任务的 dev 数据: E:\spark文本分类\glue\QQP\dev.tsv
2025-07-27 19:19:09,740 - main - INFO - 数据加载完成: 训练集=3000, 验证集=600
2025-07-27 19:19:09,740 - main - INFO - 初始化模型...
2025-07-27 19:19:09,740 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:19:09,740 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:19:10,725 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:19:10,726 - model_manager - INFO - 为任务 QQP 加载模型，标签数量: 2
2025-07-27 19:19:11,551 - model_manager - INFO - 模型加载成功:
2025-07-27 19:19:11,551 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:19:11,552 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:19:11,552 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:19:11,553 - trainer - INFO - 训练器初始化完成: 任务=QQP, 输出目录=E:\spark文本分类\models\QQP
2025-07-27 19:19:11,553 - trainer - INFO - 准备训练数据...
2025-07-27 19:19:11,774 - trainer - INFO - 数据准备完成: 训练集=3000, 验证集=600
2025-07-27 19:19:11,774 - main - INFO - 开始训练，参数: {'epochs': 2, 'learning_rate': 2e-05}
2025-07-27 19:19:11,775 - trainer - INFO - 开始训练: 轮数=2, 学习率=2e-05
2025-07-27 19:19:11,777 - model_manager - INFO - 优化器创建成功: lr=2e-05, weight_decay=0.01
2025-07-27 19:19:11,779 - model_manager - INFO - 调度器创建成功: warmup_steps=18, total_steps=188
2025-07-27 19:19:11,779 - trainer - INFO - 开始第 1/2 轮训练
2025-07-27 19:20:16,961 - trainer - INFO - Epoch 1/2:
2025-07-27 19:20:16,962 - trainer - INFO -   训练损失: 0.1709
2025-07-27 19:20:16,963 - trainer - INFO -   accuracy: 1.0000
2025-07-27 19:20:16,968 - trainer - INFO -   f1: 0.0000
2025-07-27 19:20:16,968 - trainer - INFO -   eval_loss: 0.0008
2025-07-27 19:20:16,969 - trainer - INFO - 开始第 2/2 轮训练
2025-07-27 19:20:21,008 - trainer - INFO - Step 100: loss=0.0009, progress=53.2%
2025-07-27 19:21:23,264 - trainer - INFO - Epoch 2/2:
2025-07-27 19:21:23,265 - trainer - INFO -   训练损失: 0.0007
2025-07-27 19:21:23,265 - trainer - INFO -   accuracy: 1.0000
2025-07-27 19:21:23,267 - trainer - INFO -   f1: 0.0000
2025-07-27 19:21:23,268 - trainer - INFO -   eval_loss: 0.0005
2025-07-27 19:21:23,268 - trainer - INFO - 训练完成! 总时间: 2m 11s
2025-07-27 19:21:23,272 - trainer - INFO - 训练历史已保存: E:\spark文本分类\models\QQP\training_history.json
2025-07-27 19:21:23,275 - main - INFO - 任务 QQP 训练完成!
2025-07-27 19:21:23,275 - main - INFO - 最佳指标: 0.0000
2025-07-27 19:21:23,275 - main - INFO - 训练时间: 131.49秒
2025-07-27 19:21:23,283 - main - INFO - 开始预测任务: QQP
2025-07-27 19:21:23,285 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:21:23,286 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:21:27,976 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:21:27,976 - model_manager - INFO - 为任务 QQP 加载模型，标签数量: 2
2025-07-27 19:21:29,019 - model_manager - INFO - 模型加载成功:
2025-07-27 19:21:29,019 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:21:29,020 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:21:29,020 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:21:29,020 - main - INFO - 使用预训练模型进行预测
2025-07-27 19:21:29,020 - main - INFO - 加载测试数据...
2025-07-27 19:21:29,021 - data_processor - INFO - 加载 QQP 任务的 test 数据: E:\spark文本分类\glue\QQP\test.tsv
2025-07-27 19:21:29,640 - main - INFO - 测试数据加载完成: 390965 样本
2025-07-27 19:21:29,641 - predictor - INFO - 预测器初始化完成: 任务=QQP, 设备=cuda
2025-07-27 19:21:34,758 - main - INFO - 准备预测文本: 390965 个样本
2025-07-27 19:58:24,550 - main - ERROR - 预测任务 QQP 失败: An error occurred while calling o933.cancelAllJobs.
: java.lang.IllegalStateException: Cannot call methods on a stopped SparkContext.
This stopped SparkContext was created at:

org.apache.spark.api.java.JavaSparkContext.<init>(JavaSparkContext.scala:58)
java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
py4j.reflection.MethodInvoker.invoke(MethodInvoker.java:247)
py4j.reflection.ReflectionEngine.invoke(ReflectionEngine.java:374)
py4j.Gateway.invoke(Gateway.java:238)
py4j.commands.ConstructorCommand.invokeConstructor(ConstructorCommand.java:80)
py4j.commands.ConstructorCommand.execute(ConstructorCommand.java:69)
py4j.ClientServerConnection.waitForCommands(ClientServerConnection.java:182)
py4j.ClientServerConnection.run(ClientServerConnection.java:106)
java.base/java.lang.Thread.run(Thread.java:833)

The currently active SparkContext was created at:

org.apache.spark.api.java.JavaSparkContext.<init>(JavaSparkContext.scala:58)
java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
py4j.reflection.MethodInvoker.invoke(MethodInvoker.java:247)
py4j.reflection.ReflectionEngine.invoke(ReflectionEngine.java:374)
py4j.Gateway.invoke(Gateway.java:238)
py4j.commands.ConstructorCommand.invokeConstructor(ConstructorCommand.java:80)
py4j.commands.ConstructorCommand.execute(ConstructorCommand.java:69)
py4j.ClientServerConnection.waitForCommands(ClientServerConnection.java:182)
py4j.ClientServerConnection.run(ClientServerConnection.java:106)
java.base/java.lang.Thread.run(Thread.java:833)
         

	at org.apache.spark.SparkContext.assertNotStopped(SparkContext.scala:122)

	at org.apache.spark.SparkContext.cancelAllJobs(SparkContext.scala:2596)

	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)

	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)

	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)

	at java.base/java.lang.reflect.Method.invoke(Method.java:568)

	at py4j.reflection.MethodInvoker.invoke(MethodInvoker.java:244)

	at py4j.reflection.ReflectionEngine.invoke(ReflectionEngine.java:374)

	at py4j.Gateway.invoke(Gateway.java:282)

	at py4j.commands.AbstractCommand.invokeMethod(AbstractCommand.java:132)

	at py4j.commands.CallCommand.execute(CallCommand.java:79)

	at py4j.ClientServerConnection.waitForCommands(ClientServerConnection.java:182)

	at py4j.ClientServerConnection.run(ClientServerConnection.java:106)

	at java.base/java.lang.Thread.run(Thread.java:833)


2025-07-27 19:58:25,149 - py4j.clientserver - INFO - Error while receiving.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\py4j\clientserver.py", line 511, in send_command
    answer = smart_decode(self.stream.readline()[:-1])
  File "C:\Program Files\Python310\lib\socket.py", line 705, in readinto
    return self._sock.recv_into(b)
ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-27 19:58:25,154 - py4j.clientserver - INFO - Closing down clientserver connection
2025-07-27 19:58:25,155 - root - ERROR - Exception while sending command.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\py4j\clientserver.py", line 511, in send_command
    answer = smart_decode(self.stream.readline()[:-1])
  File "C:\Program Files\Python310\lib\socket.py", line 705, in readinto
    return self._sock.recv_into(b)
ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\py4j\java_gateway.py", line 1038, in send_command
    response = connection.send_command(command)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\py4j\clientserver.py", line 539, in send_command
    raise Py4JNetworkError(
py4j.protocol.Py4JNetworkError: Error while sending or receiving
2025-07-27 19:58:25,159 - py4j.clientserver - INFO - Closing down clientserver connection
2025-07-27 19:58:27,511 - py4j.clientserver - INFO - Closing down clientserver connection
2025-07-27 19:58:27,511 - py4j.clientserver - INFO - Closing down clientserver connection
2025-07-27 19:58:29,550 - py4j.clientserver - INFO - Closing down clientserver connection
2025-07-27 19:58:29,550 - py4j.clientserver - INFO - Closing down clientserver connection
2025-07-27 19:58:29,569 - py4j.clientserver - INFO - Closing down clientserver connection
