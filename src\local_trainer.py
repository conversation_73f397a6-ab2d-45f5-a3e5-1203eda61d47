"""
本地训练器 - 针对RTX 3060优化
"""
import os
import json
import torch
import numpy as np
from datetime import datetime
from transformers import (
    AutoTokenizer,
    AutoModelForSequenceClassification,
    TrainingArguments,
    Trainer,
    DataCollatorWithPadding
)
from datasets import Dataset
from sklearn.metrics import accuracy_score, f1_score
import logging
from .config import config
from .local_data_processor import LocalGLUEDataProcessor

logger = logging.getLogger(__name__)

class LocalGLUETrainer:
    """本地GLUE训练器"""
    
    def __init__(self, task_name):
        self.task_name = task_name
        self.config = config
        self.data_processor = LocalGLUEDataProcessor()
        
        # 设置设备
        self.device = self._setup_device()
        
        # 创建输出目录
        self.output_dir = os.path.join(
            config.get("paths.results_dir", "results"), 
            task_name
        )
        os.makedirs(self.output_dir, exist_ok=True)
        
        logger.info(f"🎯 初始化训练器: {task_name}")
        logger.info(f"📱 使用设备: {self.device}")
    
    def _setup_device(self):
        """设置计算设备"""
        if torch.cuda.is_available() and config.get("training.use_gpu", True):
            device = torch.device("cuda")
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            logger.info(f"🚀 使用GPU: {gpu_name} ({gpu_memory:.1f}GB)")
        else:
            device = torch.device("cpu")
            logger.info("💻 使用CPU训练")
        
        return device
    
    def load_data(self):
        """加载和预处理数据"""
        logger.info(f"📂 加载数据: {self.task_name}")
        
        # 使用Spark数据处理器加载数据
        datasets = self.data_processor.load_data(self.task_name)
        
        # 数据统计
        stats = self.data_processor.get_statistics(self.task_name)
        logger.info(f"📊 数据统计: {stats}")
        
        return datasets
    
    def preprocess_for_training(self, datasets):
        """为训练预处理数据"""
        logger.info("🔄 预处理训练数据...")
        
        model_config = config.get_model_config()
        model_name = model_config.get("name", "distilbert-base-uncased")
        max_length = model_config.get("max_length", 128)
        
        # 加载tokenizer
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        
        processed_datasets = {}
        
        for split, df in datasets.items():
            if df is None or len(df) == 0:
                continue
            
            # 根据任务类型处理文本
            if self.task_name == "SST-2":
                texts = df['text'].tolist()
                labels = df['labels'].tolist() if 'labels' in df.columns else None
            
            elif self.task_name == "RTE":
                # 组合两个句子
                texts = [f"{row['sentence1']} [SEP] {row['sentence2']}" 
                        for _, row in df.iterrows()]
                labels = df['labels'].tolist() if 'labels' in df.columns else None
            
            else:
                # 默认处理
                text_cols = [col for col in df.columns if 'sentence' in col or 'text' in col]
                if len(text_cols) >= 1:
                    texts = df[text_cols[0]].tolist()
                else:
                    raise ValueError(f"无法找到文本列: {df.columns}")
                labels = df['labels'].tolist() if 'labels' in df.columns else None
            
            # 分词
            encodings = tokenizer(
                texts,
                truncation=True,
                padding=True,
                max_length=max_length,
                return_tensors="pt"
            )
            
            # 创建Dataset
            dataset_dict = {
                'input_ids': encodings['input_ids'],
                'attention_mask': encodings['attention_mask']
            }
            
            if labels is not None:
                dataset_dict['labels'] = labels
            
            dataset = Dataset.from_dict(dataset_dict)
            processed_datasets[split] = dataset
            
            logger.info(f"✅ {split}数据预处理完成: {len(dataset)}条")
        
        return processed_datasets, tokenizer
    
    def create_model(self, num_labels):
        """创建模型"""
        model_config = config.get_model_config()
        model_name = model_config.get("name", "distilbert-base-uncased")
        
        logger.info(f"🤖 加载模型: {model_name}")
        
        model = AutoModelForSequenceClassification.from_pretrained(
            model_name,
            num_labels=num_labels
        )
        
        model.to(self.device)
        
        # 显示模型参数数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        logger.info(f"📊 模型参数: {total_params:,} 总计, {trainable_params:,} 可训练")
        
        return model
    
    def compute_metrics(self, eval_pred):
        """计算评估指标"""
        predictions, labels = eval_pred
        predictions = np.argmax(predictions, axis=1)
        
        metrics = {
            "accuracy": accuracy_score(labels, predictions),
            "f1": f1_score(labels, predictions, average='weighted')
        }
        
        return metrics
    
    def train(self):
        """训练模型"""
        logger.info(f"🚀 开始训练: {self.task_name}")
        
        # 加载数据
        datasets = self.load_data()
        processed_datasets, tokenizer = self.preprocess_for_training(datasets)
        
        # 获取训练和验证数据
        train_dataset = processed_datasets.get('train')
        eval_dataset = processed_datasets.get('dev')
        
        if train_dataset is None:
            raise ValueError("缺少训练数据")
        
        # 确定标签数量
        if 'labels' in train_dataset.features:
            labels = train_dataset['labels']
            num_labels = len(set(labels))
        else:
            num_labels = 2  # 默认二分类
        
        logger.info(f"🏷️ 标签数量: {num_labels}")
        
        # 创建模型
        model = self.create_model(num_labels)
        
        # 训练参数
        training_config = config.get_training_config()
        model_config = config.get_model_config()
        
        training_args = TrainingArguments(
            output_dir=self.output_dir,
            num_train_epochs=model_config.get("num_epochs", 2),
            per_device_train_batch_size=model_config.get("batch_size", 8),
            per_device_eval_batch_size=model_config.get("batch_size", 8),
            warmup_steps=model_config.get("warmup_steps", 100),
            weight_decay=model_config.get("weight_decay", 0.01),
            learning_rate=model_config.get("learning_rate", 2e-5),
            
            logging_dir=f'{self.output_dir}/logs',
            logging_steps=training_config.get("logging_steps", 50),
            
            evaluation_strategy="epoch" if eval_dataset else "no",
            save_strategy="epoch",
            save_total_limit=2,
            load_best_model_at_end=True if eval_dataset else False,
            
            # RTX 3060优化
            fp16=training_config.get("fp16", True),  # 混合精度
            dataloader_pin_memory=True,
            dataloader_num_workers=2,
            
            # 其他设置
            seed=training_config.get("seed", 42),
            report_to=None,  # 不使用wandb等
        )
        
        # 创建训练器
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            tokenizer=tokenizer,
            data_collator=DataCollatorWithPadding(tokenizer=tokenizer),
            compute_metrics=self.compute_metrics if eval_dataset else None,
        )
        
        # 开始训练
        logger.info("🔥 开始训练...")
        start_time = datetime.now()
        
        train_result = trainer.train()
        
        end_time = datetime.now()
        training_time = (end_time - start_time).total_seconds()
        
        logger.info(f"✅ 训练完成，用时: {training_time:.1f}秒")
        
        # 评估
        eval_results = {}
        if eval_dataset:
            logger.info("📊 评估模型...")
            eval_results = trainer.evaluate()
        
        # 保存模型
        model_save_path = os.path.join(self.output_dir, "model")
        trainer.save_model(model_save_path)
        logger.info(f"💾 模型已保存: {model_save_path}")
        
        # 保存结果
        results = {
            "task_name": self.task_name,
            "model_name": model_config.get("name"),
            "training_time": training_time,
            "train_results": train_result.metrics,
            "eval_results": eval_results,
            "config": {
                "model": model_config,
                "training": training_config
            },
            "timestamp": datetime.now().isoformat(),
            "device": str(self.device),
            "num_labels": num_labels
        }
        
        results_file = os.path.join(self.output_dir, "results.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📄 结果已保存: {results_file}")
        
        # 显示关键指标
        if eval_results:
            accuracy = eval_results.get('eval_accuracy', 0)
            logger.info(f"🎯 最终准确率: {accuracy:.4f}")
        
        return results
    
    def close(self):
        """清理资源"""
        self.data_processor.close()
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        logger.info("✅ 资源清理完成")
