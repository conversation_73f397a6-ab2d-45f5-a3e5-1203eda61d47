2025-07-27 18:58:15,140 - utils - INFO - 日志已配置，日志文件: E:\spark文本分类\logs\glue_training_20250727_185815.log
2025-07-27 18:58:15,140 - utils - INFO - ================================================================================
2025-07-27 18:58:15,140 - utils - INFO - GLUE基准测试系统
2025-07-27 18:58:15,141 - utils - INFO - 基于Apache Spark和BERT的文本分类
2025-07-27 18:58:15,141 - utils - INFO - ================================================================================
2025-07-27 18:58:15,141 - utils - INFO - 任务: CoLA
2025-07-27 18:58:15,141 - utils - INFO - 模式: train
2025-07-27 18:58:15,172 - utils - INFO - 设备: cuda
2025-07-27 18:58:15,173 - utils - INFO - Spark主节点: local[*]
2025-07-27 18:58:18,807 - __main__ - INFO - Spark会话创建成功，版本: 3.5.0
2025-07-27 18:58:18,809 - __main__ - INFO - GLUE基准测试系统初始化完成
2025-07-27 18:58:18,809 - utils - INFO - 
🚀 开始训练任务: CoLA
2025-07-27 18:58:18,809 - __main__ - INFO - 开始训练任务: CoLA
2025-07-27 18:58:18,811 - __main__ - INFO - 加载训练和验证数据...
2025-07-27 18:58:18,811 - data_processor - INFO - 加载 CoLA 任务的 train 数据: E:\spark文本分类\glue\CoLA\train.tsv
2025-07-27 18:58:22,075 - data_processor - INFO - 加载 CoLA 任务的 dev 数据: E:\spark文本分类\glue\CoLA\dev.tsv
2025-07-27 18:58:22,772 - __main__ - INFO - 数据加载完成: 训练集=50, 验证集=10
2025-07-27 18:58:22,772 - __main__ - INFO - 初始化模型...
2025-07-27 18:58:22,772 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 18:58:22,772 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 18:58:28,356 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 18:58:28,357 - model_manager - INFO - 为任务 CoLA 加载模型，标签数量: 2
2025-07-27 18:58:29,016 - model_manager - INFO - 模型加载成功:
2025-07-27 18:58:29,016 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 18:58:29,017 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 18:58:29,017 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 18:58:29,017 - trainer - INFO - 训练器初始化完成: 任务=CoLA, 输出目录=E:\spark文本分类\models\CoLA
2025-07-27 18:58:29,017 - trainer - INFO - 准备训练数据...
2025-07-27 18:58:29,163 - trainer - INFO - 数据准备完成: 训练集=50, 验证集=10
2025-07-27 18:58:29,163 - __main__ - INFO - 开始训练，参数: {'epochs': 1, 'learning_rate': 2e-05}
2025-07-27 18:58:29,163 - trainer - INFO - 开始训练: 轮数=1, 学习率=2e-05
2025-07-27 18:58:29,165 - model_manager - INFO - 优化器创建成功: lr=2e-05, weight_decay=0.01
2025-07-27 18:58:29,165 - model_manager - INFO - 调度器创建成功: warmup_steps=0, total_steps=2
2025-07-27 18:58:29,165 - trainer - INFO - 开始第 1/1 轮训练
2025-07-27 18:58:30,047 - trainer - INFO - Epoch 1/1:
2025-07-27 18:58:30,048 - trainer - INFO -   训练损失: 0.6573
2025-07-27 18:58:30,048 - trainer - INFO -   accuracy: 0.7000
2025-07-27 18:58:30,048 - trainer - INFO -   matthews_corrcoef: 0.0000
2025-07-27 18:58:30,048 - trainer - INFO -   eval_loss: 0.6338
2025-07-27 18:58:30,048 - trainer - INFO - 训练完成! 总时间: 0s
2025-07-27 18:58:30,049 - trainer - INFO - 训练历史已保存: E:\spark文本分类\models\CoLA\training_history.json
2025-07-27 18:58:30,049 - __main__ - INFO - 任务 CoLA 训练完成!
2025-07-27 18:58:30,049 - __main__ - INFO - 最佳指标: 0.0000
2025-07-27 18:58:30,049 - __main__ - INFO - 训练时间: 0.88秒
2025-07-27 18:58:30,053 - utils - INFO - ✅ 任务 CoLA 训练完成!
2025-07-27 18:58:30,053 - utils - INFO - 
🎉 所有操作完成!
2025-07-27 18:58:30,053 - utils - INFO - 
📋 下一步:
2025-07-27 18:58:30,053 - utils - INFO - 1. 检查生成的提交文件
2025-07-27 18:58:30,054 - utils - INFO - 2. 访问 https://gluebenchmark.com/ 提交结果
2025-07-27 18:58:30,054 - utils - INFO - 3. 查看模型性能和排名
2025-07-27 18:58:30,391 - __main__ - INFO - Spark会话已关闭
2025-07-27 18:58:30,413 - py4j.clientserver - INFO - Closing down clientserver connection
