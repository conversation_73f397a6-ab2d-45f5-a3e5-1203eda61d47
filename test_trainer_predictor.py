"""
测试训练器和预测器模块
"""
import os
import sys
import logging
import torch
import pandas as pd
from pyspark.sql import SparkSession

# 设置环境变量避免TensorFlow依赖
os.environ['USE_TF'] = 'NO'
os.environ['USE_TORCH'] = 'YES'

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from model_manager import GLUEModelManager
from trainer import GLUETrainer, GLUEDataset
from predictor import GLUEPredictor
from data_processor import GLUEDataProcessor
from utils import setup_logging, set_seed
import config

def create_spark_session():
    """创建Spark会话"""
    try:
        spark = SparkSession.builder \
            .appName("GLUE-Trainer-Test") \
            .master("local[2]") \
            .config("spark.driver.memory", "2g") \
            .config("spark.executor.memory", "2g") \
            .getOrCreate()
        
        print(f"Spark会话创建成功，版本: {spark.version}")
        return spark
    except Exception as e:
        print(f"创建Spark会话失败: {e}")
        return None

def test_dataset_creation():
    """测试数据集创建"""
    print("=" * 50)
    print("测试数据集创建")
    print("=" * 50)
    
    try:
        from transformers import BertTokenizer
        
        # 加载分词器
        tokenizer = BertTokenizer.from_pretrained(config.MODEL_NAME)
        print("✓ 分词器加载成功")
        
        # 测试单句子数据集
        texts = [
            "This is a test sentence.",
            "Another test sentence.",
            "Third test sentence for validation."
        ]
        labels = [1, 0, 1]
        
        dataset = GLUEDataset(texts, labels, tokenizer, max_length=64)
        print(f"✓ 单句子数据集创建成功: {len(dataset)} 样本")
        
        # 测试数据项
        item = dataset[0]
        print(f"  - input_ids形状: {item['input_ids'].shape}")
        print(f"  - attention_mask形状: {item['attention_mask'].shape}")
        print(f"  - 标签: {item['labels'].item()}")
        
        # 测试句子对数据集
        sentence_pairs = [
            ("First sentence.", "Second sentence."),
            ("Another first.", "Another second."),
            ("Third first.", "Third second.")
        ]
        
        pair_dataset = GLUEDataset(sentence_pairs, labels, tokenizer, max_length=64)
        print(f"✓ 句子对数据集创建成功: {len(pair_dataset)} 样本")
        
        # 测试句子对数据项
        pair_item = pair_dataset[0]
        print(f"  - input_ids形状: {pair_item['input_ids'].shape}")
        print(f"  - token_type_ids形状: {pair_item['token_type_ids'].shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据集创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trainer_initialization():
    """测试训练器初始化"""
    print("\n" + "=" * 50)
    print("测试训练器初始化")
    print("=" * 50)
    
    try:
        # 创建模型管理器
        model_manager = GLUEModelManager()
        model_manager.load_tokenizer()
        model_manager.load_model_for_task("CoLA")
        print("✓ 模型管理器创建成功")
        
        # 创建训练器
        trainer = GLUETrainer(model_manager, "CoLA")
        print("✓ 训练器创建成功")
        print(f"  - 任务: {trainer.task}")
        print(f"  - 输出目录: {trainer.output_dir}")
        print(f"  - 设备: {trainer.device}")
        
        # 检查输出目录是否创建
        if os.path.exists(trainer.output_dir):
            print("✓ 输出目录创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 训练器初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_predictor_basic():
    """测试预测器基础功能"""
    print("\n" + "=" * 50)
    print("测试预测器基础功能")
    print("=" * 50)
    
    try:
        # 创建模型管理器和预测器
        model_manager = GLUEModelManager()
        model_manager.load_tokenizer()
        model_manager.load_model_for_task("CoLA")
        
        predictor = GLUEPredictor(model_manager, "CoLA")
        print("✓ 预测器创建成功")
        
        # 测试单个文本预测
        test_text = "This is a grammatically correct sentence."
        result = predictor.predict_single(test_text)
        print("✓ 单文本预测成功")
        print(f"  - 预测结果: {result['prediction']}")
        print(f"  - 概率分布: {result['probability']}")
        
        # 测试批量文本预测
        test_texts = [
            "This is a correct sentence.",
            "This sentence is also correct.",
            "Sentence this correct is not."
        ]
        
        batch_result = predictor.predict_texts(test_texts)
        print("✓ 批量预测成功")
        print(f"  - 预测数量: {batch_result['num_samples']}")
        print(f"  - 预测结果: {batch_result['predictions']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 预测器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sentence_pair_prediction():
    """测试句子对预测"""
    print("\n" + "=" * 50)
    print("测试句子对预测")
    print("=" * 50)
    
    try:
        # 创建模型管理器和预测器（使用MRPC任务）
        model_manager = GLUEModelManager()
        model_manager.load_tokenizer()
        model_manager.load_model_for_task("MRPC")
        
        predictor = GLUEPredictor(model_manager, "MRPC")
        print("✓ 句子对预测器创建成功")
        
        # 测试句子对预测
        sentence_pairs = [
            ("The cat is on the mat.", "A cat is sitting on a mat."),
            ("I love programming.", "I hate programming."),
            ("Hello world.", "Goodbye world.")
        ]
        
        results = predictor.predict_texts(sentence_pairs)
        print("✓ 句子对预测成功")
        print(f"  - 预测数量: {results['num_samples']}")
        print(f"  - 预测结果: {results['predictions']}")
        
        # 测试单个句子对
        single_pair = ("This is sentence one.", "This is sentence two.")
        single_result = predictor.predict_single(single_pair)
        print("✓ 单句子对预测成功")
        print(f"  - 预测结果: {single_result['prediction']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 句子对预测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_regression_prediction():
    """测试回归任务预测"""
    print("\n" + "=" * 50)
    print("测试回归任务预测")
    print("=" * 50)
    
    try:
        # 创建模型管理器和预测器（使用STS-B任务）
        model_manager = GLUEModelManager()
        model_manager.load_tokenizer()
        model_manager.load_model_for_task("STS-B")
        
        predictor = GLUEPredictor(model_manager, "STS-B")
        print("✓ 回归预测器创建成功")
        
        # 测试回归预测
        sentence_pairs = [
            ("The cat is sleeping.", "A cat is resting."),
            ("I love pizza.", "I hate vegetables."),
            ("Good morning.", "Good evening.")
        ]
        
        results = predictor.predict_texts(sentence_pairs)
        print("✓ 回归预测成功")
        print(f"  - 预测数量: {results['num_samples']}")
        print(f"  - 相似度分数: {results['predictions']}")
        
        # 注意：回归任务没有概率分布
        if 'probabilities' not in results:
            print("✓ 回归任务正确地没有概率输出")
        
        return True
        
    except Exception as e:
        print(f"✗ 回归预测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dataframe_prediction():
    """测试DataFrame预测"""
    print("\n" + "=" * 50)
    print("测试DataFrame预测")
    print("=" * 50)
    
    try:
        # 创建模型管理器和预测器
        model_manager = GLUEModelManager()
        model_manager.load_tokenizer()
        model_manager.load_model_for_task("CoLA")
        
        predictor = GLUEPredictor(model_manager, "CoLA")
        print("✓ 预测器创建成功")
        
        # 创建测试DataFrame
        test_data = {
            'sentence': [
                "This is a grammatically correct sentence.",
                "This sentence is also correct.",
                "Sentence this correct is not.",
                "Very good sentence structure here."
            ],
            'id': [1, 2, 3, 4]
        }
        
        df = pd.DataFrame(test_data)
        print(f"✓ 测试DataFrame创建成功: {len(df)} 行")
        
        # 执行预测
        result_df = predictor.predict_dataframe(df, ['sentence'])
        print("✓ DataFrame预测成功")
        print(f"  - 结果列: {result_df.columns.tolist()}")
        print(f"  - 预测结果: {result_df['prediction'].tolist()}")
        
        # 检查概率列
        prob_columns = [col for col in result_df.columns if col.startswith('prob_')]
        if prob_columns:
            print(f"  - 概率列: {prob_columns}")
        
        return True
        
    except Exception as e:
        print(f"✗ DataFrame预测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_submission_generation():
    """测试提交文件生成"""
    print("\n" + "=" * 50)
    print("测试提交文件生成")
    print("=" * 50)
    
    try:
        # 创建模型管理器和预测器
        model_manager = GLUEModelManager()
        model_manager.load_tokenizer()
        model_manager.load_model_for_task("CoLA")
        
        predictor = GLUEPredictor(model_manager, "CoLA")
        print("✓ 预测器创建成功")
        
        # 准备测试数据
        test_texts = [
            "This is a test sentence.",
            "Another test sentence.",
            "Third test sentence.",
            "Final test sentence."
        ]
        
        # 生成提交文件
        output_path = os.path.join(config.RESULTS_DIR, "test_submission.tsv")
        submission_path = predictor.generate_submission(test_texts, output_path)
        
        print(f"✓ 提交文件生成成功: {submission_path}")
        
        # 验证文件内容
        if os.path.exists(submission_path):
            with open(submission_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"  - 文件行数: {len(lines)}")
            print(f"  - 标题行: {lines[0].strip()}")
            print(f"  - 第一个预测: {lines[1].strip()}")
            
            if len(lines) == len(test_texts) + 1:  # +1 for header
                print("✓ 文件格式正确")
        
        return True
        
    except Exception as e:
        print(f"✗ 提交文件生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试训练器和预测器模块...")
    
    # 设置环境
    set_seed(config.RANDOM_SEED)
    logger = setup_logging(config.LOG_DIR)
    
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    
    # 运行测试
    tests = [
        test_dataset_creation,
        test_trainer_initialization,
        test_predictor_basic,
        test_sentence_pair_prediction,
        test_regression_prediction,
        test_dataframe_prediction,
        test_submission_generation
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"测试 {test_func.__name__} 出现异常: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 所有测试通过! ({passed}/{total})")
    else:
        print(f"❌ 部分测试失败: {passed}/{total} 通过")
    
    for i, (test_func, result) in enumerate(zip(tests, results)):
        status = "✅" if result else "❌"
        print(f"{status} {test_func.__name__}")
