#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版环境检查脚本 - 避免编码问题
"""
import sys
import os
import subprocess
import importlib

def check_python_version():
    """检查Python版本"""
    print("=== Python环境检查 ===")
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        print("[OK] Python版本符合要求 (>=3.8)")
        return True
    else:
        print("[ERROR] Python版本过低，需要3.8或更高版本")
        return False

def check_java():
    """检查Java环境"""
    print("\n=== Java环境检查 ===")
    try:
        result = subprocess.run(['java', '-version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            java_output = result.stderr if result.stderr else result.stdout
            java_version = java_output.split('\n')[0]
            print(f"[OK] Java已安装: {java_version}")
            return True
        else:
            print("[ERROR] Java未安装或配置错误")
            return False
    except FileNotFoundError:
        print("[ERROR] Java未找到，请安装Java 8或更高版本")
        return False

def check_spark():
    """检查Spark环境"""
    print("\n=== Spark环境检查 ===")
    
    # 检查SPARK_HOME
    spark_home = os.environ.get('SPARK_HOME')
    if spark_home:
        print(f"[OK] SPARK_HOME: {spark_home}")
    else:
        print("[WARNING] SPARK_HOME未设置")
    
    # 检查pyspark
    try:
        import pyspark
        print(f"[OK] PySpark已安装: {pyspark.__version__}")
        
        # 尝试创建SparkContext
        from pyspark.sql import SparkSession
        spark = SparkSession.builder \
            .appName("EnvironmentTest") \
            .master("local[2]") \
            .config("spark.driver.memory", "2g") \
            .getOrCreate()
        
        # 简单测试
        df = spark.createDataFrame([(1, "test")], ["id", "text"])
        count = df.count()
        spark.stop()
        
        print(f"[OK] Spark测试成功，数据行数: {count}")
        return True
        
    except Exception as e:
        print(f"[ERROR] Spark测试失败: {e}")
        return False

def check_required_packages():
    """检查必需的Python包"""
    print("\n=== Python包检查 ===")
    
    required_packages = {
        'torch': 'PyTorch',
        'transformers': 'Hugging Face Transformers',
        'datasets': 'Hugging Face Datasets',
        'pandas': 'Pandas',
        'numpy': 'NumPy',
        'sklearn': 'Scikit-learn',
        'tqdm': 'TQDM'
    }
    
    missing_packages = []
    
    for package, name in required_packages.items():
        try:
            module = importlib.import_module(package)
            version = getattr(module, '__version__', 'Unknown')
            print(f"[OK] {name}: {version}")
        except ImportError:
            print(f"[ERROR] {name}: 未安装")
            missing_packages.append(package)
    
    return missing_packages

def check_gpu():
    """检查GPU可用性"""
    print("\n=== GPU环境检查 ===")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"[OK] GPU可用: {gpu_count}个GPU")
            print(f"     主GPU: {gpu_name}")
            print(f"     CUDA版本: {torch.version.cuda}")
            return True
        else:
            print("[WARNING] GPU不可用，将使用CPU训练")
            return False
    except ImportError:
        print("[ERROR] PyTorch未安装，无法检查GPU")
        return False

def check_disk_space():
    """检查磁盘空间"""
    print("\n=== 磁盘空间检查 ===")
    
    import shutil
    total, used, free = shutil.disk_usage(".")
    
    total_gb = total // (1024**3)
    used_gb = used // (1024**3)
    free_gb = free // (1024**3)
    
    print(f"总空间: {total_gb}GB")
    print(f"已使用: {used_gb}GB")
    print(f"可用空间: {free_gb}GB")
    
    if free_gb >= 5:
        print("[OK] 磁盘空间充足")
        return True
    else:
        print("[WARNING] 磁盘空间不足，建议至少5GB可用空间")
        return False

def generate_install_commands(missing_packages):
    """生成安装命令"""
    if not missing_packages:
        return
    
    print("\n=== 安装建议 ===")
    print("缺失的包可以通过以下命令安装：")
    print(f"pip install {' '.join(missing_packages)}")
    
    print("\n完整的依赖安装命令：")
    print("pip install torch transformers datasets pandas numpy scikit-learn tqdm pyspark")

def main():
    """主函数"""
    # 设置控制台编码
    if sys.platform == "win32":
        try:
            import codecs
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')
        except:
            pass
    
    print("开始检查本地Spark GLUE环境...")
    print("=" * 50)
    
    checks = []
    
    # 执行各项检查
    checks.append(("Python", check_python_version()))
    checks.append(("Java", check_java()))
    checks.append(("Spark", check_spark()))
    
    missing_packages = check_required_packages()
    checks.append(("Python包", len(missing_packages) == 0))
    
    checks.append(("GPU", check_gpu()))
    checks.append(("磁盘空间", check_disk_space()))
    
    # 生成安装建议
    generate_install_commands(missing_packages)
    
    # 总结
    print("\n" + "=" * 50)
    print("环境检查总结:")
    
    all_passed = True
    for name, passed in checks:
        status = "[OK]" if passed else "[ERROR]"
        print(f"{status} {name}")
        if not passed and name in ["Python", "Java", "Spark"]:
            all_passed = False
    
    if all_passed:
        print("\n环境检查通过！可以开始测试Spark GLUE项目")
    else:
        print("\n部分环境检查未通过，请先解决相关问题")
    
    return all_passed

if __name__ == "__main__":
    main()
