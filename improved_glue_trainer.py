#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版GLUE训练器 - 解决过拟合问题
"""
import os
import json
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from sklearn.metrics import accuracy_score, f1_score, matthews_corrcoef
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split
from datetime import datetime
import logging
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImprovedDataProcessor:
    """改进的数据处理器"""
    
    def __init__(self):
        logger.info("初始化改进的数据处理器")
        
    def create_diverse_sst2_data(self, num_samples=2000):
        """创建更多样化的SST-2数据"""
        logger.info(f"创建多样化SST-2数据集: {num_samples}样本")
        
        # 更丰富的正面评论模板
        positive_templates = [
            "This movie is absolutely {} and {}!",
            "I really {} this film because it's {}!",
            "What a {} story with {} acting!",
            "The {} cinematography makes this {}!",
            "One of the most {} movies with {} direction!",
            "Incredibly {} plot and {} characters!",
            "Such {} dialogue and {} performances!",
            "A truly {} experience that's {}!",
            "The {} soundtrack complements the {} story!",
            "Beautifully {} scenes with {} emotion!"
        ]
        
        negative_templates = [
            "This movie is completely {} and {}.",
            "I really {} this film because it's {}.",
            "What a {} story with {} acting.",
            "The {} cinematography makes this {}.",
            "One of the most {} movies with {} direction.",
            "Incredibly {} plot and {} characters.",
            "Such {} dialogue and {} performances.",
            "A truly {} experience that's {}.",
            "The {} soundtrack ruins the {} story.",
            "Poorly {} scenes with {} emotion."
        ]
        
        # 扩展词汇表
        positive_words = [
            "fantastic", "amazing", "excellent", "brilliant", "outstanding", 
            "wonderful", "incredible", "superb", "magnificent", "spectacular",
            "love", "adore", "enjoy", "appreciate", "best", "greatest", "perfect",
            "marvelous", "exceptional", "extraordinary", "phenomenal", "stunning",
            "delightful", "captivating", "inspiring", "uplifting", "heartwarming",
            "engaging", "compelling", "masterful", "genius", "flawless"
        ]
        
        negative_words = [
            "terrible", "awful", "horrible", "boring", "disappointing", 
            "bad", "poor", "worst", "hate", "dislike", "dreadful", "pathetic",
            "disgusting", "annoying", "frustrating", "unbearable", "painful",
            "mediocre", "uninspiring", "forgettable", "tedious", "irritating",
            "confusing", "pointless", "ridiculous", "absurd", "nonsensical",
            "shallow", "predictable", "cliched", "overrated", "pretentious"
        ]
        
        # 生成更多样化的数据
        data = []
        for i in range(num_samples):
            if i % 2 == 0:
                # 正面情感
                template = positive_templates[i % len(positive_templates)]
                words = np.random.choice(positive_words, 2, replace=False)
                sentence = template.format(words[0], words[1])
                label = 1
            else:
                # 负面情感
                template = negative_templates[i % len(negative_templates)]
                words = np.random.choice(negative_words, 2, replace=False)
                sentence = template.format(words[0], words[1])
                label = 0
            
            # 添加一些变化
            if np.random.random() < 0.1:  # 10%的概率添加额外内容
                extra_phrases = [
                    " The plot was well-developed.",
                    " The characters were believable.",
                    " The pacing was just right.",
                    " The ending was satisfying.",
                    " The visuals were impressive.",
                    " The music was fitting.",
                    " The themes were meaningful.",
                    " The dialogue felt natural."
                ]
                sentence += np.random.choice(extra_phrases)
            
            data.append({"sentence": sentence, "label": label})
        
        df = pd.DataFrame(data)
        logger.info(f"✅ 多样化SST-2数据创建完成: {len(df)}条记录")
        return df
    
    def create_diverse_rte_data(self, num_samples=1500):
        """创建更多样化的RTE数据"""
        logger.info(f"创建多样化RTE数据集: {num_samples}样本")
        
        # 更多的蕴含关系对
        entailment_pairs = [
            ("The cat is sleeping on the sofa.", "There is a cat on the furniture.", 1),
            ("John went to the grocery store.", "John is shopping for food.", 1),
            ("She is reading a science book.", "She is studying science.", 1),
            ("The dog is barking loudly.", "There is a dog making noise.", 1),
            ("The restaurant serves Italian food.", "You can eat pasta there.", 1),
            ("She works as a teacher.", "She educates students.", 1),
            ("The car is moving fast.", "The vehicle is in motion.", 1),
            ("He is playing the piano.", "He is making music.", 1),
            ("The flowers are blooming.", "Plants are growing.", 1),
            ("Students are studying hard.", "People are learning.", 1),
            ("The bird is flying high.", "An animal is in the air.", 1),
            ("She is cooking dinner.", "Someone is preparing food.", 1),
            ("The train arrived on time.", "Transportation was punctual.", 1),
            ("He bought a new computer.", "Someone made a purchase.", 1),
            ("The children are playing outside.", "Kids are having fun outdoors.", 1),
            ("The doctor examined the patient.", "A medical professional checked someone.", 1),
            ("The artist painted a portrait.", "Someone created artwork.", 1),
            ("The athlete won the race.", "A competitor finished first.", 1),
            ("The chef prepared the meal.", "Food was cooked by someone.", 1),
            ("The student passed the exam.", "Someone succeeded in a test.", 1)
        ]
        
        # 更多的矛盾关系对
        contradiction_pairs = [
            ("The weather is sunny today.", "It's raining outside.", 0),
            ("The car is painted red.", "The car is blue in color.", 0),
            ("Students are taking an exam.", "Students are playing games.", 0),
            ("The movie starts at 8 PM.", "The movie ends at 8 PM.", 0),
            ("The book is very thick.", "The book is very thin.", 0),
            ("The room is completely dark.", "The room is brightly lit.", 0),
            ("He is very tall.", "He is very short.", 0),
            ("The water is hot.", "The water is cold.", 0),
            ("The door is open.", "The door is closed.", 0),
            ("She is happy.", "She is sad.", 0),
            ("The store is crowded.", "The store is empty.", 0),
            ("The music is loud.", "The music is quiet.", 0),
            ("He is running fast.", "He is walking slowly.", 0),
            ("The food is delicious.", "The food tastes terrible.", 0),
            ("The building is new.", "The building is ancient.", 0),
            ("The team won the game.", "The team lost the match.", 0),
            ("The price is expensive.", "The cost is very cheap.", 0),
            ("The road is smooth.", "The path is very bumpy.", 0),
            ("The weather is warm.", "The temperature is freezing.", 0),
            ("The test was easy.", "The exam was extremely difficult.", 0)
        ]
        
        all_pairs = entailment_pairs + contradiction_pairs
        
        # 生成数据
        data = []
        for i in range(num_samples):
            premise, hypothesis, label = all_pairs[i % len(all_pairs)]
            data.append({
                "index": i,
                "sentence1": premise,
                "sentence2": hypothesis,
                "label": label
            })
        
        df = pd.DataFrame(data)
        logger.info(f"✅ 多样化RTE数据创建完成: {len(df)}条记录")
        return df
    
    def advanced_preprocess(self, df, task_name):
        """高级预处理"""
        logger.info(f"高级预处理{task_name}数据...")
        
        if task_name == "SST-2":
            # 单句情感分析
            df['text'] = df['sentence'].apply(lambda x: re.sub(r'[^a-zA-Z0-9\s]', '', str(x).lower()))
            df = df[df['text'].str.len() > 10]  # 过滤太短的文本
            df['labels'] = df['label']
            
        elif task_name == "RTE":
            # 文本蕴含，组合两个句子
            df['text'] = df['sentence1'].str.lower() + " [SEP] " + df['sentence2'].str.lower()
            df['text'] = df['text'].apply(lambda x: re.sub(r'[^a-zA-Z0-9\s\[\]]', '', str(x)))
            df = df[df['text'].str.len() > 15]
            df['labels'] = df['label']
        
        # 添加文本长度统计
        df['text_length'] = df['text'].str.len()
        
        # 显示统计信息
        total_count = len(df)
        avg_length = df['text_length'].mean()
        
        logger.info(f"✅ 高级预处理完成: {total_count}条记录，平均长度: {avg_length:.1f}")
        
        return df[['text', 'labels', 'text_length']]

class ImprovedGLUEClassifier(nn.Module):
    """改进的GLUE分类器 - 防止过拟合"""
    
    def __init__(self, input_dim, hidden_dim=64, num_classes=2, dropout=0.5):
        super().__init__()
        
        # 更简单的架构防止过拟合
        self.fc1 = nn.Linear(input_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim // 2)
        self.fc3 = nn.Linear(hidden_dim // 2, num_classes)
        
        # 更强的正则化
        self.dropout1 = nn.Dropout(dropout)
        self.dropout2 = nn.Dropout(dropout * 0.7)
        
        self.relu = nn.ReLU()
        self.batch_norm1 = nn.BatchNorm1d(hidden_dim)
        self.batch_norm2 = nn.BatchNorm1d(hidden_dim // 2)
        
    def forward(self, x):
        x = self.fc1(x)
        x = self.batch_norm1(x)
        x = self.relu(x)
        x = self.dropout1(x)
        
        x = self.fc2(x)
        x = self.batch_norm2(x)
        x = self.relu(x)
        x = self.dropout2(x)
        
        x = self.fc3(x)
        return x

class ImprovedGLUETrainer:
    """改进的GLUE训练器"""
    
    def __init__(self):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        # 改进的TF-IDF参数
        self.vectorizer = TfidfVectorizer(
            max_features=2000,  # 减少特征数量
            stop_words='english',
            ngram_range=(1, 2),
            min_df=3,  # 增加最小文档频率
            max_df=0.9,  # 降低最大文档频率
            sublinear_tf=True  # 使用对数TF
        )
        
        logger.info(f"初始化改进训练器，使用设备: {self.device}")
    
    def prepare_data(self, train_df, dev_df):
        """准备训练数据"""
        logger.info("准备训练数据...")
        
        # 提取文本和标签
        train_texts = train_df['text'].tolist()
        train_labels = train_df['labels'].tolist()
        dev_texts = dev_df['text'].tolist()
        dev_labels = dev_df['labels'].tolist()
        
        # 使用改进的TF-IDF向量化
        X_train = self.vectorizer.fit_transform(train_texts)
        X_dev = self.vectorizer.transform(dev_texts)
        
        # 转换为密集矩阵
        X_train = X_train.toarray()
        X_dev = X_dev.toarray()
        
        # 特征标准化
        from sklearn.preprocessing import StandardScaler
        self.scaler = StandardScaler()
        X_train = self.scaler.fit_transform(X_train)
        X_dev = self.scaler.transform(X_dev)
        
        # 转换为PyTorch张量
        X_train = torch.FloatTensor(X_train).to(self.device)
        X_dev = torch.FloatTensor(X_dev).to(self.device)
        y_train = torch.LongTensor(train_labels).to(self.device)
        y_dev = torch.LongTensor(dev_labels).to(self.device)
        
        logger.info(f"✅ 数据准备完成: 训练{len(train_labels)}条，验证{len(dev_labels)}条")
        logger.info(f"特征维度: {X_train.shape[1]}")
        
        return X_train, y_train, X_dev, y_dev
    
    def train_model(self, X_train, y_train, X_dev, y_dev, epochs=15):
        """训练模型 - 改进版"""
        logger.info("开始改进训练...")
        
        # 创建改进的模型
        input_dim = X_train.shape[1]
        model = ImprovedGLUEClassifier(input_dim, hidden_dim=32, num_classes=2, dropout=0.6).to(self.device)
        
        # 改进的优化器和调度器
        optimizer = optim.Adam(model.parameters(), lr=0.01, weight_decay=1e-4)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='max', factor=0.5, patience=3)
        criterion = nn.CrossEntropyLoss()
        
        # 早停机制
        best_acc = 0
        patience_counter = 0
        patience = 5
        best_model_state = None
        
        # 训练循环
        model.train()
        for epoch in range(epochs):
            optimizer.zero_grad()
            outputs = model(X_train)
            loss = criterion(outputs, y_train)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            # 验证
            model.eval()
            with torch.no_grad():
                train_pred = torch.argmax(outputs, dim=1)
                train_acc = (train_pred == y_train).float().mean()
                
                dev_outputs = model(X_dev)
                dev_pred = torch.argmax(dev_outputs, dim=1)
                dev_acc = (dev_pred == y_dev).float().mean()
            
            # 学习率调度
            scheduler.step(dev_acc)
            
            # 早停检查
            if dev_acc > best_acc:
                best_acc = dev_acc
                best_model_state = model.state_dict().copy()
                patience_counter = 0
            else:
                patience_counter += 1
            
            logger.info(f"Epoch {epoch+1}/{epochs}, 损失: {loss.item():.4f}, "
                       f"训练准确率: {train_acc:.4f}, 验证准确率: {dev_acc:.4f}")
            
            # 早停
            if patience_counter >= patience:
                logger.info(f"早停触发，在第{epoch+1}轮停止训练")
                break
            
            model.train()
        
        # 加载最佳模型
        model.load_state_dict(best_model_state)
        model.eval()
        
        # 最终评估
        with torch.no_grad():
            dev_outputs = model(X_dev)
            dev_pred = torch.argmax(dev_outputs, dim=1)
            dev_acc = (dev_pred == y_dev).float().mean()
            
            # 计算更多指标
            y_true = y_dev.cpu().numpy()
            y_pred = dev_pred.cpu().numpy()
            
            f1 = f1_score(y_true, y_pred, average='weighted')
            matthews = matthews_corrcoef(y_true, y_pred)
        
        logger.info(f"✅ 改进训练完成! 最佳验证准确率: {dev_acc:.4f}, F1: {f1:.4f}, Matthews: {matthews:.4f}")
        
        return {
            "model": model,
            "accuracy": dev_acc.item(),
            "f1": f1,
            "matthews_corr": matthews,
            "final_loss": loss.item(),
            "epochs_trained": epoch + 1
        }

def run_improved_training(task_name="SST-2", num_samples=2000):
    """运行改进的训练"""
    print(f"=== 改进版 GLUE 训练: {task_name} ===")
    print(f"样本数量: {num_samples}")
    print("防止过拟合，提高泛化能力")
    print("=" * 50)
    
    processor = ImprovedDataProcessor()
    trainer = ImprovedGLUETrainer()
    
    start_time = datetime.now()
    
    try:
        # 1. 创建多样化数据
        if task_name == "SST-2":
            df = processor.create_diverse_sst2_data(num_samples)
        elif task_name == "RTE":
            df = processor.create_diverse_rte_data(num_samples)
        else:
            raise ValueError(f"不支持的任务: {task_name}")
        
        # 2. 高级预处理
        processed_df = processor.advanced_preprocess(df, task_name)
        
        # 3. 分割数据
        train_df, dev_df = train_test_split(
            processed_df, 
            test_size=0.2, 
            random_state=42, 
            stratify=processed_df['labels']
        )
        
        logger.info(f"数据分割: 训练{len(train_df)}条，验证{len(dev_df)}条")
        
        # 4. 准备训练数据
        X_train, y_train, X_dev, y_dev = trainer.prepare_data(train_df, dev_df)
        
        # 5. 改进训练
        results = trainer.train_model(X_train, y_train, X_dev, y_dev)
        
        end_time = datetime.now()
        training_time = (end_time - start_time).total_seconds()
        
        # 6. 保存结果
        final_results = {
            "task_name": task_name,
            "timestamp": datetime.now().isoformat(),
            "training_time": training_time,
            "accuracy": results["accuracy"],
            "f1": results["f1"],
            "matthews_corr": results["matthews_corr"],
            "epochs_trained": results["epochs_trained"],
            "num_samples": num_samples,
            "train_samples": len(train_df),
            "dev_samples": len(dev_df),
            "device": str(trainer.device),
            "improvements": [
                "更多样化的数据",
                "特征标准化",
                "更强的正则化",
                "早停机制",
                "学习率调度",
                "梯度裁剪"
            ]
        }
        
        os.makedirs("results", exist_ok=True)
        with open(f"results/improved_glue_{task_name}_results.json", "w") as f:
            json.dump(final_results, f, indent=2)
        
        # 显示结果
        print("\n" + "=" * 50)
        print("🎉 改进训练完成!")
        print(f"任务: {task_name}")
        print(f"准确率: {results['accuracy']:.4f}")
        print(f"F1分数: {results['f1']:.4f}")
        print(f"Matthews相关系数: {results['matthews_corr']:.4f}")
        print(f"训练轮数: {results['epochs_trained']}")
        print(f"训练时间: {training_time:.1f}秒")
        print(f"使用设备: {trainer.device}")
        print(f"结果保存: results/improved_glue_{task_name}_results.json")
        
        return final_results
        
    except Exception as e:
        logger.error(f"改进训练失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    import sys
    
    task = sys.argv[1] if len(sys.argv) > 1 else "SST-2"
    samples = int(sys.argv[2]) if len(sys.argv) > 2 else 2000
    
    run_improved_training(task, samples)
