#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
纯PyTorch GLUE测试 - 避开Spark和编码问题
"""
import os
import json
import pandas as pd
import sys

def check_basic_environment():
    """检查基本环境"""
    print("=== 基本环境检查 ===")
    
    try:
        print(f"Python版本: {sys.version}")
        
        import torch
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"GPU: {torch.cuda.get_device_name(0)}")
            print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
        
        import pandas as pd
        print(f"Pandas版本: {pd.__version__}")
        
        import numpy as np
        print(f"NumPy版本: {np.__version__}")
        
        return True
    except Exception as e:
        print(f"环境检查失败: {e}")
        return False

def create_glue_data():
    """创建GLUE样式数据"""
    print("\n=== 创建GLUE数据 ===")
    
    os.makedirs("data", exist_ok=True)
    
    # SST-2情感分析数据
    sst2_data = [
        {"sentence": "This movie is absolutely fantastic and amazing!", "label": 1},
        {"sentence": "I love this film so much, it's incredible!", "label": 1},
        {"sentence": "Outstanding performance and brilliant story!", "label": 1},
        {"sentence": "Excellent cinematography and great acting!", "label": 1},
        {"sentence": "One of the best movies I have ever seen!", "label": 1},
        {"sentence": "This movie is terrible and completely boring.", "label": 0},
        {"sentence": "I hate this film, it's a waste of time.", "label": 0},
        {"sentence": "Awful acting and terrible storyline here.", "label": 0},
        {"sentence": "Poor direction and bad script writing.", "label": 0},
        {"sentence": "One of the worst films ever made.", "label": 0}
    ] * 20  # 重复20次，共200条数据
    
    # 创建训练和验证集
    train_data = sst2_data[:160]  # 80%训练
    dev_data = sst2_data[160:]    # 20%验证
    
    # 保存数据
    pd.DataFrame(train_data).to_csv("data/train.csv", index=False)
    pd.DataFrame(dev_data).to_csv("data/dev.csv", index=False)
    
    print(f"训练数据: {len(train_data)}条")
    print(f"验证数据: {len(dev_data)}条")
    
    return True

def test_transformers_basic():
    """测试基本的Transformers功能"""
    print("\n=== 测试Transformers ===")
    
    try:
        from transformers import AutoTokenizer
        
        # 测试tokenizer
        tokenizer = AutoTokenizer.from_pretrained("distilbert-base-uncased")
        
        test_text = "This is a test sentence."
        tokens = tokenizer(test_text, return_tensors="pt", padding=True, truncation=True)
        
        print(f"Tokenizer测试成功")
        print(f"输入文本: {test_text}")
        print(f"Token数量: {len(tokens['input_ids'][0])}")
        
        return True
    except Exception as e:
        print(f"Transformers测试失败: {e}")
        return False

def train_simple_classifier():
    """训练简单分类器"""
    print("\n=== 训练简单分类器 ===")
    
    try:
        import torch
        import torch.nn as nn
        from transformers import AutoTokenizer, AutoModel
        from torch.utils.data import Dataset, DataLoader
        import torch.optim as optim
        from sklearn.metrics import accuracy_score
        
        # 检查设备
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"使用设备: {device}")
        
        # 加载数据
        train_df = pd.read_csv("data/train.csv")
        dev_df = pd.read_csv("data/dev.csv")
        
        # 加载tokenizer
        tokenizer = AutoTokenizer.from_pretrained("distilbert-base-uncased")
        
        class SimpleDataset(Dataset):
            def __init__(self, texts, labels, tokenizer, max_length=128):
                self.texts = texts
                self.labels = labels
                self.tokenizer = tokenizer
                self.max_length = max_length
            
            def __len__(self):
                return len(self.texts)
            
            def __getitem__(self, idx):
                text = str(self.texts[idx])
                label = self.labels[idx]
                
                encoding = self.tokenizer(
                    text,
                    truncation=True,
                    padding='max_length',
                    max_length=self.max_length,
                    return_tensors='pt'
                )
                
                return {
                    'input_ids': encoding['input_ids'].flatten(),
                    'attention_mask': encoding['attention_mask'].flatten(),
                    'labels': torch.tensor(label, dtype=torch.long)
                }
        
        # 创建数据集
        train_dataset = SimpleDataset(
            train_df['sentence'].tolist(),
            train_df['label'].tolist(),
            tokenizer
        )
        
        dev_dataset = SimpleDataset(
            dev_df['sentence'].tolist(),
            dev_df['label'].tolist(),
            tokenizer
        )
        
        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True)
        dev_loader = DataLoader(dev_dataset, batch_size=8, shuffle=False)
        
        # 简单的分类模型
        class SimpleClassifier(nn.Module):
            def __init__(self, vocab_size=30522, embed_dim=128, hidden_dim=64, num_classes=2):
                super().__init__()
                self.embedding = nn.Embedding(vocab_size, embed_dim)
                self.fc1 = nn.Linear(embed_dim, hidden_dim)
                self.fc2 = nn.Linear(hidden_dim, num_classes)
                self.dropout = nn.Dropout(0.1)
                self.relu = nn.ReLU()
                
            def forward(self, input_ids, attention_mask):
                # 简单的平均池化
                embeddings = self.embedding(input_ids)
                mask_expanded = attention_mask.unsqueeze(-1).expand(embeddings.size()).float()
                sum_embeddings = torch.sum(embeddings * mask_expanded, 1)
                sum_mask = torch.clamp(mask_expanded.sum(1), min=1e-9)
                mean_embeddings = sum_embeddings / sum_mask
                
                x = self.relu(self.fc1(mean_embeddings))
                x = self.dropout(x)
                x = self.fc2(x)
                return x
        
        # 创建模型
        model = SimpleClassifier().to(device)
        optimizer = optim.Adam(model.parameters(), lr=0.001)
        criterion = nn.CrossEntropyLoss()
        
        print("开始训练...")
        
        # 训练循环
        model.train()
        for epoch in range(2):  # 只训练2个epoch
            total_loss = 0
            for batch in train_loader:
                input_ids = batch['input_ids'].to(device)
                attention_mask = batch['attention_mask'].to(device)
                labels = batch['labels'].to(device)
                
                optimizer.zero_grad()
                outputs = model(input_ids, attention_mask)
                loss = criterion(outputs, labels)
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
            
            avg_loss = total_loss / len(train_loader)
            print(f"Epoch {epoch+1}/2, 平均损失: {avg_loss:.4f}")
        
        # 评估
        print("评估模型...")
        model.eval()
        all_preds = []
        all_labels = []
        
        with torch.no_grad():
            for batch in dev_loader:
                input_ids = batch['input_ids'].to(device)
                attention_mask = batch['attention_mask'].to(device)
                labels = batch['labels'].to(device)
                
                outputs = model(input_ids, attention_mask)
                preds = torch.argmax(outputs, dim=1)
                
                all_preds.extend(preds.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        accuracy = accuracy_score(all_labels, all_preds)
        print(f"验证准确率: {accuracy:.4f}")
        
        # 保存结果
        results = {
            "model_type": "SimpleClassifier",
            "accuracy": accuracy,
            "train_samples": len(train_dataset),
            "dev_samples": len(dev_dataset),
            "epochs": 2,
            "device": str(device)
        }
        
        os.makedirs("results", exist_ok=True)
        with open("results/pytorch_results.json", "w") as f:
            json.dump(results, f, indent=2)
        
        print("结果已保存到: results/pytorch_results.json")
        return True
        
    except Exception as e:
        print(f"训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=== 纯PyTorch GLUE测试 ===")
    print("避开Spark，直接使用PyTorch进行文本分类")
    print("=" * 50)
    
    steps = [
        ("环境检查", check_basic_environment),
        ("创建数据", create_glue_data),
        ("测试Transformers", test_transformers_basic),
        ("训练分类器", train_simple_classifier)
    ]
    
    for step_name, step_func in steps:
        print(f"\n执行: {step_name}")
        if step_func():
            print(f"[成功] {step_name}")
        else:
            print(f"[失败] {step_name}")
            break
    else:
        print("\n" + "=" * 50)
        print("所有步骤完成！")
        
        # 显示结果
        if os.path.exists("results/pytorch_results.json"):
            with open("results/pytorch_results.json", "r") as f:
                results = json.load(f)
            
            print("\n最终结果:")
            print(f"  模型类型: {results['model_type']}")
            print(f"  准确率: {results['accuracy']:.4f}")
            print(f"  训练样本: {results['train_samples']}")
            print(f"  验证样本: {results['dev_samples']}")
            print(f"  使用设备: {results['device']}")

if __name__ == "__main__":
    main()
