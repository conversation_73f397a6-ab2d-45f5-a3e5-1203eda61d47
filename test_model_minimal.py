"""
最小化的模型测试 - 避免TensorFlow依赖
"""
import os
import sys

# 设置环境变量避免TensorFlow导入
os.environ['USE_TF'] = 'NO'
os.environ['USE_TORCH'] = 'YES'

def test_torch_only():
    """测试纯PyTorch功能"""
    print("=" * 50)
    print("测试纯PyTorch功能")
    print("=" * 50)
    
    try:
        import torch
        print(f"✓ PyTorch版本: {torch.__version__}")
        print(f"✓ CUDA可用: {torch.cuda.is_available()}")
        
        # 测试基础tensor操作
        x = torch.randn(2, 3)
        y = torch.randn(3, 4)
        z = torch.mm(x, y)
        print(f"✓ 基础tensor操作成功: {z.shape}")
        
        # 测试神经网络模块
        import torch.nn as nn
        linear = nn.Linear(10, 2)
        input_tensor = torch.randn(5, 10)
        output = linear(input_tensor)
        print(f"✓ 神经网络模块测试成功: {output.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ PyTorch测试失败: {e}")
        return False

def test_transformers_minimal():
    """测试最小化的transformers导入"""
    print("\n" + "=" * 50)
    print("测试最小化transformers导入")
    print("=" * 50)
    
    try:
        # 尝试直接导入特定模块
        print("尝试导入BertTokenizer...")
        from transformers import BertTokenizer
        print("✓ BertTokenizer导入成功")
        
        print("尝试导入BertConfig...")
        from transformers import BertConfig
        print("✓ BertConfig导入成功")
        
        print("尝试导入BertModel...")
        from transformers import BertModel
        print("✓ BertModel导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ transformers导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bert_tokenizer():
    """测试BERT分词器"""
    print("\n" + "=" * 50)
    print("测试BERT分词器")
    print("=" * 50)
    
    try:
        from transformers import BertTokenizer
        
        # 使用较小的模型进行测试
        model_name = "bert-base-uncased"
        print(f"加载分词器: {model_name}")
        
        tokenizer = BertTokenizer.from_pretrained(model_name)
        print(f"✓ 分词器加载成功")
        print(f"  - 词汇表大小: {len(tokenizer)}")
        
        # 测试分词
        test_text = "Hello, this is a test sentence."
        tokens = tokenizer.tokenize(test_text)
        print(f"✓ 分词成功")
        print(f"  - 原文: {test_text}")
        print(f"  - 分词: {tokens}")
        
        # 测试编码
        encoded = tokenizer.encode(test_text, add_special_tokens=True)
        print(f"✓ 编码成功: {encoded}")
        
        # 测试解码
        decoded = tokenizer.decode(encoded)
        print(f"✓ 解码成功: {decoded}")
        
        return True
        
    except Exception as e:
        print(f"✗ BERT分词器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bert_model():
    """测试BERT模型"""
    print("\n" + "=" * 50)
    print("测试BERT模型")
    print("=" * 50)
    
    try:
        from transformers import BertTokenizer, BertModel, BertConfig
        import torch
        
        model_name = "bert-base-uncased"
        print(f"加载BERT模型: {model_name}")
        
        # 加载分词器
        tokenizer = BertTokenizer.from_pretrained(model_name)
        print("✓ 分词器加载成功")
        
        # 加载模型
        model = BertModel.from_pretrained(model_name)
        model.eval()
        print("✓ 模型加载成功")
        
        # 模型信息
        total_params = sum(p.numel() for p in model.parameters())
        print(f"  - 总参数: {total_params:,}")
        print(f"  - 模型大小: {total_params * 4 / 1024 / 1024:.1f} MB")
        
        # 测试推理
        test_text = "This is a test sentence for BERT."
        inputs = tokenizer(test_text, return_tensors="pt")
        
        with torch.no_grad():
            outputs = model(**inputs)
            last_hidden_states = outputs.last_hidden_state
        
        print(f"✓ 模型推理成功")
        print(f"  - 输出形状: {last_hidden_states.shape}")
        print(f"  - 序列长度: {last_hidden_states.shape[1]}")
        print(f"  - 隐藏维度: {last_hidden_states.shape[2]}")
        
        return True
        
    except Exception as e:
        print(f"✗ BERT模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_classification_model():
    """测试分类模型"""
    print("\n" + "=" * 50)
    print("测试分类模型")
    print("=" * 50)
    
    try:
        from transformers import BertTokenizer, BertForSequenceClassification
        import torch
        
        model_name = "bert-base-uncased"
        print(f"加载分类模型: {model_name}")
        
        # 加载分词器
        tokenizer = BertTokenizer.from_pretrained(model_name)
        print("✓ 分词器加载成功")
        
        # 加载分类模型
        model = BertForSequenceClassification.from_pretrained(
            model_name,
            num_labels=2  # 二分类
        )
        model.eval()
        print("✓ 分类模型加载成功")
        print(f"  - 标签数量: {model.config.num_labels}")
        
        # 测试分类推理
        test_texts = [
            "This is a positive sentence.",
            "This is a negative sentence.",
            "This is a neutral sentence."
        ]
        
        for i, text in enumerate(test_texts):
            inputs = tokenizer(text, return_tensors="pt")
            
            with torch.no_grad():
                outputs = model(**inputs)
                logits = outputs.logits
                prediction = torch.argmax(logits, dim=-1)
                probabilities = torch.softmax(logits, dim=-1)
            
            print(f"  文本 {i+1}: {text}")
            print(f"    预测: {prediction.item()}")
            print(f"    概率: {probabilities.squeeze().tolist()}")
        
        print("✓ 分类推理测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 分类模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_integration():
    """测试配置集成"""
    print("\n" + "=" * 50)
    print("测试配置集成")
    print("=" * 50)
    
    try:
        # 添加src目录到路径
        sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
        
        import config
        print("✓ 配置模块导入成功")
        print(f"  - 模型名称: {config.MODEL_NAME}")
        print(f"  - 最大序列长度: {config.MAX_SEQ_LENGTH}")
        print(f"  - 批次大小: {config.BATCH_SIZE}")
        print(f"  - 学习率: {config.LEARNING_RATE}")
        print(f"  - 设备: {config.DEVICE}")
        
        # 测试GLUE任务配置
        print(f"  - GLUE任务: {config.GLUE_TASKS}")
        print(f"  - 任务类型: {config.TASK_TYPES}")
        print(f"  - 标签数量: {config.NUM_LABELS}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始最小化模型测试...")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 运行测试
    tests = [
        test_torch_only,
        test_transformers_minimal,
        test_bert_tokenizer,
        test_bert_model,
        test_classification_model,
        test_config_integration
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"测试 {test_func.__name__} 出现异常: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 所有测试通过! ({passed}/{total})")
    else:
        print(f"❌ 部分测试失败: {passed}/{total} 通过")
    
    for i, (test_func, result) in enumerate(zip(tests, results)):
        status = "✅" if result else "❌"
        print(f"{status} {test_func.__name__}")
