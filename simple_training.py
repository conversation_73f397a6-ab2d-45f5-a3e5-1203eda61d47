
import os
import json
import pandas as pd

def create_sample_data():
    """创建示例数据"""
    print("创建示例数据...")
    
    os.makedirs("data", exist_ok=True)
    
    # 创建简单的情感分析数据
    data = [
        {"text": "I love this movie", "label": 1},
        {"text": "This film is great", "label": 1},
        {"text": "Amazing story", "label": 1},
        {"text": "Excellent acting", "label": 1},
        {"text": "I hate this movie", "label": 0},
        {"text": "Terrible film", "label": 0},
        {"text": "Boring story", "label": 0},
        {"text": "Bad acting", "label": 0}
    ] * 10  # 重复10次，共80条数据
    
    df = pd.DataFrame(data)
    df.to_csv("data/sample_data.csv", index=False)
    print(f"示例数据已创建: {len(df)}条记录")
    
    return df

def train_simple_model():
    """训练简单模型"""
    print("训练简单模型...")
    
    try:
        import torch
        import torch.nn as nn
        from sklearn.feature_extraction.text import TfidfVectorizer
        from sklearn.linear_model import LogisticRegression
        from sklearn.metrics import accuracy_score
        from sklearn.model_selection import train_test_split
        
        # 加载数据
        df = pd.read_csv("data/sample_data.csv")
        
        # 使用sklearn进行简单训练（避免复杂的深度学习问题）
        vectorizer = TfidfVectorizer(max_features=1000)
        X = vectorizer.fit_transform(df['text'])
        y = df['label']
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 训练模型
        model = LogisticRegression()
        model.fit(X_train, y_train)
        
        # 预测和评估
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"训练完成！准确率: {accuracy:.4f}")
        
        # 保存结果
        results = {
            "model_type": "LogisticRegression",
            "accuracy": accuracy,
            "train_samples": len(X_train),
            "test_samples": len(X_test)
        }
        
        os.makedirs("results", exist_ok=True)
        with open("results/simple_results.json", "w") as f:
            json.dump(results, f, indent=2)
        
        print("结果已保存到: results/simple_results.json")
        return True
        
    except Exception as e:
        print(f"训练失败: {e}")
        return False

def main():
    print("=== 简单训练测试 ===")
    
    # 创建数据
    create_sample_data()
    
    # 训练模型
    success = train_simple_model()
    
    if success:
        print("\n简单训练测试成功！")
    else:
        print("\n简单训练测试失败")
    
    return success

if __name__ == "__main__":
    main()
