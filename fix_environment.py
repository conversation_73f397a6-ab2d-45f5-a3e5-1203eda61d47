#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复环境问题
"""
import subprocess
import sys
import os

def fix_numpy_issue():
    """修复NumPy版本问题"""
    print("修复NumPy版本问题...")
    
    try:
        # 卸载并重新安装兼容版本的NumPy
        commands = [
            [sys.executable, "-m", "pip", "uninstall", "numpy", "-y"],
            [sys.executable, "-m", "pip", "install", "numpy==1.24.3"],
            [sys.executable, "-m", "pip", "install", "--upgrade", "torch", "torchvision", "torchaudio", "--index-url", "https://download.pytorch.org/whl/cu118"]
        ]
        
        for cmd in commands:
            print(f"执行: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"警告: 命令执行可能有问题: {result.stderr}")
            else:
                print("成功")
        
        return True
    except Exception as e:
        print(f"修复失败: {e}")
        return False

def create_minimal_test():
    """创建最小化测试"""
    print("创建最小化测试...")
    
    # 创建目录
    os.makedirs("data", exist_ok=True)
    os.makedirs("results", exist_ok=True)
    
    # 创建最小化训练脚本
    minimal_script = '''
import sys
import os

def test_basic_imports():
    """测试基本导入"""
    print("测试基本导入...")
    
    try:
        import numpy as np
        print(f"NumPy版本: {np.__version__}")
        
        import pandas as pd
        print(f"Pandas版本: {pd.__version__}")
        
        import torch
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"GPU: {torch.cuda.get_device_name(0)}")
        
        return True
    except Exception as e:
        print(f"导入失败: {e}")
        return False

def test_simple_model():
    """测试简单模型"""
    print("测试简单模型...")
    
    try:
        import torch
        import torch.nn as nn
        
        # 创建简单模型
        model = nn.Linear(10, 2)
        x = torch.randn(5, 10)
        y = model(x)
        
        print(f"模型测试成功，输出形状: {y.shape}")
        return True
    except Exception as e:
        print(f"模型测试失败: {e}")
        return False

def test_transformers():
    """测试Transformers"""
    print("测试Transformers...")
    
    try:
        from transformers import AutoTokenizer
        
        # 测试tokenizer
        tokenizer = AutoTokenizer.from_pretrained("distilbert-base-uncased")
        text = "Hello world"
        tokens = tokenizer(text, return_tensors="pt")
        
        print(f"Tokenizer测试成功，token数量: {len(tokens['input_ids'][0])}")
        return True
    except Exception as e:
        print(f"Transformers测试失败: {e}")
        return False

def main():
    print("=== 最小化环境测试 ===")
    
    tests = [
        ("基本导入", test_basic_imports),
        ("简单模型", test_simple_model),
        ("Transformers", test_transformers)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\\n--- {test_name} ---")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"{test_name}测试出错: {e}")
            results[test_name] = False
    
    print("\\n=== 测试结果 ===")
    for test_name, passed in results.items():
        status = "通过" if passed else "失败"
        print(f"{test_name}: {status}")
    
    if all(results.values()):
        print("\\n所有测试通过！环境正常")
        return True
    else:
        print("\\n部分测试失败，需要修复环境")
        return False

if __name__ == "__main__":
    main()
'''
    
    with open("minimal_test.py", "w", encoding='utf-8') as f:
        f.write(minimal_script)
    
    print("最小化测试脚本已创建: minimal_test.py")
    return True

def create_simple_training():
    """创建简单训练脚本"""
    print("创建简单训练脚本...")
    
    training_script = '''
import os
import json
import pandas as pd

def create_sample_data():
    """创建示例数据"""
    print("创建示例数据...")
    
    os.makedirs("data", exist_ok=True)
    
    # 创建简单的情感分析数据
    data = [
        {"text": "I love this movie", "label": 1},
        {"text": "This film is great", "label": 1},
        {"text": "Amazing story", "label": 1},
        {"text": "Excellent acting", "label": 1},
        {"text": "I hate this movie", "label": 0},
        {"text": "Terrible film", "label": 0},
        {"text": "Boring story", "label": 0},
        {"text": "Bad acting", "label": 0}
    ] * 10  # 重复10次，共80条数据
    
    df = pd.DataFrame(data)
    df.to_csv("data/sample_data.csv", index=False)
    print(f"示例数据已创建: {len(df)}条记录")
    
    return df

def train_simple_model():
    """训练简单模型"""
    print("训练简单模型...")
    
    try:
        import torch
        import torch.nn as nn
        from sklearn.feature_extraction.text import TfidfVectorizer
        from sklearn.linear_model import LogisticRegression
        from sklearn.metrics import accuracy_score
        from sklearn.model_selection import train_test_split
        
        # 加载数据
        df = pd.read_csv("data/sample_data.csv")
        
        # 使用sklearn进行简单训练（避免复杂的深度学习问题）
        vectorizer = TfidfVectorizer(max_features=1000)
        X = vectorizer.fit_transform(df['text'])
        y = df['label']
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 训练模型
        model = LogisticRegression()
        model.fit(X_train, y_train)
        
        # 预测和评估
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"训练完成！准确率: {accuracy:.4f}")
        
        # 保存结果
        results = {
            "model_type": "LogisticRegression",
            "accuracy": accuracy,
            "train_samples": len(X_train),
            "test_samples": len(X_test)
        }
        
        os.makedirs("results", exist_ok=True)
        with open("results/simple_results.json", "w") as f:
            json.dump(results, f, indent=2)
        
        print("结果已保存到: results/simple_results.json")
        return True
        
    except Exception as e:
        print(f"训练失败: {e}")
        return False

def main():
    print("=== 简单训练测试 ===")
    
    # 创建数据
    create_sample_data()
    
    # 训练模型
    success = train_simple_model()
    
    if success:
        print("\\n简单训练测试成功！")
    else:
        print("\\n简单训练测试失败")
    
    return success

if __name__ == "__main__":
    main()
'''
    
    with open("simple_training.py", "w", encoding='utf-8') as f:
        f.write(training_script)
    
    print("简单训练脚本已创建: simple_training.py")
    return True

def main():
    """主函数"""
    print("=== 环境修复工具 ===")
    
    steps = [
        ("修复NumPy问题", fix_numpy_issue),
        ("创建最小化测试", create_minimal_test),
        ("创建简单训练", create_simple_training)
    ]
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if step_func():
            print(f"{step_name}完成")
        else:
            print(f"{step_name}失败")
    
    print("\n=== 下一步操作 ===")
    print("1. 运行最小化测试: python minimal_test.py")
    print("2. 运行简单训练: python simple_training.py")

if __name__ == "__main__":
    main()
