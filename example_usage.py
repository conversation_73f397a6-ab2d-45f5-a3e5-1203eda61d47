"""
GLUE基准测试使用示例
演示如何使用本系统进行文本分类任务
"""
import os
import sys
import logging

# 设置环境变量
os.environ['USE_TF'] = 'NO'
os.environ['USE_TORCH'] = 'YES'

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from main import GLUEBenchmark
from utils import setup_logging, set_seed
import config

def example_single_task_training():
    """示例1: 单任务训练"""
    print("="*60)
    print("示例1: 单任务训练 (CoLA)")
    print("="*60)
    
    # 创建GLUE基准测试实例
    benchmark = GLUEBenchmark()
    
    try:
        # 训练CoLA任务（语法可接受性判断）
        results = benchmark.train_task(
            task="CoLA",
            epochs=1,  # 快速演示，只训练1个epoch
            max_samples=200  # 只使用200个样本进行快速测试
        )
        
        print(f"✅ 训练完成!")
        print(f"最佳指标: {results['best_metric']:.4f}")
        print(f"训练时间: {results['total_time']:.2f}秒")
        
    finally:
        benchmark.cleanup()

def example_single_task_prediction():
    """示例2: 单任务预测"""
    print("\n" + "="*60)
    print("示例2: 单任务预测 (CoLA)")
    print("="*60)
    
    benchmark = GLUEBenchmark()
    
    try:
        # 预测CoLA任务
        submission_path = benchmark.predict_task("CoLA")
        
        print(f"✅ 预测完成!")
        print(f"提交文件: {submission_path}")
        
        # 显示提交文件内容（前几行）
        if os.path.exists(submission_path):
            print("\n📄 提交文件内容预览:")
            with open(submission_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for i, line in enumerate(lines[:6]):  # 显示前6行
                    print(f"  {i}: {line.strip()}")
                if len(lines) > 6:
                    print(f"  ... (共{len(lines)}行)")
        
    finally:
        benchmark.cleanup()

def example_multiple_tasks():
    """示例3: 多任务快速测试"""
    print("\n" + "="*60)
    print("示例3: 多任务快速测试")
    print("="*60)
    
    benchmark = GLUEBenchmark()
    
    try:
        # 选择几个较小的任务进行快速测试
        test_tasks = ["CoLA", "RTE", "MRPC"]
        
        for task in test_tasks:
            print(f"\n🚀 测试任务: {task}")
            
            try:
                # 快速训练
                results = benchmark.train_task(
                    task=task,
                    epochs=1,
                    max_samples=100  # 每个任务只用100个样本
                )
                
                print(f"  ✅ {task} 训练完成，最佳指标: {results['best_metric']:.4f}")
                
                # 预测
                submission_path = benchmark.predict_task(task)
                print(f"  📁 {task} 提交文件: {os.path.basename(submission_path)}")
                
            except Exception as e:
                print(f"  ❌ {task} 失败: {e}")
        
    finally:
        benchmark.cleanup()

def example_custom_model_usage():
    """示例4: 自定义模型使用"""
    print("\n" + "="*60)
    print("示例4: 自定义模型使用")
    print("="*60)
    
    # 演示如何直接使用各个模块
    from model_manager import GLUEModelManager
    from predictor import GLUEPredictor
    
    try:
        # 1. 创建模型管理器
        model_manager = GLUEModelManager()
        model_manager.load_tokenizer()
        model_manager.load_model_for_task("CoLA")
        
        print("✅ 模型加载完成")
        
        # 2. 创建预测器
        predictor = GLUEPredictor(model_manager, "CoLA")
        
        # 3. 测试一些句子
        test_sentences = [
            "This is a grammatically correct sentence.",
            "This sentence is also correct.",
            "Sentence this correct is not.",  # 语法错误
            "Very good sentence structure here.",
            "Me go store now.",  # 语法错误
        ]
        
        print("\n🔍 语法判断测试:")
        for sentence in test_sentences:
            result = predictor.predict_single(sentence)
            prediction = "✅ 正确" if result['prediction'] == 1 else "❌ 错误"
            confidence = max(result['probability'])
            
            print(f"  {sentence}")
            print(f"    → {prediction} (置信度: {confidence:.3f})")
        
    except Exception as e:
        print(f"❌ 自定义模型使用失败: {e}")

def example_performance_comparison():
    """示例5: 性能对比测试"""
    print("\n" + "="*60)
    print("示例5: 性能对比测试")
    print("="*60)
    
    from model_manager import GLUEModelManager
    from predictor import GLUEPredictor
    import time
    
    try:
        # 创建模型
        model_manager = GLUEModelManager()
        model_manager.load_tokenizer()
        model_manager.load_model_for_task("CoLA")
        predictor = GLUEPredictor(model_manager, "CoLA")
        
        # 准备测试数据
        test_sentences = [
            "This is a test sentence for performance evaluation.",
            "Another sentence for testing purposes.",
            "Third sentence to measure prediction speed.",
        ] * 20  # 60个句子
        
        print(f"📊 性能测试 ({len(test_sentences)} 个句子):")
        
        # 测试不同批次大小
        batch_sizes = [1, 5, 10, 20]
        
        for batch_size in batch_sizes:
            start_time = time.time()
            
            results = predictor.predict_texts(test_sentences, batch_size=batch_size)
            
            end_time = time.time()
            elapsed = end_time - start_time
            throughput = len(test_sentences) / elapsed
            
            print(f"  批次大小 {batch_size:2d}: {elapsed:.3f}秒, {throughput:.1f} 句子/秒")
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")

def example_data_analysis():
    """示例6: 数据分析"""
    print("\n" + "="*60)
    print("示例6: GLUE数据集分析")
    print("="*60)
    
    from pyspark.sql import SparkSession
    from data_processor import GLUEDataProcessor
    
    # 创建Spark会话
    spark = SparkSession.builder \
        .appName("GLUE-Data-Analysis") \
        .master("local[2]") \
        .getOrCreate()
    
    try:
        processor = GLUEDataProcessor(spark, config.DATA_DIR)
        
        print("📈 GLUE数据集统计:")
        print("-" * 40)
        
        for task in config.GLUE_TASKS[:5]:  # 只分析前5个任务
            try:
                stats = processor.get_task_statistics(task)
                
                print(f"\n{task}:")
                print(f"  训练集: {stats.get('train_size', 'N/A')} 样本")
                print(f"  验证集: {stats.get('dev_size', 'N/A')} 样本")
                print(f"  测试集: {stats.get('test_size', 'N/A')} 样本")
                
                if 'label_distribution' in stats:
                    print(f"  标签分布: {stats['label_distribution']}")
                
                # 显示文本长度统计
                for key, value in stats.items():
                    if 'avg_length' in key:
                        print(f"  平均长度: {value:.1f} 字符")
                        break
                
            except Exception as e:
                print(f"  {task}: 数据加载失败 - {e}")
        
    finally:
        spark.stop()

def main():
    """运行所有示例"""
    # 设置日志
    setup_logging(config.LOG_DIR, "INFO")
    set_seed(config.RANDOM_SEED)
    
    print("🚀 GLUE基准测试系统使用示例")
    print("本示例将演示系统的各种功能")
    print("\n⚠️  注意: 为了快速演示，我们使用较少的训练样本和epoch")
    print("实际使用时，请根据需要调整参数以获得更好的性能")
    
    try:
        # 运行示例
        example_single_task_training()
        example_single_task_prediction()
        example_multiple_tasks()
        example_custom_model_usage()
        example_performance_comparison()
        example_data_analysis()
        
        print("\n" + "="*60)
        print("🎉 所有示例运行完成!")
        print("="*60)
        
        print("\n📚 更多使用方法:")
        print("1. 使用命令行: python main.py --help")
        print("2. 查看配置文件: config.py")
        print("3. 查看文档: README.md")
        print("4. 提交结果到: https://gluebenchmark.com/")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断示例运行")
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
