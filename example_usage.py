"""
使用示例 - 演示如何使用GLUE文本分类项目
"""
import os
import sys

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.trainer import GLUETrainer
from src.predictor import GLUEPredictor
from src.data_processor import GLUEDataProcessor
from config import Config

def example_data_exploration():
    """示例：数据探索"""
    print("=== 数据探索示例 ===")
    
    # 创建数据处理器
    processor = GLUEDataProcessor()
    
    # 加载SST-2数据集
    datasets = processor.load_task_data("SST-2")
    
    # 预处理数据
    processed_datasets = processor.preprocess_for_model(datasets, "SST-2")
    
    # 获取统计信息
    stats = processor.get_data_statistics(processed_datasets, "SST-2")
    
    print(f"SST-2数据集统计信息:")
    for split, split_stats in stats['splits'].items():
        print(f"  {split}: {split_stats['num_samples']} 样本")
        if 'label_distribution' in split_stats:
            print(f"    标签分布: {split_stats['label_distribution']}")
    
    processor.close()

def example_single_task_training():
    """示例：单任务训练"""
    print("\n=== 单任务训练示例 ===")
    
    # 选择一个较小的任务进行演示
    task_name = "SST-2"
    
    print(f"开始训练任务: {task_name}")
    
    # 创建训练器
    trainer = GLUETrainer(task_name)
    
    # 运行完整训练流水线
    results = trainer.run_full_pipeline()
    
    print(f"训练完成!")
    print(f"最终评估结果: {results['evaluation_results']}")

def example_prediction():
    """示例：模型预测"""
    print("\n=== 模型预测示例 ===")
    
    task_name = "SST-2"
    
    # 检查是否有训练好的模型
    config = Config()
    model_path = os.path.join(config.MODEL_SAVE_DIR, task_name)
    
    if not os.path.exists(model_path):
        print(f"模型不存在，请先训练模型: {model_path}")
        return
    
    print(f"开始预测任务: {task_name}")
    
    # 创建预测器
    predictor = GLUEPredictor(task_name)
    
    # 进行预测并生成提交文件
    results = predictor.predict_and_submit()
    
    print(f"预测完成!")
    print(f"提交文件: {results['submission_file']}")
    print(f"统计文件: {results['stats_file']}")

def example_quick_test():
    """示例：快速测试（使用较小的配置）"""
    print("\n=== 快速测试示例 ===")
    
    # 修改配置以进行快速测试
    config = Config()
    original_epochs = config.NUM_EPOCHS
    original_batch_size = config.BATCH_SIZE
    
    # 设置较小的参数进行快速测试
    config.NUM_EPOCHS = 1
    config.BATCH_SIZE = 8
    
    print(f"使用快速测试配置: epochs={config.NUM_EPOCHS}, batch_size={config.BATCH_SIZE}")
    
    try:
        # 选择最小的任务
        task_name = "RTE"  # RTE是较小的数据集
        
        print(f"快速训练任务: {task_name}")
        
        # 创建训练器
        trainer = GLUETrainer(task_name)
        
        # 只准备数据和训练模型（不做完整评估）
        datasets = trainer.prepare_data()
        results = trainer.train_model(datasets)
        
        print(f"快速训练完成!")
        print(f"训练损失: {results['train_result']['train_loss']:.4f}")
        
    finally:
        # 恢复原始配置
        config.NUM_EPOCHS = original_epochs
        config.BATCH_SIZE = original_batch_size

def main():
    """主函数 - 运行所有示例"""
    print("GLUE文本分类项目使用示例")
    print("=" * 50)
    
    # 创建必要目录
    config = Config()
    config.create_directories()
    
    try:
        # 1. 数据探索
        example_data_exploration()
        
        # 2. 快速测试（推荐先运行这个）
        example_quick_test()
        
        # 询问用户是否继续完整训练
        user_input = input("\n是否继续完整训练示例？(y/n): ")
        if user_input.lower() == 'y':
            # 3. 单任务训练
            example_single_task_training()
            
            # 4. 模型预测
            example_prediction()
        
        print("\n示例运行完成!")
        
    except Exception as e:
        print(f"示例运行出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
