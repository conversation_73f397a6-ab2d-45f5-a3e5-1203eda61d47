# 🚀 本地Spark GLUE文本分类项目

## 📋 项目简介

基于您的优秀硬件配置（RTX 3060 + Spark 3.4.0）的GLUE基准测试项目，专门针对本地环境优化。

## 🖥️ 硬件配置

- **GPU**: NVIDIA GeForce RTX 3060 (6GB)
- **PyTorch**: 2.7.1+cu118
- **Spark**: 3.4.0
- **Java**: 17.0.2
- **Python**: 3.10.6

## 🚀 快速开始

### 方法1: 一键运行（推荐）

```bash
# 完整测试流程
python run_local_test.py full
```

### 方法2: 分步执行

```bash
# 1. 检查环境
python check_environment.py

# 2. 设置项目
python setup_local_project.py

# 3. 运行演示
python run_local_test.py demo
```

### 方法3: 交互式菜单

```bash
# 启动交互式菜单
python run_local_test.py
```

## 📁 项目结构

```
├── src/                          # 源代码
│   ├── config.py                 # 配置管理
│   ├── local_data_processor.py   # Spark数据处理
│   ├── local_trainer.py          # 训练器（RTX 3060优化）
│   └── main.py                   # 主程序
├── data/glue/                    # GLUE数据集
│   ├── SST-2/                    # 情感分析
│   └── RTE/                      # 文本蕴含
├── models/                       # 模型保存
├── results/                      # 训练结果
├── configs/                      # 配置文件
└── logs/                         # 日志文件
```

## 🎯 支持的任务

| 任务 | 类型 | 描述 | 预期准确率 |
|------|------|------|------------|
| **SST-2** | 情感分析 | 电影评论情感分类 | ~90%+ |
| **RTE** | 文本蕴含 | 判断句子间逻辑关系 | ~65%+ |

## ⚙️ 配置优化

### RTX 3060专用优化

```json
{
  "model": {
    "batch_size": 8,        // 适合6GB显存
    "fp16": true,           // 混合精度训练
    "max_length": 128       // 平衡性能和精度
  },
  "spark": {
    "driver_memory": "4g",  // 充分利用内存
    "master": "local[*]"    // 使用所有CPU核心
  }
}
```

## 🔧 常用命令

### 训练单个任务
```bash
# 训练SST-2（情感分析）
python src/main.py --task SST-2

# 训练RTE（文本蕴含）
python src/main.py --task RTE
```

### 训练多个任务
```bash
# 训练指定任务
python src/main.py --tasks SST-2 RTE

# 训练所有任务
python src/main.py --all
```

### 查看结果
```bash
# 查看训练结果
python run_local_test.py results
```

## 📊 预期性能

基于您的RTX 3060配置：

| 任务 | 训练时间 | 显存使用 | 预期准确率 |
|------|----------|----------|------------|
| SST-2 | ~5-10分钟 | ~3-4GB | 90%+ |
| RTE | ~2-5分钟 | ~2-3GB | 65%+ |

## 🐛 故障排除

### 常见问题

1. **显存不足**
   ```bash
   # 减少批次大小
   # 在configs/config.json中修改batch_size为4或更小
   ```

2. **Spark启动失败**
   ```bash
   # 检查Java环境
   java -version
   
   # 清理临时文件
   rm -rf /tmp/spark-*
   ```

3. **依赖包缺失**
   ```bash
   # 安装所有依赖
   pip install -r requirements.txt
   ```

### 性能调优

1. **提升训练速度**
   - 启用混合精度训练 (fp16=true)
   - 增加批次大小（如果显存允许）
   - 使用更多CPU核心

2. **提升准确率**
   - 增加训练轮数
   - 调整学习率
   - 使用更大的模型

## 📈 结果分析

训练完成后，结果保存在 `results/` 目录：

```
results/
├── SST-2/
│   ├── model/              # 训练好的模型
│   ├── results.json        # 详细结果
│   └── logs/              # 训练日志
└── training_summary.json   # 总结果
```

### 结果文件说明

- `results.json`: 包含准确率、训练时间、模型配置等
- `model/`: 可直接用于推理的模型文件
- `logs/`: 详细的训练日志

## 🎉 成功标志

如果看到以下输出，说明测试成功：

```
✅ 任务 SST-2 训练完成
🎯 SST-2 训练结果:
   准确率: 0.9xxx
   训练时间: xxx秒
```

## 📞 技术支持

如果遇到问题：

1. 查看日志文件：`logs/glue_local_*.log`
2. 检查结果文件：`results/*/results.json`
3. 运行环境检查：`python check_environment.py`

## 🎯 下一步

成功运行后，您可以：

1. 尝试更多GLUE任务
2. 调整模型参数优化性能
3. 将结果提交到GLUE官方网站
4. 扩展到更大的数据集

---

**🎉 祝您测试顺利！您的RTX 3060配置非常适合深度学习训练！**
