#!/usr/bin/env python3
"""
本地测试运行脚本
"""
import os
import sys
import subprocess
import time

def print_header():
    """打印标题"""
    print("🚀 Spark GLUE 本地测试")
    print("=" * 50)
    print("🖥️ 硬件配置:")
    print("   GPU: NVIDIA GeForce RTX 3060 (6GB)")
    print("   PyTorch: 2.7.1+cu118")
    print("   Spark: 3.4.0")
    print("   Java: 17.0.2")
    print("=" * 50)

def check_environment():
    """检查环境"""
    print("🔍 检查环境...")
    
    try:
        result = subprocess.run([sys.executable, "check_environment.py"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 环境检查通过")
            return True
        else:
            print("❌ 环境检查失败:")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 环境检查出错: {e}")
        return False

def setup_project():
    """设置项目"""
    print("\n📁 设置项目结构...")
    
    try:
        result = subprocess.run([sys.executable, "setup_local_project.py"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 项目设置完成")
            print(result.stdout)
            return True
        else:
            print("❌ 项目设置失败:")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 项目设置出错: {e}")
        return False

def install_dependencies():
    """安装依赖"""
    print("\n📦 检查依赖包...")
    
    required_packages = [
        "torch", "transformers", "datasets", 
        "pyspark", "pandas", "numpy", 
        "scikit-learn", "tqdm"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 需要安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def run_demo():
    """运行演示"""
    print("\n🎪 运行演示...")
    
    try:
        # 运行演示
        result = subprocess.run([sys.executable, "src/main.py", "--demo"], 
                              text=True)
        
        if result.returncode == 0:
            print("✅ 演示运行成功")
            return True
        else:
            print("❌ 演示运行失败")
            return False
            
    except Exception as e:
        print(f"❌ 演示运行出错: {e}")
        return False

def run_specific_task(task_name):
    """运行特定任务"""
    print(f"\n🎯 运行任务: {task_name}")
    
    try:
        start_time = time.time()
        
        result = subprocess.run([sys.executable, "src/main.py", "--task", task_name], 
                              text=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ 任务 {task_name} 完成，用时: {duration:.1f}秒")
            return True
        else:
            print(f"❌ 任务 {task_name} 失败")
            return False
            
    except Exception as e:
        print(f"❌ 任务运行出错: {e}")
        return False

def show_results():
    """显示结果"""
    print("\n📊 查看结果...")
    
    results_dir = "results"
    if os.path.exists(results_dir):
        print(f"📁 结果目录: {results_dir}")
        
        for item in os.listdir(results_dir):
            item_path = os.path.join(results_dir, item)
            if os.path.isdir(item_path):
                print(f"   📂 {item}/")
                
                # 查看结果文件
                results_file = os.path.join(item_path, "results.json")
                if os.path.exists(results_file):
                    try:
                        import json
                        with open(results_file, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        task_name = data.get('task_name', 'Unknown')
                        eval_results = data.get('eval_results', {})
                        accuracy = eval_results.get('eval_accuracy', 0)
                        training_time = data.get('training_time', 0)
                        
                        print(f"      🎯 任务: {task_name}")
                        print(f"      📈 准确率: {accuracy:.4f}")
                        print(f"      ⏱️ 训练时间: {training_time:.1f}秒")
                        
                    except Exception as e:
                        print(f"      ❌ 读取结果失败: {e}")
                else:
                    print(f"      ⚠️ 无结果文件")
            elif item.endswith('.json'):
                print(f"   📄 {item}")
    else:
        print("⚠️ 结果目录不存在")

def interactive_menu():
    """交互式菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🎮 选择操作:")
        print("1. 检查环境")
        print("2. 设置项目")
        print("3. 运行演示")
        print("4. 训练SST-2任务")
        print("5. 训练RTE任务")
        print("6. 查看结果")
        print("7. 退出")
        print("=" * 50)
        
        choice = input("请选择 (1-7): ").strip()
        
        if choice == "1":
            check_environment()
        elif choice == "2":
            setup_project()
        elif choice == "3":
            run_demo()
        elif choice == "4":
            run_specific_task("SST-2")
        elif choice == "5":
            run_specific_task("RTE")
        elif choice == "6":
            show_results()
        elif choice == "7":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重试")

def main():
    """主函数"""
    print_header()
    
    if len(sys.argv) > 1:
        # 命令行模式
        command = sys.argv[1]
        
        if command == "check":
            check_environment()
        elif command == "setup":
            setup_project()
        elif command == "demo":
            run_demo()
        elif command == "sst2":
            run_specific_task("SST-2")
        elif command == "rte":
            run_specific_task("RTE")
        elif command == "results":
            show_results()
        elif command == "full":
            # 完整测试流程
            print("🔄 执行完整测试流程...")
            
            steps = [
                ("检查环境", check_environment),
                ("设置项目", setup_project),
                ("检查依赖", install_dependencies),
                ("运行演示", run_demo)
            ]
            
            for step_name, step_func in steps:
                print(f"\n📋 {step_name}...")
                if not step_func():
                    print(f"❌ {step_name}失败，停止执行")
                    break
                print(f"✅ {step_name}完成")
            else:
                print("\n🎉 完整测试流程执行完成！")
                show_results()
        else:
            print(f"❌ 未知命令: {command}")
            print("可用命令: check, setup, demo, sst2, rte, results, full")
    else:
        # 交互式模式
        interactive_menu()

if __name__ == "__main__":
    main()
