"""
预测器模块 - 模型推理和结果生成
"""
import os
import logging
import json
from typing import Dict, List, Tuple, Optional, Any, Union
import torch
import numpy as np
import pandas as pd
from tqdm import tqdm
from pyspark.sql import SparkSession, DataFrame
from pyspark.sql.functions import col, udf, struct, collect_list
from pyspark.sql.types import ArrayType, FloatType, IntegerType, StringType, StructType, StructField

from model_manager import GLUEModelManager
from trainer import GLUEDataset
import config

logger = logging.getLogger(__name__)

class GLUEPredictor:
    """GLUE任务预测器"""
    
    def __init__(self, model_manager: GLUEModelManager, task: str, device: str = None):
        self.model_manager = model_manager
        self.task = task
        self.device = device or config.DEVICE
        
        # 确保模型在评估模式
        if self.model_manager.model is not None:
            self.model_manager.model.eval()
        
        logger.info(f"预测器初始化完成: 任务={task}, 设备={self.device}")
    
    def predict_texts(self, texts: List[Union[str, Tuple[str, str]]], 
                     batch_size: int = None) -> Dict[str, Any]:
        """预测文本列表"""
        if self.model_manager.model is None:
            raise ValueError("模型未加载，请先加载模型")
        
        batch_size = batch_size or config.BATCH_SIZE
        
        # 创建数据集
        dataset = GLUEDataset(
            texts=texts,
            labels=None,  # 预测时不需要标签
            tokenizer=self.model_manager.tokenizer,
            max_length=config.MAX_SEQ_LENGTH
        )
        
        # 创建数据加载器
        from torch.utils.data import DataLoader
        dataloader = DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=0
        )
        
        # 执行预测
        all_predictions = []
        all_probabilities = []
        
        self.model_manager.model.eval()
        with torch.no_grad():
            for batch in tqdm(dataloader, desc="Predicting"):
                # 移除labels键（如果存在）
                batch = {k: v.to(self.device) for k, v in batch.items() if k != 'labels'}
                
                # 前向传播
                outputs = self.model_manager.model(**batch)
                logits = outputs.logits
                
                # 处理预测结果
                if config.TASK_TYPES.get(self.task) == "regression":
                    # 回归任务
                    predictions = logits.squeeze().cpu().numpy()
                    probabilities = None  # 回归任务没有概率
                else:
                    # 分类任务
                    predictions = torch.argmax(logits, dim=-1).cpu().numpy()
                    probabilities = torch.softmax(logits, dim=-1).cpu().numpy()
                
                all_predictions.extend(predictions.tolist() if isinstance(predictions, np.ndarray) else [predictions])
                if probabilities is not None:
                    all_probabilities.extend(probabilities.tolist())
        
        result = {
            'predictions': all_predictions,
            'task': self.task,
            'num_samples': len(texts)
        }
        
        if all_probabilities:
            result['probabilities'] = all_probabilities
        
        return result
    
    def predict_single(self, text: Union[str, Tuple[str, str]]) -> Dict[str, Any]:
        """预测单个文本"""
        result = self.predict_texts([text])
        
        return {
            'prediction': result['predictions'][0],
            'probability': result['probabilities'][0] if 'probabilities' in result else None,
            'task': self.task
        }
    
    def predict_dataframe(self, df: pd.DataFrame, text_columns: List[str]) -> pd.DataFrame:
        """预测pandas DataFrame"""
        # 准备文本数据
        if len(text_columns) == 1:
            # 单句子任务
            texts = df[text_columns[0]].tolist()
        elif len(text_columns) == 2:
            # 句子对任务
            texts = list(zip(df[text_columns[0]], df[text_columns[1]]))
        else:
            raise ValueError(f"不支持的文本列数量: {len(text_columns)}")
        
        # 执行预测
        results = self.predict_texts(texts)
        
        # 添加预测结果到DataFrame
        result_df = df.copy()
        result_df['prediction'] = results['predictions']
        
        if 'probabilities' in results:
            # 为每个类别添加概率列
            num_labels = len(results['probabilities'][0])
            for i in range(num_labels):
                result_df[f'prob_class_{i}'] = [prob[i] for prob in results['probabilities']]
        
        return result_df
    
    def generate_submission(self, test_texts: List[Union[str, Tuple[str, str]]], 
                          output_path: str, task_labels: List[str] = None) -> str:
        """生成GLUE提交格式的结果文件"""
        # 执行预测
        results = self.predict_texts(test_texts)
        predictions = results['predictions']
        
        # 准备提交数据
        submission_data = []
        
        for i, pred in enumerate(predictions):
            if config.TASK_TYPES.get(self.task) == "regression":
                # 回归任务：直接使用预测值
                prediction_value = float(pred)
                # STS-B任务需要限制在0-5范围内
                if self.task == "STS-B":
                    prediction_value = max(0.0, min(5.0, prediction_value))
            else:
                # 分类任务：转换为标签
                if task_labels:
                    prediction_value = task_labels[int(pred)]
                else:
                    prediction_value = int(pred)
            
            submission_data.append({
                'index': i,
                'prediction': prediction_value
            })
        
        # 保存为TSV格式
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("index\tprediction\n")
            for item in submission_data:
                f.write(f"{item['index']}\t{item['prediction']}\n")
        
        logger.info(f"提交文件已生成: {output_path}")
        return output_path
    
    def evaluate_predictions(self, predictions: List[float], 
                           true_labels: List[int]) -> Dict[str, float]:
        """评估预测结果"""
        if config.TASK_TYPES.get(self.task) == "regression":
            # 回归任务
            from scipy.stats import pearsonr, spearmanr
            pearson_corr = pearsonr(predictions, true_labels)[0]
            spearman_corr = spearmanr(predictions, true_labels)[0]
            mse = np.mean((np.array(predictions) - np.array(true_labels)) ** 2)
            
            return {
                "pearson_correlation": pearson_corr,
                "spearman_correlation": spearman_corr,
                "mse": mse,
                "combined_score": (pearson_corr + spearman_corr) / 2
            }
        else:
            # 分类任务
            from sklearn.metrics import accuracy_score, f1_score, matthews_corrcoef
            
            accuracy = accuracy_score(true_labels, predictions)
            
            if self.task == "CoLA":
                mcc = matthews_corrcoef(true_labels, predictions)
                return {"accuracy": accuracy, "matthews_corrcoef": mcc}
            elif len(np.unique(true_labels)) > 2:
                f1 = f1_score(true_labels, predictions, average="macro")
                return {"accuracy": accuracy, "f1_macro": f1}
            else:
                f1 = f1_score(true_labels, predictions)
                return {"accuracy": accuracy, "f1": f1}

class SparkPredictor:
    """基于Spark的分布式预测器"""
    
    def __init__(self, spark: SparkSession, predictor: GLUEPredictor):
        self.spark = spark
        self.predictor = predictor
    
    def predict_spark_dataframe(self, df: DataFrame, text_columns: List[str], 
                               batch_size: int = 1000) -> DataFrame:
        """预测Spark DataFrame"""
        logger.info(f"开始Spark DataFrame预测: {df.count()} 行")
        
        # 创建预测UDF
        predict_udf = self._create_prediction_udf(text_columns, batch_size)
        
        # 根据文本列数量处理
        if len(text_columns) == 1:
            # 单句子任务
            result_df = df.withColumn("prediction", predict_udf(col(text_columns[0])))
        elif len(text_columns) == 2:
            # 句子对任务
            from pyspark.sql.functions import array
            text_array = array(col(text_columns[0]), col(text_columns[1]))
            result_df = df.withColumn("prediction", predict_udf(text_array))
        else:
            raise ValueError(f"不支持的文本列数量: {len(text_columns)}")
        
        return result_df
    
    def _create_prediction_udf(self, text_columns: List[str], batch_size: int):
        """创建预测UDF"""
        task = self.predictor.task
        model_name = self.predictor.model_manager.model_name
        max_length = config.MAX_SEQ_LENGTH
        
        def predict_batch(texts):
            """批量预测函数"""
            import torch
            from transformers import AutoTokenizer, BertForSequenceClassification
            
            # 在每个executor中重新加载模型
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = BertForSequenceClassification.from_pretrained(model_name)
            model.eval()
            
            results = []
            with torch.no_grad():
                for text in texts:
                    if isinstance(text, (list, tuple)) and len(text) == 2:
                        # 句子对
                        inputs = tokenizer(
                            text[0], text[1],
                            padding=True,
                            truncation=True,
                            max_length=max_length,
                            return_tensors="pt"
                        )
                    else:
                        # 单句子
                        inputs = tokenizer(
                            text,
                            padding=True,
                            truncation=True,
                            max_length=max_length,
                            return_tensors="pt"
                        )
                    
                    outputs = model(**inputs)
                    
                    if config.TASK_TYPES.get(task) == "regression":
                        # 回归任务
                        prediction = outputs.logits.squeeze().item()
                    else:
                        # 分类任务
                        prediction = torch.argmax(outputs.logits, dim=-1).item()
                    
                    results.append(float(prediction))
            
            return results
        
        # 注册UDF
        if len(text_columns) == 1:
            prediction_udf = udf(lambda x: predict_batch([x])[0], FloatType())
        else:
            prediction_udf = udf(lambda x: predict_batch([x])[0], FloatType())
        
        return prediction_udf
    
    def batch_predict_and_save(self, df: DataFrame, text_columns: List[str], 
                              output_path: str, format: str = "tsv") -> str:
        """批量预测并保存结果"""
        # 执行预测
        result_df = self.predict_spark_dataframe(df, text_columns)
        
        # 保存结果
        if format.lower() == "tsv":
            result_df.coalesce(1).write.mode("overwrite").option("header", "true").option("sep", "\t").csv(output_path)
        elif format.lower() == "csv":
            result_df.coalesce(1).write.mode("overwrite").option("header", "true").csv(output_path)
        elif format.lower() == "parquet":
            result_df.write.mode("overwrite").parquet(output_path)
        else:
            raise ValueError(f"不支持的格式: {format}")
        
        logger.info(f"预测结果已保存: {output_path}")
        return output_path
