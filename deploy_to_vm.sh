#!/bin/bash

# 虚拟机自动化部署脚本
# 用于在Ubuntu虚拟机中部署Spark GLUE文本分类项目

set -e  # 遇到错误立即退出

echo "=========================================="
echo "  Spark GLUE文本分类项目 - 虚拟机部署"
echo "=========================================="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if command -v $1 &> /dev/null; then
        log_success "$1 已安装"
        return 0
    else
        log_error "$1 未安装"
        return 1
    fi
}

# 检查文件是否存在
check_file() {
    if [ -f "$1" ]; then
        log_success "文件存在: $1"
        return 0
    else
        log_error "文件不存在: $1"
        return 1
    fi
}

# 检查目录是否存在
check_directory() {
    if [ -d "$1" ]; then
        log_success "目录存在: $1"
        return 0
    else
        log_error "目录不存在: $1"
        return 1
    fi
}

# 1. 环境检查
log_info "步骤1: 检查系统环境"
echo "----------------------------------------"

# 检查操作系统
if [ -f /etc/os-release ]; then
    . /etc/os-release
    log_info "操作系统: $NAME $VERSION"
else
    log_warning "无法确定操作系统版本"
fi

# 检查必要命令
log_info "检查必要命令..."
MISSING_COMMANDS=()

if ! check_command "java"; then
    MISSING_COMMANDS+=("java")
fi

if ! check_command "python3"; then
    MISSING_COMMANDS+=("python3")
fi

if ! check_command "pip3"; then
    MISSING_COMMANDS+=("pip3")
fi

# 检查可选命令
if check_command "spark-submit"; then
    SPARK_VERSION=$(spark-submit --version 2>&1 | grep "version" | head -1)
    log_info "Spark版本: $SPARK_VERSION"
else
    log_warning "Spark未在PATH中找到，请确保已正确安装"
fi

if check_command "hadoop"; then
    HADOOP_VERSION=$(hadoop version 2>&1 | head -1)
    log_info "Hadoop版本: $HADOOP_VERSION"
else
    log_warning "Hadoop未在PATH中找到"
fi

# 如果有缺失的命令，提供安装建议
if [ ${#MISSING_COMMANDS[@]} -gt 0 ]; then
    log_error "缺失必要命令: ${MISSING_COMMANDS[*]}"
    echo ""
    echo "安装建议:"
    echo "sudo apt update"
    echo "sudo apt install openjdk-8-jdk python3 python3-pip python3-venv"
    echo ""
    read -p "是否继续部署？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 2. 项目文件检查
log_info "步骤2: 检查项目文件"
echo "----------------------------------------"

REQUIRED_FILES=(
    "config.py"
    "main.py"
    "requirements.txt"
    "src/data_processor.py"
    "src/model_manager.py"
    "src/trainer.py"
    "src/predictor.py"
)

MISSING_FILES=()
for file in "${REQUIRED_FILES[@]}"; do
    if ! check_file "$file"; then
        MISSING_FILES+=("$file")
    fi
done

if [ ${#MISSING_FILES[@]} -gt 0 ]; then
    log_error "缺失项目文件: ${MISSING_FILES[*]}"
    echo "请确保所有项目文件都已正确传输到虚拟机"
    exit 1
fi

# 检查GLUE数据集
log_info "检查GLUE数据集..."
if check_directory "glue"; then
    GLUE_TASKS=("SST-2" "CoLA" "MRPC" "RTE" "QNLI" "QQP" "MNLI" "STS-B" "WNLI")
    AVAILABLE_TASKS=0
    
    for task in "${GLUE_TASKS[@]}"; do
        if [ -d "glue/$task" ] && [ -f "glue/$task/train.tsv" ]; then
            log_success "GLUE任务可用: $task"
            ((AVAILABLE_TASKS++))
        else
            log_warning "GLUE任务缺失: $task"
        fi
    done
    
    log_info "可用GLUE任务: $AVAILABLE_TASKS/${#GLUE_TASKS[@]}"
    
    if [ $AVAILABLE_TASKS -eq 0 ]; then
        log_error "没有可用的GLUE任务数据"
        echo "请确保GLUE数据集已正确放置在glue/目录中"
        exit 1
    fi
else
    log_error "GLUE数据集目录不存在"
    echo "请将GLUE数据集放置在项目根目录的glue/文件夹中"
    exit 1
fi

# 3. Python环境设置
log_info "步骤3: 设置Python环境"
echo "----------------------------------------"

# 检查是否已有虚拟环境
if [ -d "venv" ]; then
    log_info "发现现有虚拟环境"
    read -p "是否重新创建虚拟环境？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "删除现有虚拟环境..."
        rm -rf venv
    fi
fi

# 创建虚拟环境
if [ ! -d "venv" ]; then
    log_info "创建Python虚拟环境..."
    python3 -m venv venv
    log_success "虚拟环境创建完成"
fi

# 激活虚拟环境
log_info "激活虚拟环境..."
source venv/bin/activate

# 升级pip
log_info "升级pip..."
pip install --upgrade pip

# 4. 安装依赖
log_info "步骤4: 安装Python依赖"
echo "----------------------------------------"

log_info "安装项目依赖..."
# 使用国内镜像加速安装
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

log_success "依赖安装完成"

# 5. 创建项目目录
log_info "步骤5: 创建项目目录"
echo "----------------------------------------"

DIRECTORIES=("outputs" "saved_models" "results" "logs")

for dir in "${DIRECTORIES[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        log_success "创建目录: $dir"
    else
        log_info "目录已存在: $dir"
    fi
done

# 6. 环境变量设置
log_info "步骤6: 检查环境变量"
echo "----------------------------------------"

# 检查JAVA_HOME
if [ -z "$JAVA_HOME" ]; then
    log_warning "JAVA_HOME未设置"
    # 尝试自动检测Java路径
    JAVA_PATH=$(readlink -f $(which java) | sed "s:/bin/java::")
    if [ -d "$JAVA_PATH" ]; then
        export JAVA_HOME="$JAVA_PATH"
        log_info "自动设置JAVA_HOME: $JAVA_HOME"
    fi
else
    log_success "JAVA_HOME: $JAVA_HOME"
fi

# 检查SPARK_HOME
if [ -z "$SPARK_HOME" ]; then
    log_warning "SPARK_HOME未设置"
    # 尝试自动检测Spark路径
    if command -v spark-submit &> /dev/null; then
        SPARK_PATH=$(dirname $(dirname $(readlink -f $(which spark-submit))))
        if [ -d "$SPARK_PATH" ]; then
            export SPARK_HOME="$SPARK_PATH"
            log_info "自动设置SPARK_HOME: $SPARK_HOME"
        fi
    fi
else
    log_success "SPARK_HOME: $SPARK_HOME"
fi

# 7. 运行测试
log_info "步骤7: 运行安装测试"
echo "----------------------------------------"

log_info "运行基础测试..."
if python test_installation.py; then
    log_success "基础测试通过"
else
    log_error "基础测试失败"
    echo ""
    echo "可能的解决方案:"
    echo "1. 检查Java和Spark环境变量"
    echo "2. 确保所有依赖都已正确安装"
    echo "3. 检查GLUE数据集是否完整"
    exit 1
fi

# 8. 完成部署
log_info "步骤8: 部署完成"
echo "----------------------------------------"

log_success "🎉 项目部署成功！"
echo ""
echo "下一步操作:"
echo "1. 激活虚拟环境: source venv/bin/activate"
echo "2. 运行快速示例: python example_usage.py"
echo "3. 训练单个任务: python main.py --mode train --task SST-2"
echo "4. 查看帮助信息: python main.py --help"
echo ""
echo "项目目录结构:"
echo "├── src/           # 源代码"
echo "├── glue/          # GLUE数据集"
echo "├── outputs/       # 训练输出"
echo "├── saved_models/  # 保存的模型"
echo "├── results/       # 实验结果"
echo "└── logs/          # 日志文件"
echo ""
echo "重要提示:"
echo "- 首次运行会下载预训练模型，需要网络连接"
echo "- 建议先运行小任务(如RTE)进行测试"
echo "- 如遇到内存不足，可在config.py中调整BATCH_SIZE"
echo ""

# 创建快速启动脚本
cat > start_project.sh << 'EOF'
#!/bin/bash
echo "启动Spark GLUE文本分类项目..."
source venv/bin/activate
echo "虚拟环境已激活"
echo "可用命令:"
echo "  python example_usage.py          # 运行示例"
echo "  python main.py --help            # 查看帮助"
echo "  python main.py --mode train --task SST-2  # 训练模型"
EOF

chmod +x start_project.sh
log_success "创建快速启动脚本: start_project.sh"

echo ""
log_success "部署完成！使用 ./start_project.sh 快速开始"
