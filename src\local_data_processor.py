"""
本地数据处理模块 - 使用Spark进行数据处理
"""
import os
import pandas as pd
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, when, lit
from pyspark.sql.types import StructType, StructField, StringType, IntegerType
import logging
from .config import config

logger = logging.getLogger(__name__)

class SparkDataProcessor:
    """Spark数据处理器"""
    
    def __init__(self):
        self.spark = self._create_spark_session()
        self.data_root = config.get("paths.data_root", "data/glue")
        
    def _create_spark_session(self):
        """创建Spark会话"""
        spark_config = config.get_spark_config()
        
        spark = SparkSession.builder \
            .appName(spark_config.get("app_name", "GLUE_Local_Test")) \
            .master(spark_config.get("master", "local[*]")) \
            .config("spark.driver.memory", spark_config.get("driver_memory", "4g")) \
            .config("spark.executor.memory", spark_config.get("executor_memory", "2g")) \
            .config("spark.sql.adaptive.enabled", "true") \
            .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
            .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
            .getOrCreate()
        
        # 设置日志级别
        spark.sparkContext.setLogLevel("WARN")
        
        logger.info(f"✅ Spark会话已创建: {spark.version}")
        return spark
    
    def load_task_data(self, task_name):
        """加载指定任务的数据"""
        logger.info(f"📂 加载任务数据: {task_name}")
        
        task_dir = os.path.join(self.data_root, task_name)
        
        if not os.path.exists(task_dir):
            raise FileNotFoundError(f"任务目录不存在: {task_dir}")
        
        # 加载训练、验证、测试数据
        datasets = {}
        
        for split in ['train', 'dev', 'test']:
            file_path = os.path.join(task_dir, f"{split}.tsv")
            
            if os.path.exists(file_path):
                # 使用pandas读取，然后转换为Spark DataFrame
                pandas_df = pd.read_csv(file_path, sep='\t')
                spark_df = self.spark.createDataFrame(pandas_df)
                
                datasets[split] = spark_df
                logger.info(f"✅ 加载{split}数据: {spark_df.count()}条记录")
            else:
                logger.warning(f"⚠️ 文件不存在: {file_path}")
        
        return datasets
    
    def preprocess_data(self, datasets, task_name):
        """预处理数据"""
        logger.info(f"🔄 预处理数据: {task_name}")
        
        processed_datasets = {}
        
        for split, df in datasets.items():
            if task_name == "SST-2":
                # SST-2: 单句情感分析
                processed_df = df.select(
                    col("sentence").alias("text"),
                    col("label").alias("labels") if "label" in df.columns else lit(None).alias("labels")
                )
            
            elif task_name == "RTE":
                # RTE: 文本蕴含，需要组合两个句子
                if "sentence1" in df.columns and "sentence2" in df.columns:
                    processed_df = df.select(
                        col("sentence1"),
                        col("sentence2"),
                        col("label").alias("labels") if "label" in df.columns else lit(None).alias("labels")
                    )
                else:
                    processed_df = df
            
            else:
                # 其他任务的默认处理
                processed_df = df
            
            processed_datasets[split] = processed_df
            logger.info(f"✅ {split}数据预处理完成")
        
        return processed_datasets
    
    def get_data_statistics(self, datasets, task_name):
        """获取数据统计信息"""
        logger.info(f"📊 计算数据统计: {task_name}")
        
        stats = {
            "task_name": task_name,
            "splits": {}
        }
        
        for split, df in datasets.items():
            split_stats = {
                "count": df.count(),
                "columns": df.columns
            }
            
            # 如果有标签列，计算标签分布
            if "labels" in df.columns:
                label_dist = df.groupBy("labels").count().collect()
                split_stats["label_distribution"] = {
                    str(row["labels"]): row["count"] for row in label_dist
                }
            
            stats["splits"][split] = split_stats
        
        return stats
    
    def convert_to_pandas(self, spark_df):
        """将Spark DataFrame转换为Pandas DataFrame"""
        return spark_df.toPandas()
    
    def save_processed_data(self, datasets, task_name, output_dir):
        """保存处理后的数据"""
        logger.info(f"💾 保存处理后的数据: {task_name}")
        
        task_output_dir = os.path.join(output_dir, task_name)
        os.makedirs(task_output_dir, exist_ok=True)
        
        for split, df in datasets.items():
            output_path = os.path.join(task_output_dir, f"{split}_processed.parquet")
            
            # 保存为Parquet格式（Spark原生格式）
            df.write.mode("overwrite").parquet(output_path)
            logger.info(f"✅ 保存{split}数据到: {output_path}")
    
    def close(self):
        """关闭Spark会话"""
        if self.spark:
            self.spark.stop()
            logger.info("✅ Spark会话已关闭")

class LocalGLUEDataProcessor:
    """本地GLUE数据处理器"""
    
    def __init__(self):
        self.spark_processor = SparkDataProcessor()
        
    def load_data(self, task_name):
        """加载数据"""
        datasets = self.spark_processor.load_task_data(task_name)
        processed_datasets = self.spark_processor.preprocess_data(datasets, task_name)
        
        # 转换为pandas格式以兼容现有代码
        pandas_datasets = {}
        for split, spark_df in processed_datasets.items():
            pandas_datasets[split] = self.spark_processor.convert_to_pandas(spark_df)
        
        return pandas_datasets
    
    def get_statistics(self, task_name):
        """获取数据统计"""
        datasets = self.spark_processor.load_task_data(task_name)
        return self.spark_processor.get_data_statistics(datasets, task_name)
    
    def close(self):
        """关闭资源"""
        self.spark_processor.close()
