"""
Spark数据处理模块
使用PySpark读取和预处理GLUE数据集
"""
import os
import pandas as pd
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, when, isnan, isnull, trim, length
from pyspark.sql.types import StructType, StructField, StringType, IntegerType, FloatType
from typing import Dict, List, Tuple, Optional
import logging

from config import Config

class GLUEDataProcessor:
    """GLUE数据集处理器"""
    
    def __init__(self):
        """初始化Spark会话和配置"""
        self.config = Config()
        self.spark = self._create_spark_session()
        self.logger = self._setup_logger()
        
    def _create_spark_session(self) -> SparkSession:
        """创建Spark会话"""
        spark = SparkSession.builder \
            .appName(self.config.SPARK_APP_NAME) \
            .master(self.config.SPARK_MASTER) \
            .config("spark.executor.memory", self.config.SPARK_MEMORY) \
            .config("spark.driver.memory", self.config.SPARK_MEMORY) \
            .config("spark.sql.adaptive.enabled", "true") \
            .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
            .getOrCreate()
        
        spark.sparkContext.setLogLevel("WARN")
        return spark
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def load_task_data(self, task_name: str) -> Dict[str, pd.DataFrame]:
        """
        加载指定任务的数据
        
        Args:
            task_name: GLUE任务名称
            
        Returns:
            包含train, dev, test数据的字典
        """
        self.logger.info(f"Loading data for task: {task_name}")
        
        task_path = os.path.join(self.config.DATA_ROOT, task_name)
        data_files = {
            'train': os.path.join(task_path, 'train.tsv'),
            'dev': os.path.join(task_path, 'dev.tsv'),
            'test': os.path.join(task_path, 'test.tsv')
        }
        
        datasets = {}
        for split, file_path in data_files.items():
            if os.path.exists(file_path):
                # 使用Spark读取TSV文件
                df_spark = self.spark.read \
                    .option("header", "true") \
                    .option("sep", "\t") \
                    .option("quote", '"') \
                    .option("escape", '"') \
                    .csv(file_path)
                
                # 转换为Pandas DataFrame以便后续处理
                df_pandas = df_spark.toPandas()
                datasets[split] = self._clean_dataframe(df_pandas, task_name)
                
                self.logger.info(f"Loaded {split} set: {len(df_pandas)} samples")
            else:
                self.logger.warning(f"File not found: {file_path}")
        
        return datasets
    
    def _clean_dataframe(self, df: pd.DataFrame, task_name: str) -> pd.DataFrame:
        """
        清洗数据框
        
        Args:
            df: 原始数据框
            task_name: 任务名称
            
        Returns:
            清洗后的数据框
        """
        # 移除空行
        df = df.dropna(how='all')
        
        # 获取任务配置
        task_config = self.config.get_task_config(task_name)
        text_columns = task_config.get('text_column', [])
        if isinstance(text_columns, str):
            text_columns = [text_columns]
        
        # 清洗文本列
        for col_name in text_columns:
            if col_name in df.columns:
                # 移除前后空格
                df[col_name] = df[col_name].astype(str).str.strip()
                # 移除空字符串
                df = df[df[col_name] != '']
        
        # 处理标签列
        label_column = task_config.get('label_column')
        if label_column and label_column in df.columns:
            if task_name == 'STS-B':
                # STS-B是回归任务，标签是浮点数
                df[label_column] = pd.to_numeric(df[label_column], errors='coerce')
                df = df.dropna(subset=[label_column])
            else:
                # 分类任务，确保标签是整数
                df[label_column] = pd.to_numeric(df[label_column], errors='coerce')
                df = df.dropna(subset=[label_column])
                df[label_column] = df[label_column].astype(int)
        
        return df
    
    def preprocess_for_model(self, datasets: Dict[str, pd.DataFrame], 
                           task_name: str) -> Dict[str, pd.DataFrame]:
        """
        为模型训练预处理数据
        
        Args:
            datasets: 原始数据集字典
            task_name: 任务名称
            
        Returns:
            预处理后的数据集字典
        """
        self.logger.info(f"Preprocessing data for task: {task_name}")
        
        task_config = self.config.get_task_config(task_name)
        text_columns = task_config.get('text_column', [])
        if isinstance(text_columns, str):
            text_columns = [text_columns]
        
        processed_datasets = {}
        
        for split, df in datasets.items():
            processed_df = df.copy()
            
            # 创建组合文本列
            if len(text_columns) == 1:
                processed_df['text'] = processed_df[text_columns[0]]
            elif len(text_columns) == 2:
                # 对于句子对任务，使用[SEP]分隔符连接
                processed_df['text'] = (
                    processed_df[text_columns[0]].astype(str) + 
                    " [SEP] " + 
                    processed_df[text_columns[1]].astype(str)
                )
            
            # 确保有labels列（测试集可能没有）
            label_column = task_config.get('label_column')
            if label_column and label_column in processed_df.columns:
                processed_df['labels'] = processed_df[label_column]
            
            processed_datasets[split] = processed_df
            
        return processed_datasets
    
    def get_data_statistics(self, datasets: Dict[str, pd.DataFrame], 
                          task_name: str) -> Dict:
        """
        获取数据集统计信息
        
        Args:
            datasets: 数据集字典
            task_name: 任务名称
            
        Returns:
            统计信息字典
        """
        stats = {
            'task_name': task_name,
            'splits': {}
        }
        
        for split, df in datasets.items():
            split_stats = {
                'num_samples': len(df),
                'columns': list(df.columns)
            }
            
            # 文本长度统计
            if 'text' in df.columns:
                text_lengths = df['text'].str.len()
                split_stats['text_length'] = {
                    'mean': float(text_lengths.mean()),
                    'std': float(text_lengths.std()),
                    'min': int(text_lengths.min()),
                    'max': int(text_lengths.max())
                }
            
            # 标签分布统计
            if 'labels' in df.columns:
                label_counts = df['labels'].value_counts().to_dict()
                split_stats['label_distribution'] = {
                    str(k): int(v) for k, v in label_counts.items()
                }
            
            stats['splits'][split] = split_stats
        
        return stats
    
    def close(self):
        """关闭Spark会话"""
        if self.spark:
            self.spark.stop()
            self.logger.info("Spark session closed")
