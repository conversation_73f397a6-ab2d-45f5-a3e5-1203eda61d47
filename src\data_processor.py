"""
数据处理模块 - 基于Spark的GLUE数据集处理
"""
import os
import logging
from typing import Dict, List, Tuple, Optional, Any
from pyspark.sql import SparkSession, DataFrame
from pyspark.sql.functions import col, lit, when, regexp_replace, trim, length
from pyspark.sql.types import StructType, StructField, StringType, IntegerType, FloatType
import pandas as pd

logger = logging.getLogger(__name__)

class GLUEDataProcessor:
    """GLUE数据集处理器"""
    
    def __init__(self, spark: SparkSession, data_dir: str):
        self.spark = spark
        self.data_dir = data_dir
        self.task_schemas = self._get_task_schemas()
        
    def _get_task_schemas(self) -> Dict[str, StructType]:
        """定义各任务的数据模式"""
        schemas = {
            "CoLA": StructType([
                StructField("source", StringType(), True),
                StructField("label", IntegerType(), True),
                StructField("label_notes", StringType(), True),
                StructField("sentence", StringType(), True)
            ]),
            "SST-2": StructType([
                StructField("sentence", StringType(), True),
                StructField("label", IntegerType(), True)
            ]),
            "MRPC": StructType([
                StructField("Quality", StringType(), True),
                StructField("id1", StringType(), True),
                StructField("id2", StringType(), True),
                StructField("string1", StringType(), True),
                StructField("string2", StringType(), True)
            ]),
            "STS-B": StructType([
                StructField("genre", StringType(), True),
                StructField("filename", StringType(), True),
                StructField("year", StringType(), True),
                StructField("old_index", StringType(), True),
                StructField("source1", StringType(), True),
                StructField("source2", StringType(), True),
                StructField("sentence1", StringType(), True),
                StructField("sentence2", StringType(), True),
                StructField("score", FloatType(), True)
            ]),
            "QQP": StructType([
                StructField("id", StringType(), True),
                StructField("qid1", StringType(), True),
                StructField("qid2", StringType(), True),
                StructField("question1", StringType(), True),
                StructField("question2", StringType(), True),
                StructField("is_duplicate", IntegerType(), True)
            ]),
            "MNLI": StructType([
                StructField("index", StringType(), True),
                StructField("promptID", StringType(), True),
                StructField("pairID", StringType(), True),
                StructField("genre", StringType(), True),
                StructField("sentence1_binary_parse", StringType(), True),
                StructField("sentence2_binary_parse", StringType(), True),
                StructField("sentence1_parse", StringType(), True),
                StructField("sentence2_parse", StringType(), True),
                StructField("sentence1", StringType(), True),
                StructField("sentence2", StringType(), True),
                StructField("label1", StringType(), True),
                StructField("gold_label", StringType(), True)
            ]),
            "QNLI": StructType([
                StructField("index", StringType(), True),
                StructField("question", StringType(), True),
                StructField("sentence", StringType(), True),
                StructField("label", StringType(), True)
            ]),
            "RTE": StructType([
                StructField("index", StringType(), True),
                StructField("sentence1", StringType(), True),
                StructField("sentence2", StringType(), True),
                StructField("label", StringType(), True)
            ]),
            "WNLI": StructType([
                StructField("index", StringType(), True),
                StructField("sentence1", StringType(), True),
                StructField("sentence2", StringType(), True),
                StructField("label", IntegerType(), True)
            ])
        }
        return schemas
    
    def load_task_data(self, task: str, split: str = "train") -> DataFrame:
        """加载指定任务的数据"""
        task_dir = os.path.join(self.data_dir, task)
        
        if not os.path.exists(task_dir):
            raise FileNotFoundError(f"任务目录不存在: {task_dir}")
        
        # 根据任务和分割确定文件名
        file_mapping = self._get_file_mapping(task, split)
        file_path = os.path.join(task_dir, file_mapping)
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"数据文件不存在: {file_path}")
        
        logger.info(f"加载 {task} 任务的 {split} 数据: {file_path}")
        
        # 读取数据
        if file_path.endswith('.tsv'):
            # 某些任务没有标题行或格式特殊
            if task == "CoLA" and split in ["train", "dev"]:
                df = self.spark.read.option("header", "false").option("sep", "\t").csv(file_path)
                # 为CoLA训练和验证集添加列名
                df = df.toDF("source", "label", "label_notes", "sentence")
            elif task == "CoLA" and split == "test":
                # CoLA测试集有标题行且只有2列
                df = self.spark.read.option("header", "true").option("sep", "\t").csv(file_path)
            elif task == "MRPC" and split == "train":
                df = self.spark.read.option("header", "true").option("sep", "\t").csv(file_path)
            else:
                df = self.spark.read.option("header", "true").option("sep", "\t").csv(file_path)
        else:
            df = self.spark.read.option("header", "true").csv(file_path)
        
        # 数据清洗和预处理
        df = self._preprocess_data(df, task, split)
        
        return df

    def _get_text_columns(self, task: str) -> List[str]:
        """获取文本列名"""
        text_columns_mapping = {
            "CoLA": ["sentence"],
            "SST-2": ["sentence"],
            "MRPC": ["string1", "string2"],
            "STS-B": ["sentence1", "sentence2"],
            "QQP": ["question1", "question2"],
            "MNLI": ["sentence1", "sentence2"],
            "QNLI": ["question", "sentence"],
            "RTE": ["sentence1", "sentence2"],
            "WNLI": ["sentence1", "sentence2"]
        }
        return text_columns_mapping.get(task, [])

    def _process_labels(self, df: DataFrame, task: str, split: str) -> DataFrame:
        """处理标签"""
        if split == "test":
            # 测试集通常没有标签
            return df

        if task == "CoLA":
            # CoLA: 0=不可接受, 1=可接受
            pass
        elif task == "SST-2":
            # SST-2: 0=负面, 1=正面
            pass
        elif task == "MRPC":
            # MRPC: 0=不等价, 1=等价
            if "Quality" in df.columns:
                df = df.withColumn("label", col("Quality").cast(IntegerType()))
        elif task == "STS-B":
            # STS-B: 回归任务，分数0-5
            pass
        elif task == "QQP":
            # QQP: 0=不重复, 1=重复
            pass
        elif task == "MNLI":
            # MNLI: contradiction, entailment, neutral
            label_mapping = {"contradiction": 0, "entailment": 1, "neutral": 2}
            for label, idx in label_mapping.items():
                df = df.withColumn("label",
                    when(col("gold_label") == label, lit(idx))
                    .otherwise(col("label") if "label" in df.columns else lit(-1)))
        elif task in ["QNLI", "RTE"]:
            # QNLI/RTE: not_entailment, entailment
            label_mapping = {"not_entailment": 0, "entailment": 1}
            for label, idx in label_mapping.items():
                df = df.withColumn("label",
                    when(col("label") == label, lit(idx))
                    .otherwise(col("label") if "label" in df.columns else lit(-1)))
        elif task == "WNLI":
            # WNLI: 0, 1
            pass

        return df

    def _task_specific_preprocessing(self, df: DataFrame, task: str, split: str) -> DataFrame:
        """任务特定的预处理"""
        if task == "MRPC":
            # MRPC需要重命名列
            if "string1" in df.columns and "string2" in df.columns:
                df = df.withColumnRenamed("string1", "sentence1")
                df = df.withColumnRenamed("string2", "sentence2")

        elif task == "QQP":
            # QQP需要重命名列
            if "question1" in df.columns and "question2" in df.columns:
                df = df.withColumnRenamed("question1", "sentence1")
                df = df.withColumnRenamed("question2", "sentence2")

        elif task == "QNLI":
            # QNLI需要重命名列
            if "question" in df.columns and "sentence" in df.columns:
                df = df.withColumnRenamed("question", "sentence1")
                df = df.withColumnRenamed("sentence", "sentence2")

        return df

    def get_task_statistics(self, task: str) -> Dict[str, Any]:
        """获取任务统计信息"""
        stats = {}

        try:
            # 加载训练、验证和测试数据
            train_df = self.load_task_data(task, "train")
            dev_df = self.load_task_data(task, "dev")

            stats["train_size"] = train_df.count()
            stats["dev_size"] = dev_df.count()

            # 尝试加载测试数据
            try:
                test_df = self.load_task_data(task, "test")
                stats["test_size"] = test_df.count()
            except FileNotFoundError:
                stats["test_size"] = 0

            # 标签分布（如果有标签）
            if "label" in train_df.columns:
                label_dist = train_df.groupBy("label").count().collect()
                stats["label_distribution"] = {row["label"]: row["count"] for row in label_dist}

            # 文本长度统计
            text_columns = self._get_text_columns(task)
            for col_name in text_columns:
                if col_name in train_df.columns:
                    lengths = train_df.select(length(col(col_name)).alias("length")).collect()
                    lengths = [row["length"] for row in lengths if row["length"] is not None]
                    if lengths:
                        stats[f"{col_name}_avg_length"] = sum(lengths) / len(lengths)
                        stats[f"{col_name}_max_length"] = max(lengths)
                        stats[f"{col_name}_min_length"] = min(lengths)

        except Exception as e:
            logger.error(f"获取 {task} 统计信息时出错: {e}")
            stats["error"] = str(e)

        return stats

    def prepare_training_data(self, task: str, tokenizer, max_length: int = 128) -> Tuple[DataFrame, DataFrame]:
        """准备训练数据"""
        train_df = self.load_task_data(task, "train")
        dev_df = self.load_task_data(task, "dev")

        # 标准化列名
        train_df = self._standardize_columns(train_df, task)
        dev_df = self._standardize_columns(dev_df, task)

        return train_df, dev_df

    def _standardize_columns(self, df: DataFrame, task: str) -> DataFrame:
        """标准化列名"""
        # 确保所有数据都有统一的列名格式
        if task in ["MRPC", "STS-B", "QQP", "MNLI", "RTE", "WNLI"]:
            # 双句子任务
            required_columns = ["sentence1", "sentence2"]
        elif task in ["QNLI"]:
            # 问答任务
            required_columns = ["sentence1", "sentence2"]  # question -> sentence1, sentence -> sentence2
        else:
            # 单句子任务
            required_columns = ["sentence"]

        # 添加标签列（如果存在）
        if "label" in df.columns:
            required_columns.append("label")

        # 选择需要的列
        available_columns = [col for col in required_columns if col in df.columns]
        df = df.select(*available_columns)

        return df
    
    def _get_file_mapping(self, task: str, split: str) -> str:
        """获取文件名映射"""
        file_mappings = {
            "CoLA": {
                "train": "train.tsv",
                "dev": "dev.tsv", 
                "test": "test.tsv"
            },
            "SST-2": {
                "train": "train.tsv",
                "dev": "dev.tsv",
                "test": "test.tsv"
            },
            "MRPC": {
                "train": "msr_paraphrase_train.txt",
                "dev": "msr_paraphrase_test.txt",
                "test": "test.tsv"
            },
            "STS-B": {
                "train": "train.tsv",
                "dev": "dev.tsv",
                "test": "test.tsv"
            },
            "QQP": {
                "train": "train.tsv",
                "dev": "dev.tsv", 
                "test": "test.tsv"
            },
            "MNLI": {
                "train": "train.tsv",
                "dev": "dev_matched.tsv",
                "dev_mismatched": "dev_mismatched.tsv",
                "test": "test_matched.tsv",
                "test_mismatched": "test_mismatched.tsv"
            },
            "QNLI": {
                "train": "train.tsv",
                "dev": "dev.tsv",
                "test": "test.tsv"
            },
            "RTE": {
                "train": "train.tsv", 
                "dev": "dev.tsv",
                "test": "test.tsv"
            },
            "WNLI": {
                "train": "train.tsv",
                "dev": "dev.tsv",
                "test": "test.tsv"
            }
        }
        
        return file_mappings[task][split]
    
    def _preprocess_data(self, df: DataFrame, task: str, split: str) -> DataFrame:
        """数据预处理"""
        # 基本清洗：去除空值和空白字符
        text_columns = self._get_text_columns(task)
        
        for col_name in text_columns:
            if col_name in df.columns:
                df = df.withColumn(col_name, trim(col(col_name)))
                df = df.filter(col(col_name).isNotNull() & (length(col(col_name)) > 0))
        
        # 标签处理
        df = self._process_labels(df, task, split)
        
        # 任务特定的预处理
        df = self._task_specific_preprocessing(df, task, split)
        
        return df
