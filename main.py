"""
GLUE基准测试主程序
基于Apache Spark和BERT的文本分类系统

使用方法:
    python main.py --task CoLA --mode train
    python main.py --task CoLA --mode predict
    python main.py --task all --mode train
"""
import os
import sys
import argparse
import logging
from typing import Dict, Any, Optional
from pyspark.sql import SparkSession

# 设置环境变量避免TensorFlow依赖
os.environ['USE_TF'] = 'NO'
os.environ['USE_TORCH'] = 'YES'

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from model_manager import GLUEModelManager
from trainer import GLUETrainer, SparkTrainer
from predictor import GLUEPredictor, SparkPredictor
from data_processor import GLUEDataProcessor
from utils import setup_logging, set_seed, get_device
import config

logger = logging.getLogger(__name__)

class GLUEBenchmark:
    """GLUE基准测试主类"""
    
    def __init__(self, spark_master: str = "local[*]"):
        self.spark = self._create_spark_session(spark_master)
        self.data_processor = GLUEDataProcessor(self.spark, config.DATA_DIR)
        
        # 设置随机种子
        set_seed(config.RANDOM_SEED)
        
        logger.info("GLUE基准测试系统初始化完成")
    
    def _create_spark_session(self, master: str) -> SparkSession:
        """创建Spark会话"""
        spark = SparkSession.builder \
            .appName(config.SPARK_APP_NAME) \
            .master(master) \
            .config("spark.driver.memory", "4g") \
            .config("spark.executor.memory", "4g") \
            .config("spark.sql.adaptive.enabled", "true") \
            .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
            .getOrCreate()
        
        logger.info(f"Spark会话创建成功，版本: {spark.version}")
        return spark
    
    def train_task(self, task: str, epochs: int = None, learning_rate: float = None,
                   batch_size: int = None, max_samples: int = None) -> Dict[str, Any]:
        """训练单个任务"""
        logger.info(f"开始训练任务: {task}")
        
        # 检查任务是否支持
        if task not in config.GLUE_TASKS:
            raise ValueError(f"不支持的任务: {task}，支持的任务: {config.GLUE_TASKS}")
        
        try:
            # 1. 加载数据
            logger.info("加载训练和验证数据...")
            train_df = self.data_processor.load_task_data(task, "train")
            eval_df = self.data_processor.load_task_data(task, "dev")
            
            # 限制样本数量（用于快速测试）
            if max_samples:
                train_df = train_df.limit(max_samples)
                eval_df = eval_df.limit(max_samples // 5)
            
            logger.info(f"数据加载完成: 训练集={train_df.count()}, 验证集={eval_df.count()}")
            
            # 2. 初始化模型
            logger.info("初始化模型...")
            model_manager = GLUEModelManager()
            model_manager.load_tokenizer()
            model_manager.load_model_for_task(task)
            
            # 3. 创建训练器
            output_dir = os.path.join(config.MODEL_SAVE_DIR, task)
            trainer = GLUETrainer(model_manager, task, output_dir)
            
            # 4. 准备数据
            train_loader, eval_loader = trainer.prepare_data(train_df, eval_df)
            
            # 5. 执行训练
            training_args = {
                'epochs': epochs or config.EPOCHS,
                'learning_rate': learning_rate or config.LEARNING_RATE
            }
            
            logger.info(f"开始训练，参数: {training_args}")
            results = trainer.train(train_loader, eval_loader, **training_args)
            
            logger.info(f"任务 {task} 训练完成!")
            logger.info(f"最佳指标: {results['best_metric']:.4f}")
            logger.info(f"训练时间: {results['total_time']:.2f}秒")
            
            return results
            
        except Exception as e:
            logger.error(f"训练任务 {task} 失败: {e}")
            raise
    
    def predict_task(self, task: str, model_path: str = None, 
                    output_path: str = None) -> str:
        """预测单个任务"""
        logger.info(f"开始预测任务: {task}")
        
        try:
            # 1. 加载模型
            model_manager = GLUEModelManager()
            
            if model_path and os.path.exists(model_path):
                # 加载已训练的模型
                model_manager.load_model(model_path)
                logger.info(f"加载已训练模型: {model_path}")
            else:
                # 使用预训练模型
                model_manager.load_tokenizer()
                model_manager.load_model_for_task(task)
                logger.info("使用预训练模型进行预测")
            
            # 2. 加载测试数据
            logger.info("加载测试数据...")
            test_df = self.data_processor.load_task_data(task, "test")
            logger.info(f"测试数据加载完成: {test_df.count()} 样本")
            
            # 3. 创建预测器
            predictor = GLUEPredictor(model_manager, task)
            
            # 4. 准备测试文本
            test_rows = test_df.collect()
            test_texts = []
            
            # 根据任务类型准备文本
            text_columns = self.data_processor._get_text_columns(task)
            
            for row in test_rows:
                if len(text_columns) == 1:
                    # 单句子任务
                    if hasattr(row, 'sentence'):
                        test_texts.append(row.sentence)
                    else:
                        # 查找文本列
                        for col in row.asDict().keys():
                            if 'sentence' in col.lower() or 'text' in col.lower():
                                test_texts.append(row[col])
                                break
                else:
                    # 句子对任务
                    if hasattr(row, 'sentence1') and hasattr(row, 'sentence2'):
                        test_texts.append((row.sentence1, row.sentence2))
                    else:
                        # 查找句子对列
                        text_cols = [col for col in row.asDict().keys() 
                                   if 'sentence' in col.lower() or 'text' in col.lower()]
                        if len(text_cols) >= 2:
                            test_texts.append((row[text_cols[0]], row[text_cols[1]]))
            
            logger.info(f"准备预测文本: {len(test_texts)} 个样本")
            
            # 5. 执行预测
            if not output_path:
                output_path = os.path.join(config.RESULTS_DIR, f"{task}_submission.tsv")
            
            # 获取任务标签
            task_labels = None
            if config.TASK_TYPES.get(task) != "regression":
                if task == "MNLI":
                    task_labels = ["contradiction", "entailment", "neutral"]
                elif task in ["QNLI", "RTE"]:
                    task_labels = ["not_entailment", "entailment"]
                else:
                    task_labels = None  # 使用数字标签
            
            submission_path = predictor.generate_submission(
                test_texts, output_path, task_labels
            )
            
            logger.info(f"任务 {task} 预测完成!")
            logger.info(f"提交文件: {submission_path}")
            
            return submission_path
            
        except Exception as e:
            logger.error(f"预测任务 {task} 失败: {e}")
            raise
    
    def train_all_tasks(self, epochs: int = None, max_samples: int = None) -> Dict[str, Any]:
        """训练所有GLUE任务"""
        logger.info("开始训练所有GLUE任务")
        
        results = {}
        failed_tasks = []
        
        for task in config.GLUE_TASKS:
            try:
                logger.info(f"\n{'='*60}")
                logger.info(f"训练任务: {task}")
                logger.info(f"{'='*60}")
                
                task_results = self.train_task(
                    task, 
                    epochs=epochs, 
                    max_samples=max_samples
                )
                results[task] = task_results
                
                logger.info(f"✅ {task} 训练成功")
                
            except Exception as e:
                logger.error(f"❌ {task} 训练失败: {e}")
                failed_tasks.append(task)
                results[task] = {"error": str(e)}
        
        # 总结
        successful_tasks = len(results) - len(failed_tasks)
        logger.info(f"\n{'='*60}")
        logger.info(f"训练总结")
        logger.info(f"{'='*60}")
        logger.info(f"成功: {successful_tasks}/{len(config.GLUE_TASKS)}")
        logger.info(f"失败: {failed_tasks}")
        
        return results
    
    def predict_all_tasks(self) -> Dict[str, str]:
        """预测所有GLUE任务"""
        logger.info("开始预测所有GLUE任务")
        
        submission_files = {}
        failed_tasks = []
        
        for task in config.GLUE_TASKS:
            try:
                logger.info(f"\n{'='*60}")
                logger.info(f"预测任务: {task}")
                logger.info(f"{'='*60}")
                
                # 检查是否有已训练的模型
                model_path = os.path.join(config.MODEL_SAVE_DIR, task, "best_model_hf")
                if not os.path.exists(model_path):
                    model_path = None
                
                submission_path = self.predict_task(task, model_path)
                submission_files[task] = submission_path
                
                logger.info(f"✅ {task} 预测成功")
                
            except Exception as e:
                logger.error(f"❌ {task} 预测失败: {e}")
                failed_tasks.append(task)
                submission_files[task] = f"ERROR: {str(e)}"
        
        # 总结
        successful_tasks = len([f for f in submission_files.values() if not f.startswith("ERROR")])
        logger.info(f"\n{'='*60}")
        logger.info(f"预测总结")
        logger.info(f"{'='*60}")
        logger.info(f"成功: {successful_tasks}/{len(config.GLUE_TASKS)}")
        logger.info(f"失败: {failed_tasks}")
        
        return submission_files
    
    def cleanup(self):
        """清理资源"""
        if self.spark:
            self.spark.stop()
            logger.info("Spark会话已关闭")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="GLUE基准测试 - 基于Spark和BERT的文本分类系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 训练单个任务
  python main.py --task CoLA --mode train --epochs 3

  # 预测单个任务
  python main.py --task CoLA --mode predict

  # 训练所有任务
  python main.py --task all --mode train --epochs 1 --max-samples 1000

  # 预测所有任务
  python main.py --task all --mode predict

  # 快速测试
  python main.py --task CoLA --mode train --epochs 1 --max-samples 100
        """
    )

    parser.add_argument(
        '--task',
        type=str,
        required=True,
        choices=config.GLUE_TASKS + ['all'],
        help='要执行的GLUE任务或"all"表示所有任务'
    )

    parser.add_argument(
        '--mode',
        type=str,
        required=True,
        choices=['train', 'predict', 'both'],
        help='执行模式: train(训练), predict(预测), both(训练+预测)'
    )

    parser.add_argument(
        '--epochs',
        type=int,
        default=config.EPOCHS,
        help=f'训练轮数 (默认: {config.EPOCHS})'
    )

    parser.add_argument(
        '--learning-rate',
        type=float,
        default=config.LEARNING_RATE,
        help=f'学习率 (默认: {config.LEARNING_RATE})'
    )

    parser.add_argument(
        '--batch-size',
        type=int,
        default=config.BATCH_SIZE,
        help=f'批次大小 (默认: {config.BATCH_SIZE})'
    )

    parser.add_argument(
        '--max-samples',
        type=int,
        default=None,
        help='最大训练样本数 (用于快速测试)'
    )

    parser.add_argument(
        '--model-path',
        type=str,
        default=None,
        help='预训练模型路径 (预测时使用)'
    )

    parser.add_argument(
        '--output-dir',
        type=str,
        default=config.RESULTS_DIR,
        help=f'输出目录 (默认: {config.RESULTS_DIR})'
    )

    parser.add_argument(
        '--spark-master',
        type=str,
        default="local[*]",
        help='Spark主节点地址 (默认: local[*])'
    )

    parser.add_argument(
        '--log-level',
        type=str,
        default="INFO",
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        help='日志级别 (默认: INFO)'
    )

    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()

    # 设置日志
    logger = setup_logging(config.LOG_DIR, args.log_level)

    # 显示系统信息
    logger.info("="*80)
    logger.info("GLUE基准测试系统")
    logger.info("基于Apache Spark和BERT的文本分类")
    logger.info("="*80)
    logger.info(f"任务: {args.task}")
    logger.info(f"模式: {args.mode}")
    logger.info(f"设备: {get_device()}")
    logger.info(f"Spark主节点: {args.spark_master}")

    # 创建GLUE基准测试实例
    benchmark = None

    try:
        benchmark = GLUEBenchmark(args.spark_master)

        if args.task == "all":
            # 处理所有任务
            if args.mode in ["train", "both"]:
                logger.info("\n🚀 开始训练所有GLUE任务...")
                train_results = benchmark.train_all_tasks(
                    epochs=args.epochs,
                    max_samples=args.max_samples
                )

                # 保存训练结果
                import json
                results_file = os.path.join(args.output_dir, "all_tasks_training_results.json")
                with open(results_file, 'w', encoding='utf-8') as f:
                    json.dump(train_results, f, indent=2, ensure_ascii=False, default=str)
                logger.info(f"训练结果已保存: {results_file}")

            if args.mode in ["predict", "both"]:
                logger.info("\n🔮 开始预测所有GLUE任务...")
                submission_files = benchmark.predict_all_tasks()

                # 显示提交文件
                logger.info("\n📁 生成的提交文件:")
                for task, file_path in submission_files.items():
                    if not file_path.startswith("ERROR"):
                        logger.info(f"  {task}: {file_path}")
                    else:
                        logger.error(f"  {task}: {file_path}")

        else:
            # 处理单个任务
            if args.mode in ["train", "both"]:
                logger.info(f"\n🚀 开始训练任务: {args.task}")
                train_results = benchmark.train_task(
                    args.task,
                    epochs=args.epochs,
                    learning_rate=args.learning_rate,
                    batch_size=args.batch_size,
                    max_samples=args.max_samples
                )
                logger.info(f"✅ 任务 {args.task} 训练完成!")

            if args.mode in ["predict", "both"]:
                logger.info(f"\n🔮 开始预测任务: {args.task}")
                submission_path = benchmark.predict_task(
                    args.task,
                    model_path=args.model_path,
                    output_path=os.path.join(args.output_dir, f"{args.task}_submission.tsv")
                )
                logger.info(f"✅ 任务 {args.task} 预测完成!")
                logger.info(f"📁 提交文件: {submission_path}")

        logger.info("\n🎉 所有操作完成!")
        logger.info("\n📋 下一步:")
        logger.info("1. 检查生成的提交文件")
        logger.info("2. 访问 https://gluebenchmark.com/ 提交结果")
        logger.info("3. 查看模型性能和排名")

    except KeyboardInterrupt:
        logger.info("\n⚠️ 用户中断操作")
    except Exception as e:
        logger.error(f"\n❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        if benchmark:
            benchmark.cleanup()

    return 0

if __name__ == "__main__":
    exit(main())
