"""
主程序 - 基于Spark的GLUE文本分类项目
使用大语言模型进行文本分类任务
"""
import os
import sys
import argparse
import logging
from datetime import datetime
import json

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.trainer import GLUETrainer
from src.predictor import GLUEPredictor
from config import Config

def setup_logging():
    """设置全局日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('glue_project.log')
        ]
    )

def train_single_task(task_name: str):
    """
    训练单个任务
    
    Args:
        task_name: GLUE任务名称
    """
    logger = logging.getLogger(__name__)
    logger.info(f"Starting training for task: {task_name}")
    
    try:
        # 创建训练器
        trainer = GLUETrainer(task_name)
        
        # 运行完整训练流水线
        results = trainer.run_full_pipeline()
        
        logger.info(f"Training completed for task: {task_name}")
        return results
        
    except Exception as e:
        logger.error(f"Training failed for task {task_name}: {str(e)}")
        raise

def predict_single_task(task_name: str, model_path: str = None):
    """
    预测单个任务
    
    Args:
        task_name: GLUE任务名称
        model_path: 模型路径（可选）
    """
    logger = logging.getLogger(__name__)
    logger.info(f"Starting prediction for task: {task_name}")
    
    try:
        # 创建预测器
        predictor = GLUEPredictor(task_name, model_path)
        
        # 进行预测
        results = predictor.predict_and_submit()
        
        logger.info(f"Prediction completed for task: {task_name}")
        return results
        
    except Exception as e:
        logger.error(f"Prediction failed for task {task_name}: {str(e)}")
        raise

def train_all_tasks(tasks: list = None):
    """
    训练所有任务
    
    Args:
        tasks: 任务列表（可选）
    """
    config = Config()
    if tasks is None:
        tasks = config.TASKS
    
    logger = logging.getLogger(__name__)
    logger.info(f"Starting training for all tasks: {tasks}")
    
    all_results = {}
    
    for task in tasks:
        try:
            logger.info(f"Training task: {task}")
            results = train_single_task(task)
            all_results[task] = results
            logger.info(f"Task {task} training completed")
            
        except Exception as e:
            logger.error(f"Task {task} training failed: {str(e)}")
            all_results[task] = {'error': str(e)}
    
    # 保存所有结果
    summary_file = os.path.join(config.RESULTS_DIR, "all_tasks_training_summary.json")
    with open(summary_file, 'w') as f:
        json.dump(all_results, f, indent=2)
    
    logger.info(f"All tasks training summary saved to: {summary_file}")
    return all_results

def predict_all_tasks(tasks: list = None):
    """
    预测所有任务
    
    Args:
        tasks: 任务列表（可选）
    """
    config = Config()
    if tasks is None:
        tasks = config.TASKS
    
    logger = logging.getLogger(__name__)
    logger.info(f"Starting prediction for all tasks: {tasks}")
    
    # 使用批量预测功能
    predictor = GLUEPredictor(tasks[0])  # 创建一个实例用于批量预测
    results = predictor.batch_predict_all_tasks(tasks)
    
    logger.info("All tasks prediction completed")
    return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="GLUE文本分类项目")
    parser.add_argument('--mode', choices=['train', 'predict', 'both'], 
                       default='both', help='运行模式')
    parser.add_argument('--task', type=str, help='指定单个任务名称')
    parser.add_argument('--tasks', nargs='+', help='指定多个任务名称')
    parser.add_argument('--model-path', type=str, help='模型路径（用于预测）')
    parser.add_argument('--config', type=str, help='配置文件路径')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # 创建必要目录
    config = Config()
    config.create_directories()
    
    logger.info("GLUE文本分类项目启动")
    logger.info(f"运行模式: {args.mode}")
    
    try:
        if args.task:
            # 单任务模式
            if args.mode in ['train', 'both']:
                logger.info(f"训练任务: {args.task}")
                train_single_task(args.task)
            
            if args.mode in ['predict', 'both']:
                logger.info(f"预测任务: {args.task}")
                predict_single_task(args.task, args.model_path)
        
        else:
            # 多任务模式
            tasks = args.tasks if args.tasks else config.TASKS
            
            if args.mode in ['train', 'both']:
                logger.info(f"训练所有任务: {tasks}")
                train_all_tasks(tasks)
            
            if args.mode in ['predict', 'both']:
                logger.info(f"预测所有任务: {tasks}")
                predict_all_tasks(tasks)
        
        logger.info("项目执行完成")
        
    except Exception as e:
        logger.error(f"项目执行失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
