"""
工具函数模块
"""
import os
import random
import logging
import numpy as np
import torch
from datetime import datetime
from typing import Dict, Any, Optional

def set_seed(seed: int = 42):
    """设置随机种子以确保结果可复现"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    # 确保CUDA操作的确定性
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def setup_logging(log_dir: str, log_level: str = "INFO") -> logging.Logger:
    """设置日志配置"""
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建日志文件名（包含时间戳）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"glue_training_{timestamp}.log")
    
    # 配置日志格式
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 设置日志级别
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    # 配置日志
    logging.basicConfig(
        level=level,
        format=log_format,
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志已配置，日志文件: {log_file}")
    
    return logger

def get_device() -> torch.device:
    """获取可用的设备"""
    if torch.cuda.is_available():
        device = torch.device("cuda")
        print(f"使用GPU: {torch.cuda.get_device_name()}")
    else:
        device = torch.device("cpu")
        print("使用CPU")
    return device

def format_time(elapsed_time: float) -> str:
    """格式化时间显示"""
    hours = int(elapsed_time // 3600)
    minutes = int((elapsed_time % 3600) // 60)
    seconds = int(elapsed_time % 60)
    
    if hours > 0:
        return f"{hours}h {minutes}m {seconds}s"
    elif minutes > 0:
        return f"{minutes}m {seconds}s"
    else:
        return f"{seconds}s"

def save_config(config_dict: Dict[str, Any], save_path: str):
    """保存配置到文件"""
    import json
    
    # 确保目录存在
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    # 转换不可序列化的对象
    serializable_config = {}
    for key, value in config_dict.items():
        if isinstance(value, (str, int, float, bool, list, dict)):
            serializable_config[key] = value
        else:
            serializable_config[key] = str(value)
    
    with open(save_path, 'w', encoding='utf-8') as f:
        json.dump(serializable_config, f, indent=2, ensure_ascii=False)

def load_config(config_path: str) -> Dict[str, Any]:
    """从文件加载配置"""
    import json
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    return config

def create_directories(*dirs):
    """创建多个目录"""
    for directory in dirs:
        os.makedirs(directory, exist_ok=True)

def count_parameters(model) -> int:
    """计算模型参数数量"""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

def get_model_size(model) -> str:
    """获取模型大小信息"""
    param_count = count_parameters(model)
    
    if param_count >= 1e9:
        return f"{param_count / 1e9:.1f}B parameters"
    elif param_count >= 1e6:
        return f"{param_count / 1e6:.1f}M parameters"
    elif param_count >= 1e3:
        return f"{param_count / 1e3:.1f}K parameters"
    else:
        return f"{param_count} parameters"

class ProgressTracker:
    """训练进度跟踪器"""
    
    def __init__(self, total_steps: int):
        self.total_steps = total_steps
        self.current_step = 0
        self.start_time = None
        self.step_times = []
    
    def start(self):
        """开始计时"""
        import time
        self.start_time = time.time()
    
    def step(self):
        """更新步数"""
        import time
        current_time = time.time()
        
        if self.start_time is not None:
            step_time = current_time - self.start_time
            self.step_times.append(step_time)
            self.start_time = current_time
        
        self.current_step += 1
    
    def get_progress(self) -> Dict[str, Any]:
        """获取进度信息"""
        progress = self.current_step / self.total_steps
        
        result = {
            "current_step": self.current_step,
            "total_steps": self.total_steps,
            "progress": progress,
            "progress_percent": f"{progress * 100:.1f}%"
        }
        
        if self.step_times:
            avg_step_time = np.mean(self.step_times[-10:])  # 最近10步的平均时间
            remaining_steps = self.total_steps - self.current_step
            estimated_remaining_time = avg_step_time * remaining_steps
            
            result.update({
                "avg_step_time": avg_step_time,
                "estimated_remaining_time": format_time(estimated_remaining_time)
            })
        
        return result
