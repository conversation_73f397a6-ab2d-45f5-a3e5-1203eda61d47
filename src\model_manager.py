"""
大语言模型管理模块
集成Hugging Face Transformers，实现模型加载和微调
"""
import os
import torch
import numpy as np
from transformers import (
    AutoTokenizer, AutoModelForSequenceClassification,
    TrainingArguments, Trainer, EarlyStoppingCallback,
    DataCollatorWithPadding
)
from datasets import Dataset
from sklearn.metrics import accuracy_score, f1_score, matthews_corrcoef
from scipy.stats import pearsonr
import logging
from typing import Dict, List, Tuple, Optional, Any
import json

from config import Config

class GLUEModelManager:
    """GLUE任务模型管理器"""
    
    def __init__(self, task_name: str):
        """
        初始化模型管理器
        
        Args:
            task_name: GLUE任务名称
        """
        self.task_name = task_name
        self.config = Config()
        self.task_config = self.config.get_task_config(task_name)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 设置随机种子
        torch.manual_seed(self.config.SEED)
        np.random.seed(self.config.SEED)
        
        self.logger = self._setup_logger()
        self.tokenizer = None
        self.model = None
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(f"{__name__}.{self.task_name}")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def load_model_and_tokenizer(self) -> Tuple[Any, Any]:
        """
        加载预训练模型和分词器
        
        Returns:
            (model, tokenizer) 元组
        """
        self.logger.info(f"Loading model: {self.config.MODEL_NAME}")
        
        # 加载分词器
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.config.MODEL_NAME,
            use_fast=True
        )
        
        # 获取标签数量
        num_labels = self.task_config.get('num_labels', 2)
        
        # 加载模型
        if self.task_name == 'STS-B':
            # STS-B是回归任务
            self.model = AutoModelForSequenceClassification.from_pretrained(
                self.config.MODEL_NAME,
                num_labels=1,
                problem_type="regression"
            )
        else:
            # 分类任务
            self.model = AutoModelForSequenceClassification.from_pretrained(
                self.config.MODEL_NAME,
                num_labels=num_labels,
                problem_type="single_label_classification"
            )
        
        self.model.to(self.device)
        
        self.logger.info(f"Model loaded on device: {self.device}")
        return self.model, self.tokenizer
    
    def tokenize_dataset(self, dataset_dict: Dict[str, Any]) -> Dict[str, Dataset]:
        """
        对数据集进行分词
        
        Args:
            dataset_dict: 包含文本数据的字典
            
        Returns:
            分词后的数据集字典
        """
        if not self.tokenizer:
            raise ValueError("Tokenizer not loaded. Call load_model_and_tokenizer first.")
        
        def tokenize_function(examples):
            # 分词
            tokenized = self.tokenizer(
                examples['text'],
                truncation=True,
                padding=False,  # 动态padding
                max_length=self.config.MAX_LENGTH,
                return_tensors=None
            )
            
            # 添加标签（如果存在）
            if 'labels' in examples:
                if self.task_name == 'STS-B':
                    # STS-B标签需要归一化到0-1范围
                    tokenized['labels'] = [float(label) / 5.0 for label in examples['labels']]
                else:
                    tokenized['labels'] = examples['labels']
            
            return tokenized
        
        tokenized_datasets = {}
        
        for split, df in dataset_dict.items():
            # 转换为Hugging Face Dataset格式
            dataset = Dataset.from_pandas(df)
            
            # 应用分词函数
            tokenized_dataset = dataset.map(
                tokenize_function,
                batched=True,
                remove_columns=dataset.column_names
            )
            
            tokenized_datasets[split] = tokenized_dataset
            self.logger.info(f"Tokenized {split} set: {len(tokenized_dataset)} samples")
        
        return tokenized_datasets
    
    def compute_metrics(self, eval_pred):
        """
        计算评估指标
        
        Args:
            eval_pred: 预测结果
            
        Returns:
            评估指标字典
        """
        predictions, labels = eval_pred
        
        if self.task_name == 'STS-B':
            # 回归任务 - 计算皮尔逊相关系数
            # 反归一化预测值
            predictions = predictions.flatten() * 5.0
            labels = labels * 5.0
            
            correlation, _ = pearsonr(predictions, labels)
            return {"pearson": correlation}
        
        else:
            # 分类任务
            predictions = np.argmax(predictions, axis=1)
            
            metrics = {}
            
            # 准确率
            accuracy = accuracy_score(labels, predictions)
            metrics["accuracy"] = accuracy
            
            # 任务特定指标
            task_metric = self.task_config.get('metric', 'accuracy')
            
            if task_metric == 'f1':
                if self.task_config.get('num_labels', 2) == 2:
                    f1 = f1_score(labels, predictions, average='binary')
                else:
                    f1 = f1_score(labels, predictions, average='macro')
                metrics["f1"] = f1
            
            elif task_metric == 'matthews_correlation':
                mcc = matthews_corrcoef(labels, predictions)
                metrics["matthews_correlation"] = mcc
            
            return metrics
    
    def create_trainer(self, train_dataset: Dataset, eval_dataset: Dataset) -> Trainer:
        """
        创建训练器
        
        Args:
            train_dataset: 训练数据集
            eval_dataset: 验证数据集
            
        Returns:
            Trainer对象
        """
        if not self.model:
            raise ValueError("Model not loaded. Call load_model_and_tokenizer first.")
        
        # 创建输出目录
        output_dir = os.path.join(self.config.OUTPUT_DIR, self.task_name)
        os.makedirs(output_dir, exist_ok=True)
        
        # 训练参数
        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=self.config.NUM_EPOCHS,
            per_device_train_batch_size=self.config.BATCH_SIZE,
            per_device_eval_batch_size=self.config.BATCH_SIZE,
            warmup_steps=self.config.WARMUP_STEPS,
            learning_rate=self.config.LEARNING_RATE,
            weight_decay=0.01,
            logging_dir=os.path.join(self.config.LOGS_DIR, self.task_name),
            logging_steps=self.config.LOGGING_STEPS,
            evaluation_strategy="steps",
            eval_steps=self.config.EVAL_STEPS,
            save_strategy="steps",
            save_steps=self.config.SAVE_STEPS,
            load_best_model_at_end=True,
            metric_for_best_model=self.task_config.get('metric', 'accuracy'),
            greater_is_better=True,
            save_total_limit=2,
            seed=self.config.SEED,
            fp16=torch.cuda.is_available(),  # 如果有GPU则使用混合精度
            dataloader_pin_memory=False,
            remove_unused_columns=False,
        )
        
        # 数据整理器
        data_collator = DataCollatorWithPadding(
            tokenizer=self.tokenizer,
            padding=True
        )
        
        # 创建训练器
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            tokenizer=self.tokenizer,
            data_collator=data_collator,
            compute_metrics=self.compute_metrics,
            callbacks=[EarlyStoppingCallback(early_stopping_patience=3)]
        )
        
        return trainer
    
    def save_model(self, trainer: Trainer, save_path: str = None):
        """
        保存模型
        
        Args:
            trainer: 训练器对象
            save_path: 保存路径
        """
        if save_path is None:
            save_path = os.path.join(self.config.MODEL_SAVE_DIR, self.task_name)
        
        os.makedirs(save_path, exist_ok=True)
        
        # 保存最佳模型
        trainer.save_model(save_path)
        self.tokenizer.save_pretrained(save_path)
        
        # 保存配置信息
        config_info = {
            'task_name': self.task_name,
            'model_name': self.config.MODEL_NAME,
            'task_config': self.task_config,
            'training_config': {
                'batch_size': self.config.BATCH_SIZE,
                'learning_rate': self.config.LEARNING_RATE,
                'num_epochs': self.config.NUM_EPOCHS,
                'max_length': self.config.MAX_LENGTH
            }
        }
        
        with open(os.path.join(save_path, 'config.json'), 'w') as f:
            json.dump(config_info, f, indent=2)
        
        self.logger.info(f"Model saved to: {save_path}")
    
    def load_saved_model(self, model_path: str):
        """
        加载已保存的模型
        
        Args:
            model_path: 模型路径
        """
        self.logger.info(f"Loading saved model from: {model_path}")
        
        # 加载分词器
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        
        # 加载模型
        self.model = AutoModelForSequenceClassification.from_pretrained(model_path)
        self.model.to(self.device)
        
        self.logger.info("Saved model loaded successfully")
