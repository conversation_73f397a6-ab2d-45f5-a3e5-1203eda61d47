"""
模型管理模块 - 基于Spark的BERT模型管理
"""
import os
import logging
import torch
import torch.nn as nn
from typing import Dict, Any, Optional, Tuple, List
from transformers import (
    AutoTokenizer, AutoModel, AutoConfig,
    BertTokenizer, BertModel, BertConfig,
    BertForSequenceClassification,
    get_linear_schedule_with_warmup
)
from torch.optim import AdamW
import numpy as np
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, udf
from pyspark.sql.types import ArrayType, FloatType, StringType
import config

logger = logging.getLogger(__name__)

class GLUEModelManager:
    """GLUE任务模型管理器"""
    
    def __init__(self, model_name: str = None, device: str = None):
        self.model_name = model_name or config.MODEL_NAME
        self.device = device or config.DEVICE
        self.tokenizer = None
        self.model = None
        self.config_obj = None
        
        # 确保设备可用
        if self.device == "cuda" and not torch.cuda.is_available():
            logger.warning("CUDA不可用，切换到CPU")
            self.device = "cpu"
        
        logger.info(f"模型管理器初始化: {self.model_name}, 设备: {self.device}")
    
    def load_tokenizer(self) -> AutoTokenizer:
        """加载分词器"""
        try:
            logger.info(f"加载分词器: {self.model_name}")
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            
            # 确保有pad_token
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            logger.info(f"分词器加载成功，词汇表大小: {len(self.tokenizer)}")
            return self.tokenizer
            
        except Exception as e:
            logger.error(f"加载分词器失败: {e}")
            raise
    
    def load_model_for_task(self, task: str, num_labels: int = None) -> nn.Module:
        """为特定任务加载模型"""
        try:
            # 获取任务的标签数量
            if num_labels is None:
                num_labels = config.NUM_LABELS.get(task, 2)
            
            logger.info(f"为任务 {task} 加载模型，标签数量: {num_labels}")
            
            # 加载配置
            self.config_obj = AutoConfig.from_pretrained(self.model_name)
            self.config_obj.num_labels = num_labels
            
            # 根据任务类型选择模型
            if config.TASK_TYPES.get(task) == "regression":
                # 回归任务（如STS-B）
                self.config_obj.num_labels = 1
                self.model = BertForSequenceClassification.from_pretrained(
                    self.model_name, 
                    config=self.config_obj
                )
            else:
                # 分类任务
                self.model = BertForSequenceClassification.from_pretrained(
                    self.model_name,
                    config=self.config_obj
                )
            
            # 移动到指定设备
            self.model.to(self.device)
            
            # 打印模型信息
            total_params = sum(p.numel() for p in self.model.parameters())
            trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
            
            logger.info(f"模型加载成功:")
            logger.info(f"  - 总参数: {total_params:,}")
            logger.info(f"  - 可训练参数: {trainable_params:,}")
            logger.info(f"  - 模型大小: {total_params * 4 / 1024 / 1024:.1f} MB")
            
            return self.model
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
    
    def create_optimizer(self, learning_rate: float = None, weight_decay: float = None) -> AdamW:
        """创建优化器"""
        if self.model is None:
            raise ValueError("模型未加载，请先调用load_model_for_task")
        
        learning_rate = learning_rate or config.LEARNING_RATE
        weight_decay = weight_decay or config.WEIGHT_DECAY
        
        # 设置不同的权重衰减策略
        no_decay = ["bias", "LayerNorm.weight"]
        optimizer_grouped_parameters = [
            {
                "params": [p for n, p in self.model.named_parameters() 
                          if not any(nd in n for nd in no_decay)],
                "weight_decay": weight_decay,
            },
            {
                "params": [p for n, p in self.model.named_parameters() 
                          if any(nd in n for nd in no_decay)],
                "weight_decay": 0.0,
            },
        ]
        
        optimizer = AdamW(optimizer_grouped_parameters, lr=learning_rate)
        
        logger.info(f"优化器创建成功: lr={learning_rate}, weight_decay={weight_decay}")
        return optimizer
    
    def create_scheduler(self, optimizer: AdamW, num_training_steps: int, 
                        num_warmup_steps: int = None):
        """创建学习率调度器"""
        if num_warmup_steps is None:
            num_warmup_steps = int(0.1 * num_training_steps)  # 10%的步数用于warmup
        
        scheduler = get_linear_schedule_with_warmup(
            optimizer,
            num_warmup_steps=num_warmup_steps,
            num_training_steps=num_training_steps
        )
        
        logger.info(f"调度器创建成功: warmup_steps={num_warmup_steps}, total_steps={num_training_steps}")
        return scheduler
    
    def tokenize_texts(self, texts: List[str], max_length: int = None) -> Dict[str, torch.Tensor]:
        """批量分词"""
        if self.tokenizer is None:
            raise ValueError("分词器未加载，请先调用load_tokenizer")
        
        max_length = max_length or config.MAX_SEQ_LENGTH
        
        # 处理单句子或句子对
        if isinstance(texts[0], (list, tuple)):
            # 句子对
            text1 = [t[0] for t in texts]
            text2 = [t[1] for t in texts]
            
            encoded = self.tokenizer(
                text1, text2,
                padding=True,
                truncation=True,
                max_length=max_length,
                return_tensors="pt"
            )
        else:
            # 单句子
            encoded = self.tokenizer(
                texts,
                padding=True,
                truncation=True,
                max_length=max_length,
                return_tensors="pt"
            )
        
        return encoded
    
    def save_model(self, save_path: str, task: str = None):
        """保存模型"""
        if self.model is None:
            raise ValueError("模型未加载")
        
        os.makedirs(save_path, exist_ok=True)
        
        # 保存模型和分词器
        self.model.save_pretrained(save_path)
        if self.tokenizer:
            self.tokenizer.save_pretrained(save_path)
        
        # 保存配置信息
        model_info = {
            "model_name": self.model_name,
            "task": task,
            "device": self.device,
            "num_labels": self.config_obj.num_labels if self.config_obj else None
        }
        
        import json
        with open(os.path.join(save_path, "model_info.json"), "w", encoding="utf-8") as f:
            json.dump(model_info, f, indent=2, ensure_ascii=False)
        
        logger.info(f"模型已保存到: {save_path}")
    
    def load_model(self, load_path: str) -> nn.Module:
        """加载已保存的模型"""
        try:
            # 加载模型信息
            info_path = os.path.join(load_path, "model_info.json")
            if os.path.exists(info_path):
                import json
                with open(info_path, "r", encoding="utf-8") as f:
                    model_info = json.load(f)
                logger.info(f"加载模型信息: {model_info}")
            
            # 加载分词器
            self.tokenizer = AutoTokenizer.from_pretrained(load_path)
            
            # 加载模型
            self.model = BertForSequenceClassification.from_pretrained(load_path)
            self.model.to(self.device)
            
            logger.info(f"模型从 {load_path} 加载成功")
            return self.model
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        if self.model is None:
            return {"status": "模型未加载"}
        
        info = {
            "model_name": self.model_name,
            "device": self.device,
            "total_parameters": sum(p.numel() for p in self.model.parameters()),
            "trainable_parameters": sum(p.numel() for p in self.model.parameters() if p.requires_grad),
            "model_size_mb": sum(p.numel() for p in self.model.parameters()) * 4 / 1024 / 1024,
            "config": self.config_obj.to_dict() if self.config_obj else None
        }
        
        return info

class SparkModelManager:
    """基于Spark的分布式模型管理器"""

    def __init__(self, spark: SparkSession, model_manager: GLUEModelManager):
        self.spark = spark
        self.model_manager = model_manager
        self.broadcast_model = None
        self.broadcast_tokenizer = None

    def broadcast_model_components(self):
        """广播模型组件到所有节点"""
        if self.model_manager.model is None or self.model_manager.tokenizer is None:
            raise ValueError("模型或分词器未加载")

        # 将模型移到CPU以便广播
        model_state = self.model_manager.model.cpu().state_dict()
        tokenizer = self.model_manager.tokenizer

        # 广播模型状态和分词器
        self.broadcast_model = self.spark.sparkContext.broadcast(model_state)
        self.broadcast_tokenizer = self.spark.sparkContext.broadcast(tokenizer)

        # 将模型移回原设备
        self.model_manager.model.to(self.model_manager.device)

        logger.info("模型组件已广播到所有Spark节点")

    def create_prediction_udf(self, task: str, max_length: int = None):
        """创建用于Spark DataFrame的预测UDF"""
        max_length = max_length or config.MAX_SEQ_LENGTH
        model_name = self.model_manager.model_name
        num_labels = config.NUM_LABELS.get(task, 2)
        device = "cpu"  # UDF中使用CPU

        def predict_batch(texts):
            """批量预测函数"""
            import torch
            from transformers import AutoTokenizer, BertForSequenceClassification

            # 在每个executor中重新加载模型
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = BertForSequenceClassification.from_pretrained(model_name)
            model.eval()

            results = []
            with torch.no_grad():
                for text in texts:
                    if isinstance(text, (list, tuple)) and len(text) == 2:
                        # 句子对
                        inputs = tokenizer(
                            text[0], text[1],
                            padding=True,
                            truncation=True,
                            max_length=max_length,
                            return_tensors="pt"
                        )
                    else:
                        # 单句子
                        inputs = tokenizer(
                            text,
                            padding=True,
                            truncation=True,
                            max_length=max_length,
                            return_tensors="pt"
                        )

                    outputs = model(**inputs)

                    if config.TASK_TYPES.get(task) == "regression":
                        # 回归任务
                        prediction = outputs.logits.squeeze().item()
                    else:
                        # 分类任务
                        prediction = torch.argmax(outputs.logits, dim=-1).item()

                    results.append(float(prediction))

            return results

        # 注册UDF
        prediction_udf = udf(predict_batch, ArrayType(FloatType()))
        return prediction_udf

    def batch_predict(self, df, text_columns: List[str], task: str, batch_size: int = 1000):
        """批量预测DataFrame中的文本"""
        from pyspark.sql.functions import collect_list, explode, monotonically_increasing_id

        # 添加行ID以保持顺序
        df_with_id = df.withColumn("row_id", monotonically_increasing_id())

        # 创建预测UDF
        predict_udf = self.create_prediction_udf(task)

        # 根据文本列数量处理
        if len(text_columns) == 1:
            # 单句子任务
            text_col = text_columns[0]
            result_df = df_with_id.withColumn("prediction", predict_udf(col(text_col)))
        elif len(text_columns) == 2:
            # 句子对任务
            from pyspark.sql.functions import array
            text_array = array(col(text_columns[0]), col(text_columns[1]))
            result_df = df_with_id.withColumn("prediction", predict_udf(text_array))
        else:
            raise ValueError(f"不支持的文本列数量: {len(text_columns)}")

        return result_df.drop("row_id")

    def cleanup_broadcast(self):
        """清理广播变量"""
        if self.broadcast_model:
            self.broadcast_model.unpersist()
        if self.broadcast_tokenizer:
            self.broadcast_tokenizer.unpersist()

        logger.info("广播变量已清理")

class ModelEvaluator:
    """模型评估器"""

    def __init__(self, model_manager: GLUEModelManager):
        self.model_manager = model_manager

    def evaluate_on_batch(self, texts: List[str], labels: List[int], task: str) -> Dict[str, float]:
        """在批次数据上评估模型"""
        if self.model_manager.model is None:
            raise ValueError("模型未加载")

        self.model_manager.model.eval()

        # 分词
        inputs = self.model_manager.tokenize_texts(texts)
        inputs = {k: v.to(self.model_manager.device) for k, v in inputs.items()}

        # 预测
        with torch.no_grad():
            outputs = self.model_manager.model(**inputs)
            logits = outputs.logits

        # 计算指标
        if config.TASK_TYPES.get(task) == "regression":
            # 回归任务
            predictions = logits.squeeze().cpu().numpy()
            labels = np.array(labels)

            from scipy.stats import pearsonr, spearmanr
            pearson_corr = pearsonr(predictions, labels)[0]
            spearman_corr = spearmanr(predictions, labels)[0]

            return {
                "pearson_correlation": pearson_corr,
                "spearman_correlation": spearman_corr,
                "mse": np.mean((predictions - labels) ** 2)
            }
        else:
            # 分类任务
            predictions = torch.argmax(logits, dim=-1).cpu().numpy()
            labels = np.array(labels)

            accuracy = np.mean(predictions == labels)

            # 计算F1分数
            from sklearn.metrics import f1_score, matthews_corrcoef

            if task == "CoLA":
                mcc = matthews_corrcoef(labels, predictions)
                return {"accuracy": accuracy, "matthews_corrcoef": mcc}
            elif len(np.unique(labels)) > 2:
                f1 = f1_score(labels, predictions, average="macro")
                return {"accuracy": accuracy, "f1_macro": f1}
            else:
                f1 = f1_score(labels, predictions)
                return {"accuracy": accuracy, "f1": f1}

    def compute_loss(self, texts: List[str], labels: List[int], task: str) -> float:
        """计算损失"""
        if self.model_manager.model is None:
            raise ValueError("模型未加载")

        self.model_manager.model.eval()

        # 分词
        inputs = self.model_manager.tokenize_texts(texts)
        inputs = {k: v.to(self.model_manager.device) for k, v in inputs.items()}

        # 添加标签
        labels_tensor = torch.tensor(labels, dtype=torch.long if config.TASK_TYPES.get(task) != "regression" else torch.float)
        labels_tensor = labels_tensor.to(self.model_manager.device)

        # 计算损失
        with torch.no_grad():
            outputs = self.model_manager.model(**inputs, labels=labels_tensor)
            loss = outputs.loss

        return loss.item()
