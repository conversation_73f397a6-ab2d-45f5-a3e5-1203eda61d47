2025-07-27 19:02:08,474 - utils - INFO - 日志已配置，日志文件: E:\spark文本分类\logs\glue_training_20250727_190208.log
2025-07-27 19:02:08,474 - utils - INFO - ================================================================================
2025-07-27 19:02:08,475 - utils - INFO - GLUE基准测试系统
2025-07-27 19:02:08,475 - utils - INFO - 基于Apache Spark和BERT的文本分类
2025-07-27 19:02:08,475 - utils - INFO - ================================================================================
2025-07-27 19:02:08,475 - utils - INFO - 任务: all
2025-07-27 19:02:08,475 - utils - INFO - 模式: train
2025-07-27 19:02:08,515 - utils - INFO - 设备: cuda
2025-07-27 19:02:08,515 - utils - INFO - Spark主节点: local[*]
2025-07-27 19:02:12,139 - __main__ - INFO - Spark会话创建成功，版本: 3.5.0
2025-07-27 19:02:12,141 - __main__ - INFO - GLUE基准测试系统初始化完成
2025-07-27 19:02:12,141 - utils - INFO - 
🚀 开始训练所有GLUE任务...
2025-07-27 19:02:12,142 - __main__ - INFO - 开始训练所有GLUE任务
2025-07-27 19:02:12,142 - __main__ - INFO - 
============================================================
2025-07-27 19:02:12,142 - __main__ - INFO - 训练任务: CoLA
2025-07-27 19:02:12,142 - __main__ - INFO - ============================================================
2025-07-27 19:02:12,142 - __main__ - INFO - 开始训练任务: CoLA
2025-07-27 19:02:12,142 - __main__ - INFO - 加载训练和验证数据...
2025-07-27 19:02:12,142 - data_processor - INFO - 加载 CoLA 任务的 train 数据: E:\spark文本分类\glue\CoLA\train.tsv
2025-07-27 19:02:15,612 - data_processor - INFO - 加载 CoLA 任务的 dev 数据: E:\spark文本分类\glue\CoLA\dev.tsv
2025-07-27 19:02:16,403 - __main__ - INFO - 数据加载完成: 训练集=1000, 验证集=200
2025-07-27 19:02:16,403 - __main__ - INFO - 初始化模型...
2025-07-27 19:02:16,403 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:02:16,403 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:02:18,580 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:02:18,580 - model_manager - INFO - 为任务 CoLA 加载模型，标签数量: 2
2025-07-27 19:02:19,258 - model_manager - INFO - 模型加载成功:
2025-07-27 19:02:19,258 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:02:19,258 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:02:19,258 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:02:19,259 - trainer - INFO - 训练器初始化完成: 任务=CoLA, 输出目录=E:\spark文本分类\models\CoLA
2025-07-27 19:02:19,259 - trainer - INFO - 准备训练数据...
2025-07-27 19:02:19,453 - trainer - INFO - 数据准备完成: 训练集=1000, 验证集=200
2025-07-27 19:02:19,453 - __main__ - INFO - 开始训练，参数: {'epochs': 1, 'learning_rate': 2e-05}
2025-07-27 19:02:19,453 - trainer - INFO - 开始训练: 轮数=1, 学习率=2e-05
2025-07-27 19:02:19,455 - model_manager - INFO - 优化器创建成功: lr=2e-05, weight_decay=0.01
2025-07-27 19:02:19,455 - model_manager - INFO - 调度器创建成功: warmup_steps=3, total_steps=32
2025-07-27 19:02:19,455 - trainer - INFO - 开始第 1/1 轮训练
2025-07-27 19:02:32,508 - trainer - INFO - Epoch 1/1:
2025-07-27 19:02:32,508 - trainer - INFO -   训练损失: 0.6486
2025-07-27 19:02:32,509 - trainer - INFO -   accuracy: 0.6500
2025-07-27 19:02:32,509 - trainer - INFO -   matthews_corrcoef: 0.0000
2025-07-27 19:02:32,509 - trainer - INFO -   eval_loss: 0.6312
2025-07-27 19:02:32,509 - trainer - INFO - 训练完成! 总时间: 13s
2025-07-27 19:02:32,511 - trainer - INFO - 训练历史已保存: E:\spark文本分类\models\CoLA\training_history.json
2025-07-27 19:02:32,511 - __main__ - INFO - 任务 CoLA 训练完成!
2025-07-27 19:02:32,512 - __main__ - INFO - 最佳指标: 0.0000
2025-07-27 19:02:32,512 - __main__ - INFO - 训练时间: 13.05秒
2025-07-27 19:02:32,514 - __main__ - INFO - ✅ CoLA 训练成功
2025-07-27 19:02:32,514 - __main__ - INFO - 
============================================================
2025-07-27 19:02:32,514 - __main__ - INFO - 训练任务: SST-2
2025-07-27 19:02:32,514 - __main__ - INFO - ============================================================
2025-07-27 19:02:32,515 - __main__ - INFO - 开始训练任务: SST-2
2025-07-27 19:02:32,515 - __main__ - INFO - 加载训练和验证数据...
2025-07-27 19:02:32,515 - data_processor - INFO - 加载 SST-2 任务的 train 数据: E:\spark文本分类\glue\SST-2\train.tsv
2025-07-27 19:02:32,638 - data_processor - INFO - 加载 SST-2 任务的 dev 数据: E:\spark文本分类\glue\SST-2\dev.tsv
2025-07-27 19:02:32,977 - __main__ - INFO - 数据加载完成: 训练集=1000, 验证集=200
2025-07-27 19:02:32,978 - __main__ - INFO - 初始化模型...
2025-07-27 19:02:32,978 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:02:32,978 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:02:33,979 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:02:33,980 - model_manager - INFO - 为任务 SST-2 加载模型，标签数量: 2
2025-07-27 19:02:34,576 - model_manager - INFO - 模型加载成功:
2025-07-27 19:02:34,577 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:02:34,577 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:02:34,577 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:02:34,577 - trainer - INFO - 训练器初始化完成: 任务=SST-2, 输出目录=E:\spark文本分类\models\SST-2
2025-07-27 19:02:34,577 - trainer - INFO - 准备训练数据...
2025-07-27 19:02:34,721 - trainer - INFO - 数据准备完成: 训练集=1000, 验证集=200
2025-07-27 19:02:34,722 - __main__ - INFO - 开始训练，参数: {'epochs': 1, 'learning_rate': 2e-05}
2025-07-27 19:02:34,722 - trainer - INFO - 开始训练: 轮数=1, 学习率=2e-05
2025-07-27 19:02:34,723 - model_manager - INFO - 优化器创建成功: lr=2e-05, weight_decay=0.01
2025-07-27 19:02:34,723 - model_manager - INFO - 调度器创建成功: warmup_steps=3, total_steps=32
2025-07-27 19:02:34,723 - trainer - INFO - 开始第 1/1 轮训练
2025-07-27 19:02:47,839 - trainer - INFO - Epoch 1/1:
2025-07-27 19:02:47,840 - trainer - INFO -   训练损失: 0.6780
2025-07-27 19:02:47,840 - trainer - INFO -   accuracy: 0.5550
2025-07-27 19:02:47,840 - trainer - INFO -   f1: 0.6899
2025-07-27 19:02:47,840 - trainer - INFO -   eval_loss: 0.6405
2025-07-27 19:02:49,301 - model_manager - INFO - 模型已保存到: E:\spark文本分类\models\SST-2\best_model_hf
2025-07-27 19:02:49,301 - trainer - INFO - 保存最佳模型: accuracy=0.5550
2025-07-27 19:02:49,301 - trainer - INFO - 训练完成! 总时间: 14s
2025-07-27 19:02:49,303 - trainer - INFO - 训练历史已保存: E:\spark文本分类\models\SST-2\training_history.json
2025-07-27 19:02:49,304 - __main__ - INFO - 任务 SST-2 训练完成!
2025-07-27 19:02:49,304 - __main__ - INFO - 最佳指标: 0.5550
2025-07-27 19:02:49,304 - __main__ - INFO - 训练时间: 14.58秒
2025-07-27 19:02:49,306 - __main__ - INFO - ✅ SST-2 训练成功
2025-07-27 19:02:49,307 - __main__ - INFO - 
============================================================
2025-07-27 19:02:49,307 - __main__ - INFO - 训练任务: MRPC
2025-07-27 19:02:49,307 - __main__ - INFO - ============================================================
2025-07-27 19:02:49,307 - __main__ - INFO - 开始训练任务: MRPC
2025-07-27 19:02:49,308 - __main__ - INFO - 加载训练和验证数据...
2025-07-27 19:02:49,308 - data_processor - INFO - 加载 MRPC 任务的 train 数据: E:\spark文本分类\glue\MRPC\msr_paraphrase_train.txt
2025-07-27 19:02:49,409 - data_processor - INFO - 加载 MRPC 任务的 dev 数据: E:\spark文本分类\glue\MRPC\msr_paraphrase_test.txt
2025-07-27 19:02:49,690 - __main__ - INFO - 数据加载完成: 训练集=1000, 验证集=200
2025-07-27 19:02:49,690 - __main__ - INFO - 初始化模型...
2025-07-27 19:02:49,690 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:02:49,690 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:02:50,618 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:02:50,619 - model_manager - INFO - 为任务 MRPC 加载模型，标签数量: 2
2025-07-27 19:02:51,210 - model_manager - INFO - 模型加载成功:
2025-07-27 19:02:51,210 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:02:51,210 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:02:51,211 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:02:51,211 - trainer - INFO - 训练器初始化完成: 任务=MRPC, 输出目录=E:\spark文本分类\models\MRPC
2025-07-27 19:02:51,211 - trainer - INFO - 准备训练数据...
2025-07-27 19:02:51,343 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,343 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,343 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,343 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,344 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,344 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,344 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,345 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,345 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,345 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,345 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,345 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,345 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,345 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,346 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,346 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,346 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,346 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,346 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,346 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,347 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,347 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,347 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,347 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,347 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,347 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,347 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,347 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,347 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,348 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,348 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,348 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,348 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,348 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,348 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,348 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,349 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,349 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,349 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,349 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,349 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,349 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,349 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,349 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,349 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,349 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,350 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,350 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,350 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,350 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,350 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,350 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,351 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,351 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,351 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,351 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,351 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,351 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,351 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,351 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,351 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,351 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,352 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,352 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,352 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,352 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,352 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,352 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,352 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,352 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,352 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,352 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,352 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,352 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,352 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,352 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,352 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,354 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,354 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,354 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,354 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,354 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,354 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,355 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,355 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,355 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,355 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,355 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,355 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,355 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,355 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,355 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,355 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,356 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,356 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,356 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,356 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,356 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,356 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,356 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,356 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,356 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,356 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,356 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,357 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,357 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,357 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,357 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,357 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,357 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,358 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,358 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,358 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,358 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,358 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,358 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,359 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,359 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,359 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,359 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,359 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,359 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,359 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,359 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,359 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,361 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,361 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,361 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,361 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,361 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,361 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,361 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,361 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,361 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,361 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,361 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,362 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,362 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,362 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,362 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,362 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,362 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,362 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,362 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,362 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,363 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,363 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,363 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,363 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,363 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,363 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,363 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,363 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,363 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,363 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,363 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,364 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,364 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,364 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,365 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,365 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,365 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,365 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,365 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,365 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,365 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,365 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,366 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,366 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,366 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,366 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,366 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,366 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,366 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,366 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,366 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,367 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,367 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,367 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,367 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,367 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,367 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,367 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,368 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,368 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,368 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,368 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,368 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,368 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,368 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,369 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,369 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,369 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,369 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,369 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,369 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,369 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,370 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,370 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,370 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,370 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,370 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,370 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,370 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,370 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,372 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,372 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,372 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,372 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,372 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,372 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,373 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,374 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,374 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,374 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,375 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,375 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,375 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,375 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,375 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,375 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,376 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,376 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,376 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,376 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,376 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,376 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,376 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,376 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,377 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,377 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,377 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,377 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,377 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,377 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,377 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,377 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,378 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,378 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,378 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,378 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,378 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,378 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,378 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,379 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,379 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,379 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,380 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,380 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,380 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,380 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,381 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,381 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,381 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,381 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,382 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,382 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,382 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,382 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,382 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,382 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,382 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,382 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,382 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,383 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,383 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,383 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,383 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,383 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,383 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,383 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,383 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,383 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,383 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,383 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,383 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,384 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,384 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,384 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,384 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,384 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,384 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,384 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,384 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,384 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,386 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,386 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,386 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,386 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,386 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,386 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,386 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,386 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,386 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,388 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,388 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,388 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,388 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,389 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,389 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,389 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,389 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,389 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,389 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,390 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,390 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,390 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,390 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,390 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,390 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,390 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,390 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,391 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,391 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,391 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,391 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,391 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,391 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,391 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,391 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,391 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,392 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,392 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,392 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,392 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,392 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,392 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,393 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,393 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,393 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,393 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,393 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,393 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,393 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,395 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,395 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,395 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,395 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,395 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,395 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,395 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,395 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,396 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,396 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,396 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,396 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,396 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,396 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,397 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,397 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,397 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,397 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,397 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,398 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,398 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,398 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,398 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,399 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,399 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,399 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,400 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,400 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,400 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,400 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,400 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,401 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,401 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,401 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,401 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,401 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,401 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,402 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,403 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,403 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,403 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,403 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,403 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,403 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,403 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,403 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,403 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,403 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,405 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,405 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,405 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,405 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,405 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,405 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,406 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,406 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,406 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,406 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,406 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,406 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,407 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,407 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,407 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,407 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,407 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,407 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,408 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,408 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,408 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,408 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,408 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,408 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,408 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,409 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,409 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,409 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,409 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,409 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,409 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,410 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,410 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,410 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,410 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,411 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,411 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,411 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,411 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,411 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,412 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,412 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,412 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,412 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,412 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,413 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,415 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,415 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,415 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,415 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,415 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,415 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,415 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,415 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,415 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,416 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,416 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,416 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,416 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,416 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,416 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,416 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,416 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,417 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,417 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,417 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,417 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,417 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,417 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,417 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,417 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,417 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,417 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,417 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,418 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,419 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,419 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,419 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,419 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,419 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,419 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,419 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,419 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,420 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,420 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,420 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,420 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,420 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,420 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,420 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,420 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,420 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,422 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,422 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,422 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,422 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,422 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,422 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,422 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,422 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,422 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,422 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,423 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,423 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,423 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,423 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,423 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,423 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,423 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,423 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,423 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,423 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,423 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,423 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,425 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,425 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,425 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,425 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,425 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,426 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,426 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,426 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,426 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,427 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,427 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,427 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,427 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,427 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,428 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,428 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,428 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,428 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,428 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,428 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,428 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,429 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,429 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,429 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,429 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,429 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,429 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,429 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,429 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,429 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,429 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,429 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,430 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,430 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,430 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,430 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,430 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,430 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,430 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,430 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,430 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,430 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,430 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,431 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,431 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,431 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,431 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,431 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,431 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,431 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,431 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,431 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,431 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,431 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,432 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,432 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,432 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,432 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,432 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,432 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,432 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,432 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,432 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,432 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,433 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,433 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,433 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,433 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,433 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,433 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,433 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,433 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,433 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,435 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,435 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,435 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,435 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,436 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,436 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,436 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,436 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,436 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,436 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,437 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,438 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,438 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,438 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,438 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,438 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,438 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,438 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,438 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,438 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,438 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,438 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,438 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,438 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,438 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,439 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,440 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,442 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,443 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,444 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,444 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,444 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,444 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,444 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,444 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,444 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,444 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,444 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,444 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,444 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,444 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,444 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,445 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,446 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,446 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,446 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,446 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,446 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,446 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,446 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,446 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,446 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,446 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,446 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,446 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,446 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,446 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,447 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,447 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,447 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,448 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,448 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,448 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,448 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,448 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,448 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,448 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,448 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,448 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,448 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,448 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,448 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,449 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,450 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,451 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,452 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,452 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,452 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,452 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,452 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,452 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,452 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,452 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,452 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,452 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,453 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,453 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,453 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,454 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,454 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,454 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,454 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,454 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,454 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,454 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,454 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,455 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,456 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,456 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,456 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,456 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,456 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,456 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,456 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,456 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,456 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,456 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,457 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,457 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,457 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,457 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,457 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,457 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,457 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,457 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,457 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,457 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,457 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,457 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,457 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,458 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,459 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,459 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,459 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,459 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,459 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,459 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,459 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,460 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,460 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,460 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,460 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,460 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,460 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,460 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,460 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,460 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,460 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,460 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,460 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,461 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,462 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,463 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,465 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,465 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,465 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,465 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,465 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,465 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,465 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,465 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,466 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,466 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,466 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,466 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,466 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,466 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,466 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,466 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,466 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,466 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,466 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,466 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,467 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,467 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,467 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,467 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,467 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,467 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,467 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,467 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,467 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,467 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,467 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,467 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,468 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,469 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,470 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,509 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,510 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,510 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,510 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,510 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,510 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,510 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,510 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,510 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,511 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,512 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,513 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,515 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,516 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,516 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,516 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,516 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,516 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,516 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,516 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,517 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,518 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,519 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,519 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,519 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,519 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,519 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,519 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,519 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,519 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,519 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,520 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,520 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,520 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,520 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,520 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,520 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,520 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,520 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,520 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,520 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,520 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,520 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,520 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,520 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,521 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,522 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,523 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,525 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,525 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,525 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,525 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,525 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,525 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,525 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,525 - trainer - WARNING - 无法找到句子对列: dict_keys(['Quality\t#1 ID\t#2 ID\t#1 String\t#2 String'])
2025-07-27 19:02:51,525 - __main__ - ERROR - 训练任务 MRPC 失败: num_samples should be a positive integer value, but got num_samples=0
2025-07-27 19:02:51,525 - __main__ - ERROR - ❌ MRPC 训练失败: num_samples should be a positive integer value, but got num_samples=0
2025-07-27 19:02:51,528 - __main__ - INFO - 
============================================================
2025-07-27 19:02:51,528 - __main__ - INFO - 训练任务: STS-B
2025-07-27 19:02:51,528 - __main__ - INFO - ============================================================
2025-07-27 19:02:51,528 - __main__ - INFO - 开始训练任务: STS-B
2025-07-27 19:02:51,528 - __main__ - INFO - 加载训练和验证数据...
2025-07-27 19:02:51,529 - data_processor - INFO - 加载 STS-B 任务的 train 数据: E:\spark文本分类\glue\STS-B\train.tsv
2025-07-27 19:02:51,649 - data_processor - INFO - 加载 STS-B 任务的 dev 数据: E:\spark文本分类\glue\STS-B\dev.tsv
2025-07-27 19:02:51,921 - __main__ - INFO - 数据加载完成: 训练集=1000, 验证集=200
2025-07-27 19:02:51,922 - __main__ - INFO - 初始化模型...
2025-07-27 19:02:51,922 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:02:51,922 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:02:53,150 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:02:53,151 - model_manager - INFO - 为任务 STS-B 加载模型，标签数量: 1
2025-07-27 19:02:53,812 - model_manager - INFO - 模型加载成功:
2025-07-27 19:02:53,812 - model_manager - INFO -   - 总参数: 109,483,009
2025-07-27 19:02:53,812 - model_manager - INFO -   - 可训练参数: 109,483,009
2025-07-27 19:02:53,813 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:02:53,813 - trainer - INFO - 训练器初始化完成: 任务=STS-B, 输出目录=E:\spark文本分类\models\STS-B
2025-07-27 19:02:53,813 - trainer - INFO - 准备训练数据...
2025-07-27 19:02:53,999 - trainer - INFO - 数据准备完成: 训练集=1000, 验证集=200
2025-07-27 19:02:53,999 - __main__ - INFO - 开始训练，参数: {'epochs': 1, 'learning_rate': 2e-05}
2025-07-27 19:02:53,999 - trainer - INFO - 开始训练: 轮数=1, 学习率=2e-05
2025-07-27 19:02:54,001 - model_manager - INFO - 优化器创建成功: lr=2e-05, weight_decay=0.01
2025-07-27 19:02:54,001 - model_manager - INFO - 调度器创建成功: warmup_steps=3, total_steps=32
2025-07-27 19:02:54,001 - trainer - INFO - 开始第 1/1 轮训练
2025-07-27 19:02:54,308 - __main__ - ERROR - 训练任务 STS-B 失败: Found dtype Long but expected Float
2025-07-27 19:02:54,308 - __main__ - ERROR - ❌ STS-B 训练失败: Found dtype Long but expected Float
2025-07-27 19:02:54,312 - __main__ - INFO - 
============================================================
2025-07-27 19:02:54,312 - __main__ - INFO - 训练任务: QQP
2025-07-27 19:02:54,312 - __main__ - INFO - ============================================================
2025-07-27 19:02:54,312 - __main__ - INFO - 开始训练任务: QQP
2025-07-27 19:02:54,312 - __main__ - INFO - 加载训练和验证数据...
2025-07-27 19:02:54,313 - data_processor - INFO - 加载 QQP 任务的 train 数据: E:\spark文本分类\glue\QQP\train.tsv
2025-07-27 19:02:54,432 - data_processor - INFO - 加载 QQP 任务的 dev 数据: E:\spark文本分类\glue\QQP\dev.tsv
2025-07-27 19:02:54,848 - __main__ - INFO - 数据加载完成: 训练集=1000, 验证集=200
2025-07-27 19:02:54,849 - __main__ - INFO - 初始化模型...
2025-07-27 19:02:54,849 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:02:54,849 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:02:55,810 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:02:55,811 - model_manager - INFO - 为任务 QQP 加载模型，标签数量: 2
2025-07-27 19:02:56,373 - model_manager - INFO - 模型加载成功:
2025-07-27 19:02:56,373 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:02:56,373 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:02:56,373 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:02:56,375 - trainer - INFO - 训练器初始化完成: 任务=QQP, 输出目录=E:\spark文本分类\models\QQP
2025-07-27 19:02:56,375 - trainer - INFO - 准备训练数据...
2025-07-27 19:02:56,647 - trainer - INFO - 数据准备完成: 训练集=1000, 验证集=200
2025-07-27 19:02:56,647 - __main__ - INFO - 开始训练，参数: {'epochs': 1, 'learning_rate': 2e-05}
2025-07-27 19:02:56,647 - trainer - INFO - 开始训练: 轮数=1, 学习率=2e-05
2025-07-27 19:02:56,649 - model_manager - INFO - 优化器创建成功: lr=2e-05, weight_decay=0.01
2025-07-27 19:02:56,649 - model_manager - INFO - 调度器创建成功: warmup_steps=3, total_steps=32
2025-07-27 19:02:56,649 - trainer - INFO - 开始第 1/1 轮训练
2025-07-27 19:03:10,589 - trainer - INFO - Epoch 1/1:
2025-07-27 19:03:10,589 - trainer - INFO -   训练损失: 0.2074
2025-07-27 19:03:10,589 - trainer - INFO -   accuracy: 1.0000
2025-07-27 19:03:10,589 - trainer - INFO -   f1: 0.0000
2025-07-27 19:03:10,589 - trainer - INFO -   eval_loss: 0.0572
2025-07-27 19:03:10,589 - trainer - INFO - 训练完成! 总时间: 13s
2025-07-27 19:03:10,590 - trainer - INFO - 训练历史已保存: E:\spark文本分类\models\QQP\training_history.json
2025-07-27 19:03:10,591 - __main__ - INFO - 任务 QQP 训练完成!
2025-07-27 19:03:10,591 - __main__ - INFO - 最佳指标: 0.0000
2025-07-27 19:03:10,591 - __main__ - INFO - 训练时间: 13.94秒
2025-07-27 19:03:10,595 - __main__ - INFO - ✅ QQP 训练成功
2025-07-27 19:03:10,595 - __main__ - INFO - 
============================================================
2025-07-27 19:03:10,595 - __main__ - INFO - 训练任务: MNLI
2025-07-27 19:03:10,595 - __main__ - INFO - ============================================================
2025-07-27 19:03:10,595 - __main__ - INFO - 开始训练任务: MNLI
2025-07-27 19:03:10,595 - __main__ - INFO - 加载训练和验证数据...
2025-07-27 19:03:10,596 - data_processor - INFO - 加载 MNLI 任务的 train 数据: E:\spark文本分类\glue\MNLI\train.tsv
2025-07-27 19:03:10,733 - data_processor - INFO - 加载 MNLI 任务的 dev 数据: E:\spark文本分类\glue\MNLI\dev_matched.tsv
2025-07-27 19:03:11,211 - __main__ - INFO - 数据加载完成: 训练集=1000, 验证集=200
2025-07-27 19:03:11,211 - __main__ - INFO - 初始化模型...
2025-07-27 19:03:11,211 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:03:11,212 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:03:12,778 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:03:12,778 - model_manager - INFO - 为任务 MNLI 加载模型，标签数量: 3
2025-07-27 19:03:13,341 - model_manager - INFO - 模型加载成功:
2025-07-27 19:03:13,341 - model_manager - INFO -   - 总参数: 109,484,547
2025-07-27 19:03:13,341 - model_manager - INFO -   - 可训练参数: 109,484,547
2025-07-27 19:03:13,342 - model_manager - INFO -   - 模型大小: 417.7 MB
2025-07-27 19:03:13,342 - trainer - INFO - 训练器初始化完成: 任务=MNLI, 输出目录=E:\spark文本分类\models\MNLI
2025-07-27 19:03:13,342 - trainer - INFO - 准备训练数据...
2025-07-27 19:03:13,526 - trainer - INFO - 数据准备完成: 训练集=1000, 验证集=200
2025-07-27 19:03:13,526 - __main__ - INFO - 开始训练，参数: {'epochs': 1, 'learning_rate': 2e-05}
2025-07-27 19:03:13,526 - trainer - INFO - 开始训练: 轮数=1, 学习率=2e-05
2025-07-27 19:03:13,527 - model_manager - INFO - 优化器创建成功: lr=2e-05, weight_decay=0.01
2025-07-27 19:03:13,527 - model_manager - INFO - 调度器创建成功: warmup_steps=3, total_steps=32
2025-07-27 19:03:13,528 - trainer - INFO - 开始第 1/1 轮训练
2025-07-27 19:03:27,626 - trainer - INFO - Epoch 1/1:
2025-07-27 19:03:27,626 - trainer - INFO -   训练损失: 1.1587
2025-07-27 19:03:27,626 - trainer - INFO -   accuracy: 0.3600
2025-07-27 19:03:27,626 - trainer - INFO -   f1_macro: 0.2740
2025-07-27 19:03:27,626 - trainer - INFO -   eval_loss: 1.0951
2025-07-27 19:03:29,155 - model_manager - INFO - 模型已保存到: E:\spark文本分类\models\MNLI\best_model_hf
2025-07-27 19:03:29,155 - trainer - INFO - 保存最佳模型: accuracy=0.3600
2025-07-27 19:03:29,155 - trainer - INFO - 训练完成! 总时间: 15s
2025-07-27 19:03:29,160 - trainer - INFO - 训练历史已保存: E:\spark文本分类\models\MNLI\training_history.json
2025-07-27 19:03:29,160 - __main__ - INFO - 任务 MNLI 训练完成!
2025-07-27 19:03:29,161 - __main__ - INFO - 最佳指标: 0.3600
2025-07-27 19:03:29,161 - __main__ - INFO - 训练时间: 15.63秒
2025-07-27 19:03:29,164 - __main__ - INFO - ✅ MNLI 训练成功
2025-07-27 19:03:29,164 - __main__ - INFO - 
============================================================
2025-07-27 19:03:29,164 - __main__ - INFO - 训练任务: QNLI
2025-07-27 19:03:29,165 - __main__ - INFO - ============================================================
2025-07-27 19:03:29,165 - __main__ - INFO - 开始训练任务: QNLI
2025-07-27 19:03:29,165 - __main__ - INFO - 加载训练和验证数据...
2025-07-27 19:03:29,165 - data_processor - INFO - 加载 QNLI 任务的 train 数据: E:\spark文本分类\glue\QNLI\train.tsv
2025-07-27 19:03:29,290 - data_processor - INFO - 加载 QNLI 任务的 dev 数据: E:\spark文本分类\glue\QNLI\dev.tsv
2025-07-27 19:03:29,588 - __main__ - INFO - 数据加载完成: 训练集=1000, 验证集=200
2025-07-27 19:03:29,588 - __main__ - INFO - 初始化模型...
2025-07-27 19:03:29,588 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:03:29,588 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:03:30,569 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:03:30,569 - model_manager - INFO - 为任务 QNLI 加载模型，标签数量: 2
2025-07-27 19:03:31,174 - model_manager - INFO - 模型加载成功:
2025-07-27 19:03:31,175 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:03:31,175 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:03:31,175 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:03:31,177 - trainer - INFO - 训练器初始化完成: 任务=QNLI, 输出目录=E:\spark文本分类\models\QNLI
2025-07-27 19:03:31,177 - trainer - INFO - 准备训练数据...
2025-07-27 19:03:31,318 - trainer - INFO - 数据准备完成: 训练集=1000, 验证集=200
2025-07-27 19:03:31,319 - __main__ - INFO - 开始训练，参数: {'epochs': 1, 'learning_rate': 2e-05}
2025-07-27 19:03:31,319 - trainer - INFO - 开始训练: 轮数=1, 学习率=2e-05
2025-07-27 19:03:31,320 - model_manager - INFO - 优化器创建成功: lr=2e-05, weight_decay=0.01
2025-07-27 19:03:31,320 - model_manager - INFO - 调度器创建成功: warmup_steps=3, total_steps=32
2025-07-27 19:03:31,320 - trainer - INFO - 开始第 1/1 轮训练
2025-07-27 19:03:37,727 - __main__ - ERROR - 训练任务 QNLI 失败: 'NoneType' object cannot be interpreted as an integer
2025-07-27 19:03:37,727 - __main__ - ERROR - ❌ QNLI 训练失败: 'NoneType' object cannot be interpreted as an integer
2025-07-27 19:03:37,730 - __main__ - INFO - 
============================================================
2025-07-27 19:03:37,730 - __main__ - INFO - 训练任务: RTE
2025-07-27 19:03:37,730 - __main__ - INFO - ============================================================
2025-07-27 19:03:37,731 - __main__ - INFO - 开始训练任务: RTE
2025-07-27 19:03:37,731 - __main__ - INFO - 加载训练和验证数据...
2025-07-27 19:03:37,731 - data_processor - INFO - 加载 RTE 任务的 train 数据: E:\spark文本分类\glue\RTE\train.tsv
2025-07-27 19:03:37,854 - data_processor - INFO - 加载 RTE 任务的 dev 数据: E:\spark文本分类\glue\RTE\dev.tsv
2025-07-27 19:03:38,093 - __main__ - INFO - 数据加载完成: 训练集=1000, 验证集=200
2025-07-27 19:03:38,093 - __main__ - INFO - 初始化模型...
2025-07-27 19:03:38,093 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:03:38,093 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:03:39,028 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:03:39,029 - model_manager - INFO - 为任务 RTE 加载模型，标签数量: 2
2025-07-27 19:03:39,595 - model_manager - INFO - 模型加载成功:
2025-07-27 19:03:39,595 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:03:39,596 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:03:39,596 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:03:39,597 - trainer - INFO - 训练器初始化完成: 任务=RTE, 输出目录=E:\spark文本分类\models\RTE
2025-07-27 19:03:39,597 - trainer - INFO - 准备训练数据...
2025-07-27 19:03:39,684 - trainer - INFO - 数据准备完成: 训练集=1000, 验证集=200
2025-07-27 19:03:39,684 - __main__ - INFO - 开始训练，参数: {'epochs': 1, 'learning_rate': 2e-05}
2025-07-27 19:03:39,684 - trainer - INFO - 开始训练: 轮数=1, 学习率=2e-05
2025-07-27 19:03:39,686 - model_manager - INFO - 优化器创建成功: lr=2e-05, weight_decay=0.01
2025-07-27 19:03:39,686 - model_manager - INFO - 调度器创建成功: warmup_steps=3, total_steps=32
2025-07-27 19:03:39,686 - trainer - INFO - 开始第 1/1 轮训练
2025-07-27 19:03:55,848 - trainer - INFO - Epoch 1/1:
2025-07-27 19:03:55,848 - trainer - INFO -   训练损失: 0.6961
2025-07-27 19:03:55,849 - trainer - INFO -   accuracy: 0.5350
2025-07-27 19:03:55,849 - trainer - INFO -   f1: 0.3497
2025-07-27 19:03:55,849 - trainer - INFO -   eval_loss: 0.6860
2025-07-27 19:03:57,193 - model_manager - INFO - 模型已保存到: E:\spark文本分类\models\RTE\best_model_hf
2025-07-27 19:03:57,193 - trainer - INFO - 保存最佳模型: accuracy=0.5350
2025-07-27 19:03:57,193 - trainer - INFO - 训练完成! 总时间: 17s
2025-07-27 19:03:57,195 - trainer - INFO - 训练历史已保存: E:\spark文本分类\models\RTE\training_history.json
2025-07-27 19:03:57,195 - __main__ - INFO - 任务 RTE 训练完成!
2025-07-27 19:03:57,195 - __main__ - INFO - 最佳指标: 0.5350
2025-07-27 19:03:57,195 - __main__ - INFO - 训练时间: 17.51秒
2025-07-27 19:03:57,198 - __main__ - INFO - ✅ RTE 训练成功
2025-07-27 19:03:57,198 - __main__ - INFO - 
============================================================
2025-07-27 19:03:57,198 - __main__ - INFO - 训练任务: WNLI
2025-07-27 19:03:57,199 - __main__ - INFO - ============================================================
2025-07-27 19:03:57,199 - __main__ - INFO - 开始训练任务: WNLI
2025-07-27 19:03:57,199 - __main__ - INFO - 加载训练和验证数据...
2025-07-27 19:03:57,199 - data_processor - INFO - 加载 WNLI 任务的 train 数据: E:\spark文本分类\glue\WNLI\train.tsv
2025-07-27 19:03:57,293 - data_processor - INFO - 加载 WNLI 任务的 dev 数据: E:\spark文本分类\glue\WNLI\dev.tsv
2025-07-27 19:03:57,525 - __main__ - INFO - 数据加载完成: 训练集=635, 验证集=71
2025-07-27 19:03:57,525 - __main__ - INFO - 初始化模型...
2025-07-27 19:03:57,525 - model_manager - INFO - 模型管理器初始化: bert-base-uncased, 设备: cuda
2025-07-27 19:03:57,525 - model_manager - INFO - 加载分词器: bert-base-uncased
2025-07-27 19:03:58,749 - model_manager - INFO - 分词器加载成功，词汇表大小: 30522
2025-07-27 19:03:58,750 - model_manager - INFO - 为任务 WNLI 加载模型，标签数量: 2
2025-07-27 19:03:59,346 - model_manager - INFO - 模型加载成功:
2025-07-27 19:03:59,347 - model_manager - INFO -   - 总参数: 109,483,778
2025-07-27 19:03:59,347 - model_manager - INFO -   - 可训练参数: 109,483,778
2025-07-27 19:03:59,347 - model_manager - INFO -   - 模型大小: 417.6 MB
2025-07-27 19:03:59,347 - trainer - INFO - 训练器初始化完成: 任务=WNLI, 输出目录=E:\spark文本分类\models\WNLI
2025-07-27 19:03:59,348 - trainer - INFO - 准备训练数据...
2025-07-27 19:03:59,454 - trainer - INFO - 数据准备完成: 训练集=635, 验证集=71
2025-07-27 19:03:59,454 - __main__ - INFO - 开始训练，参数: {'epochs': 1, 'learning_rate': 2e-05}
2025-07-27 19:03:59,455 - trainer - INFO - 开始训练: 轮数=1, 学习率=2e-05
2025-07-27 19:03:59,456 - model_manager - INFO - 优化器创建成功: lr=2e-05, weight_decay=0.01
2025-07-27 19:03:59,456 - model_manager - INFO - 调度器创建成功: warmup_steps=2, total_steps=20
2025-07-27 19:03:59,456 - trainer - INFO - 开始第 1/1 轮训练
2025-07-27 19:04:09,263 - trainer - INFO - Epoch 1/1:
2025-07-27 19:04:09,263 - trainer - INFO -   训练损失: 0.7326
2025-07-27 19:04:09,263 - trainer - INFO -   accuracy: 0.4225
2025-07-27 19:04:09,263 - trainer - INFO -   f1: 0.2264
2025-07-27 19:04:09,263 - trainer - INFO -   eval_loss: 0.7011
2025-07-27 19:04:10,739 - model_manager - INFO - 模型已保存到: E:\spark文本分类\models\WNLI\best_model_hf
2025-07-27 19:04:10,739 - trainer - INFO - 保存最佳模型: accuracy=0.4225
2025-07-27 19:04:10,739 - trainer - INFO - 训练完成! 总时间: 11s
2025-07-27 19:04:10,740 - trainer - INFO - 训练历史已保存: E:\spark文本分类\models\WNLI\training_history.json
2025-07-27 19:04:10,740 - __main__ - INFO - 任务 WNLI 训练完成!
2025-07-27 19:04:10,741 - __main__ - INFO - 最佳指标: 0.4225
2025-07-27 19:04:10,741 - __main__ - INFO - 训练时间: 11.28秒
2025-07-27 19:04:10,743 - __main__ - INFO - ✅ WNLI 训练成功
2025-07-27 19:04:10,743 - __main__ - INFO - 
============================================================
2025-07-27 19:04:10,743 - __main__ - INFO - 训练总结
2025-07-27 19:04:10,743 - __main__ - INFO - ============================================================
2025-07-27 19:04:10,743 - __main__ - INFO - 成功: 6/9
2025-07-27 19:04:10,743 - __main__ - INFO - 失败: ['MRPC', 'STS-B', 'QNLI']
2025-07-27 19:04:10,745 - utils - INFO - 训练结果已保存: E:\spark文本分类\results\all_tasks_training_results.json
2025-07-27 19:04:10,745 - utils - INFO - 
🎉 所有操作完成!
2025-07-27 19:04:10,745 - utils - INFO - 
📋 下一步:
2025-07-27 19:04:10,745 - utils - INFO - 1. 检查生成的提交文件
2025-07-27 19:04:10,745 - utils - INFO - 2. 访问 https://gluebenchmark.com/ 提交结果
2025-07-27 19:04:10,745 - utils - INFO - 3. 查看模型性能和排名
2025-07-27 19:04:11,233 - __main__ - INFO - Spark会话已关闭
2025-07-27 19:04:11,248 - py4j.clientserver - INFO - Closing down clientserver connection
