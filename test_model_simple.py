"""
简化的模型管理模块测试
"""
import os
import sys
import logging

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """测试模块导入"""
    print("=" * 50)
    print("测试模块导入")
    print("=" * 50)
    
    try:
        print("测试基础导入...")
        import torch
        print(f"✓ PyTorch版本: {torch.__version__}")
        print(f"✓ CUDA可用: {torch.cuda.is_available()}")
        
        print("\n测试transformers导入...")
        # 尝试最小化导入
        try:
            from transformers import AutoTokenizer
            print("✓ AutoTokenizer导入成功")
        except Exception as e:
            print(f"✗ AutoTokenizer导入失败: {e}")
            return False
        
        try:
            from transformers import AutoConfig
            print("✓ AutoConfig导入成功")
        except Exception as e:
            print(f"✗ AutoConfig导入失败: {e}")
            return False
        
        try:
            from transformers import BertForSequenceClassification
            print("✓ BertForSequenceClassification导入成功")
        except Exception as e:
            print(f"✗ BertForSequenceClassification导入失败: {e}")
            return False
        
        print("\n测试配置导入...")
        import config
        print(f"✓ 配置模块导入成功")
        print(f"  - 模型名称: {config.MODEL_NAME}")
        print(f"  - 设备: {config.DEVICE}")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tokenizer_basic():
    """测试基础分词器功能"""
    print("\n" + "=" * 50)
    print("测试基础分词器功能")
    print("=" * 50)
    
    try:
        from transformers import AutoTokenizer
        import config
        
        print(f"加载分词器: {config.MODEL_NAME}")
        tokenizer = AutoTokenizer.from_pretrained(config.MODEL_NAME)
        print(f"✓ 分词器加载成功")
        print(f"  - 词汇表大小: {len(tokenizer)}")
        
        # 测试基础分词
        test_text = "This is a test sentence."
        tokens = tokenizer.tokenize(test_text)
        print(f"✓ 分词测试成功")
        print(f"  - 原文: {test_text}")
        print(f"  - 分词结果: {tokens[:10]}...")  # 只显示前10个token
        
        # 测试编码
        encoded = tokenizer.encode(test_text, return_tensors="pt")
        print(f"✓ 编码测试成功")
        print(f"  - 编码形状: {encoded.shape}")
        
        # 测试批量编码
        test_texts = [
            "First sentence.",
            "Second sentence for testing.",
            "Third and final test sentence."
        ]
        
        batch_encoded = tokenizer(
            test_texts,
            padding=True,
            truncation=True,
            max_length=64,
            return_tensors="pt"
        )
        
        print(f"✓ 批量编码测试成功")
        print(f"  - 输入ID形状: {batch_encoded['input_ids'].shape}")
        print(f"  - 注意力掩码形状: {batch_encoded['attention_mask'].shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ 分词器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_basic():
    """测试基础模型功能"""
    print("\n" + "=" * 50)
    print("测试基础模型功能")
    print("=" * 50)
    
    try:
        from transformers import AutoTokenizer, BertForSequenceClassification, AutoConfig
        import torch
        import config
        
        print("加载模型组件...")
        
        # 加载分词器
        tokenizer = AutoTokenizer.from_pretrained(config.MODEL_NAME)
        print("✓ 分词器加载成功")
        
        # 加载配置
        model_config = AutoConfig.from_pretrained(config.MODEL_NAME)
        model_config.num_labels = 2  # 二分类
        print("✓ 模型配置加载成功")
        
        # 加载模型
        model = BertForSequenceClassification.from_pretrained(
            config.MODEL_NAME,
            config=model_config
        )
        print("✓ 模型加载成功")
        
        # 模型信息
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"  - 总参数: {total_params:,}")
        print(f"  - 可训练参数: {trainable_params:,}")
        print(f"  - 模型大小: {total_params * 4 / 1024 / 1024:.1f} MB")
        
        # 测试推理
        print("\n测试模型推理...")
        model.eval()
        
        test_text = "This is a test sentence for model inference."
        inputs = tokenizer(
            test_text,
            padding=True,
            truncation=True,
            max_length=128,
            return_tensors="pt"
        )
        
        with torch.no_grad():
            outputs = model(**inputs)
            logits = outputs.logits
            prediction = torch.argmax(logits, dim=-1)
            probabilities = torch.softmax(logits, dim=-1)
        
        print(f"✓ 模型推理成功")
        print(f"  - 输出形状: {logits.shape}")
        print(f"  - 预测结果: {prediction.item()}")
        print(f"  - 概率分布: {probabilities.squeeze().tolist()}")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_tasks():
    """测试不同任务的模型配置"""
    print("\n" + "=" * 50)
    print("测试不同任务的模型配置")
    print("=" * 50)
    
    try:
        from transformers import AutoConfig, BertForSequenceClassification
        import config
        
        # 测试不同任务
        test_tasks = {
            "CoLA": 2,      # 二分类
            "SST-2": 2,     # 二分类
            "MNLI": 3,      # 三分类
            "STS-B": 1      # 回归
        }
        
        for task, num_labels in test_tasks.items():
            print(f"\n测试任务: {task} (标签数: {num_labels})")
            
            # 配置模型
            model_config = AutoConfig.from_pretrained(config.MODEL_NAME)
            model_config.num_labels = num_labels
            
            # 加载模型
            model = BertForSequenceClassification.from_pretrained(
                config.MODEL_NAME,
                config=model_config
            )
            
            print(f"✓ {task} 模型配置成功")
            print(f"  - 标签数量: {model.config.num_labels}")
            print(f"  - 分类器输出维度: {model.classifier.out_features}")
        
        return True
        
    except Exception as e:
        print(f"✗ 任务配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sentence_pairs():
    """测试句子对处理"""
    print("\n" + "=" * 50)
    print("测试句子对处理")
    print("=" * 50)
    
    try:
        from transformers import AutoTokenizer, BertForSequenceClassification, AutoConfig
        import torch
        import config
        
        # 加载组件
        tokenizer = AutoTokenizer.from_pretrained(config.MODEL_NAME)
        model_config = AutoConfig.from_pretrained(config.MODEL_NAME)
        model_config.num_labels = 2
        model = BertForSequenceClassification.from_pretrained(
            config.MODEL_NAME,
            config=model_config
        )
        model.eval()
        
        # 测试句子对
        sentence_pairs = [
            ("The cat is on the mat.", "A cat is sitting on a mat."),
            ("I love programming.", "I hate programming."),
            ("Hello world.", "Goodbye world.")
        ]
        
        print("处理句子对...")
        for i, (sent1, sent2) in enumerate(sentence_pairs):
            print(f"\n句子对 {i+1}:")
            print(f"  句子1: {sent1}")
            print(f"  句子2: {sent2}")
            
            # 编码句子对
            inputs = tokenizer(
                sent1, sent2,
                padding=True,
                truncation=True,
                max_length=128,
                return_tensors="pt"
            )
            
            # 推理
            with torch.no_grad():
                outputs = model(**inputs)
                logits = outputs.logits
                prediction = torch.argmax(logits, dim=-1)
                probabilities = torch.softmax(logits, dim=-1)
            
            print(f"  预测: {prediction.item()}")
            print(f"  概率: {probabilities.squeeze().tolist()}")
        
        print(f"\n✓ 句子对处理测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 句子对测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始简化模型管理模块测试...")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 运行测试
    tests = [
        test_imports,
        test_tokenizer_basic,
        test_model_basic,
        test_different_tasks,
        test_sentence_pairs
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"测试 {test_func.__name__} 出现异常: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 所有测试通过! ({passed}/{total})")
    else:
        print(f"❌ 部分测试失败: {passed}/{total} 通过")
    
    for i, (test_func, result) in enumerate(zip(tests, results)):
        status = "✅" if result else "❌"
        print(f"{status} {test_func.__name__}")
