#!/usr/bin/env python3
"""
创建本地Spark GLUE项目
"""
import os
import json
import pandas as pd
import random
from pathlib import Path

def create_project_structure():
    """创建项目目录结构"""
    print("📁 创建项目目录结构...")
    
    directories = [
        "src",
        "data/glue",
        "models",
        "results", 
        "logs",
        "configs"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ 创建目录: {directory}")

def create_sample_data():
    """创建示例GLUE数据"""
    print("\n📊 创建示例GLUE数据...")
    
    # 创建SST-2数据（情感分析）
    sst2_dir = "data/glue/SST-2"
    os.makedirs(sst2_dir, exist_ok=True)
    
    # SST-2训练数据
    sst2_train = []
    positive_sentences = [
        "This movie is absolutely fantastic!",
        "I love this film so much.",
        "Amazing acting and brilliant plot.",
        "Excellent cinematography and direction.",
        "One of the best movies I've ever seen.",
        "Incredible performance by all actors.",
        "Beautifully crafted and engaging story.",
        "Outstanding visual effects and soundtrack."
    ]
    
    negative_sentences = [
        "This movie is terrible and boring.",
        "Waste of time and money.",
        "Poor acting and weak storyline.",
        "Completely disappointing experience.",
        "One of the worst films ever made.",
        "Terrible direction and bad script.",
        "Boring and predictable plot.",
        "Awful acting and poor production."
    ]
    
    # 生成训练数据
    for i in range(500):
        if i % 2 == 0:
            sentence = random.choice(positive_sentences)
            label = 1
        else:
            sentence = random.choice(negative_sentences)
            label = 0
        sst2_train.append({"sentence": sentence, "label": label})
    
    # 生成验证数据
    sst2_dev = []
    for i in range(50):
        if i % 2 == 0:
            sentence = random.choice(positive_sentences)
            label = 1
        else:
            sentence = random.choice(negative_sentences)
            label = 0
        sst2_dev.append({"sentence": sentence, "label": label})
    
    # 生成测试数据
    sst2_test = []
    all_sentences = positive_sentences + negative_sentences
    for i in range(100):
        sentence = random.choice(all_sentences)
        sst2_test.append({"index": i, "sentence": sentence})
    
    # 保存SST-2数据
    pd.DataFrame(sst2_train).to_csv(f"{sst2_dir}/train.tsv", sep='\t', index=False)
    pd.DataFrame(sst2_dev).to_csv(f"{sst2_dir}/dev.tsv", sep='\t', index=False)
    pd.DataFrame(sst2_test).to_csv(f"{sst2_dir}/test.tsv", sep='\t', index=False)
    
    print(f"✅ SST-2数据已创建: {len(sst2_train)}训练/{len(sst2_dev)}验证/{len(sst2_test)}测试")
    
    # 创建RTE数据（文本蕴含）
    rte_dir = "data/glue/RTE"
    os.makedirs(rte_dir, exist_ok=True)
    
    premise_hypothesis_pairs = [
        ("The cat is sleeping on the sofa.", "There is a cat on the furniture.", 1),
        ("John went to the grocery store.", "John is shopping for food.", 1),
        ("The weather is sunny today.", "It's raining outside.", 0),
        ("She is reading a science book.", "She is studying science.", 1),
        ("The car is painted red.", "The car is blue in color.", 0),
        ("The dog is barking loudly.", "There is a dog making noise.", 1),
        ("Students are taking an exam.", "Students are playing games.", 0),
        ("The restaurant serves Italian food.", "You can eat pasta there.", 1),
        ("The movie starts at 8 PM.", "The movie ends at 8 PM.", 0),
        ("She works as a teacher.", "She educates students.", 1)
    ]
    
    # 生成RTE训练数据
    rte_train = []
    for i in range(200):
        premise, hypothesis, label = random.choice(premise_hypothesis_pairs)
        rte_train.append({
            "index": i,
            "sentence1": premise,
            "sentence2": hypothesis,
            "label": label
        })
    
    # 生成RTE验证数据
    rte_dev = []
    for i in range(30):
        premise, hypothesis, label = random.choice(premise_hypothesis_pairs)
        rte_dev.append({
            "index": i,
            "sentence1": premise,
            "sentence2": hypothesis,
            "label": label
        })
    
    # 生成RTE测试数据
    rte_test = []
    for i in range(50):
        premise, hypothesis, _ = random.choice(premise_hypothesis_pairs)
        rte_test.append({
            "index": i,
            "sentence1": premise,
            "sentence2": hypothesis
        })
    
    # 保存RTE数据
    pd.DataFrame(rte_train).to_csv(f"{rte_dir}/train.tsv", sep='\t', index=False)
    pd.DataFrame(rte_dev).to_csv(f"{rte_dir}/dev.tsv", sep='\t', index=False)
    pd.DataFrame(rte_test).to_csv(f"{rte_dir}/test.tsv", sep='\t', index=False)
    
    print(f"✅ RTE数据已创建: {len(rte_train)}训练/{len(rte_dev)}验证/{len(rte_test)}测试")

def create_config():
    """创建配置文件"""
    print("\n⚙️ 创建配置文件...")
    
    config = {
        "project": {
            "name": "Spark_GLUE_Local_Test",
            "version": "1.0.0",
            "description": "本地Spark GLUE文本分类测试项目"
        },
        
        "paths": {
            "data_root": "data/glue",
            "model_dir": "models",
            "results_dir": "results",
            "logs_dir": "logs"
        },
        
        "model": {
            "name": "distilbert-base-uncased",
            "max_length": 128,
            "batch_size": 8,  # 适合RTX 3060
            "learning_rate": 2e-5,
            "num_epochs": 2,
            "warmup_steps": 100,
            "weight_decay": 0.01
        },
        
        "spark": {
            "app_name": "GLUE_Local_Test",
            "master": "local[*]",
            "driver_memory": "4g",
            "executor_memory": "2g",
            "sql_adaptive_enabled": True,
            "sql_adaptive_coalesce_partitions_enabled": True
        },
        
        "training": {
            "tasks": ["SST-2", "RTE"],
            "save_steps": 100,
            "eval_steps": 100,
            "logging_steps": 50,
            "seed": 42,
            "use_gpu": True,
            "fp16": True  # 混合精度训练，节省显存
        },
        
        "hardware": {
            "gpu_model": "NVIDIA GeForce RTX 3060",
            "gpu_memory": "6GB",
            "pytorch_version": "2.7.1+cu118",
            "spark_version": "3.4.0"
        }
    }
    
    with open("configs/config.json", "w", encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("✅ 配置文件已创建: configs/config.json")

def create_requirements():
    """创建requirements.txt"""
    print("\n📋 创建requirements.txt...")
    
    requirements = [
        "# PyTorch和相关库",
        "torch>=2.0.0",
        "torchvision>=0.15.0",
        "torchaudio>=2.0.0",
        "",
        "# Hugging Face生态",
        "transformers>=4.30.0",
        "datasets>=2.12.0",
        "accelerate>=0.20.0",
        "",
        "# Spark和数据处理",
        "pyspark>=3.4.0",
        "pandas>=2.0.0",
        "numpy>=1.24.0",
        "",
        "# 机器学习和评估",
        "scikit-learn>=1.3.0",
        "scipy>=1.10.0",
        "",
        "# 可视化和进度条",
        "matplotlib>=3.7.0",
        "seaborn>=0.12.0",
        "tqdm>=4.65.0",
        "",
        "# 开发工具",
        "jupyter>=1.0.0",
        "notebook>=6.5.0",
        "ipywidgets>=8.0.0"
    ]
    
    with open("requirements.txt", "w", encoding='utf-8') as f:
        f.write("\n".join(requirements))
    
    print("✅ requirements.txt已创建")

def main():
    """主函数"""
    print("🚀 创建本地Spark GLUE测试项目")
    print("=" * 50)
    print("🖥️ 检测到的硬件配置:")
    print("   GPU: NVIDIA GeForce RTX 3060 (6GB)")
    print("   PyTorch: 2.7.1+cu118")
    print("   Spark: 3.4.0")
    print("=" * 50)
    
    # 创建项目结构
    create_project_structure()
    
    # 创建示例数据
    create_sample_data()
    
    # 创建配置
    create_config()
    
    # 创建requirements
    create_requirements()
    
    print("\n" + "=" * 50)
    print("🎉 项目创建完成！")
    print("\n📋 下一步操作:")
    print("1. 检查环境: python check_environment.py")
    print("2. 安装依赖: pip install -r requirements.txt")
    print("3. 运行测试: python src/main.py")
    print("\n💡 提示: 您的RTX 3060配置很适合深度学习训练！")

if __name__ == "__main__":
    main()
