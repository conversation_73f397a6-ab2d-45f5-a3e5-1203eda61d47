#!/usr/bin/env python3
"""
创建本地Spark GLUE项目结构
"""
import os
import urllib.request
import zipfile
import json
from pathlib import Path

def create_project_structure():
    """创建项目目录结构"""
    print("📁 创建项目目录结构...")
    
    directories = [
        "src",
        "data",
        "data/glue",
        "models",
        "results",
        "logs",
        "configs",
        "tests",
        "notebooks",
        "scripts"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ 创建目录: {directory}")

def download_glue_data():
    """下载GLUE数据集"""
    print("\n📥 准备下载GLUE数据集...")
    
    # GLUE任务列表
    tasks = ["SST-2", "CoLA", "RTE", "MRPC", "QNLI", "QQP", "MNLI", "STS-B", "WNLI"]
    
    # 创建示例数据（如果无法下载真实数据）
    for task in tasks:
        task_dir = f"data/glue/{task}"
        os.makedirs(task_dir, exist_ok=True)
        
        # 创建示例数据文件
        create_sample_data(task, task_dir)
        print(f"✅ 创建 {task} 示例数据")

def create_sample_data(task, task_dir):
    """创建示例数据"""
    import pandas as pd
    import random
    
    # 设置随机种子
    random.seed(42)
    
    if task == "SST-2":
        # 情感分析数据
        sentences = [
            "This movie is great!",
            "I love this film.",
            "Terrible movie, waste of time.",
            "Amazing acting and plot.",
            "Boring and predictable.",
            "Excellent cinematography.",
            "Not worth watching.",
            "Brilliant performance by the actors."
        ]
        
        # 训练集
        train_data = []
        for i in range(100):
            sentence = random.choice(sentences)
            label = 1 if any(word in sentence.lower() for word in ['great', 'love', 'amazing', 'excellent', 'brilliant']) else 0
            train_data.append({"sentence": sentence, "label": label})
        
        train_df = pd.DataFrame(train_data)
        train_df.to_csv(f"{task_dir}/train.tsv", sep='\t', index=False)
        
        # 验证集
        dev_data = []
        for i in range(20):
            sentence = random.choice(sentences)
            label = 1 if any(word in sentence.lower() for word in ['great', 'love', 'amazing', 'excellent', 'brilliant']) else 0
            dev_data.append({"sentence": sentence, "label": label})
        
        dev_df = pd.DataFrame(dev_data)
        dev_df.to_csv(f"{task_dir}/dev.tsv", sep='\t', index=False)
        
        # 测试集
        test_data = []
        for i in range(30):
            sentence = random.choice(sentences)
            test_data.append({"index": i, "sentence": sentence})
        
        test_df = pd.DataFrame(test_data)
        test_df.to_csv(f"{task_dir}/test.tsv", sep='\t', index=False)
    
    elif task == "RTE":
        # 文本蕴含数据
        premise_hypothesis_pairs = [
            ("The cat is on the mat.", "There is a cat.", 1),
            ("John went to the store.", "John is shopping.", 1),
            ("The weather is sunny.", "It's raining.", 0),
            ("She is reading a book.", "She is studying.", 1),
            ("The car is red.", "The car is blue.", 0),
        ]
        
        # 训练集
        train_data = []
        for i in range(50):
            premise, hypothesis, label = random.choice(premise_hypothesis_pairs)
            train_data.append({
                "index": i,
                "sentence1": premise,
                "sentence2": hypothesis,
                "label": label
            })
        
        train_df = pd.DataFrame(train_data)
        train_df.to_csv(f"{task_dir}/train.tsv", sep='\t', index=False)
        
        # 验证集
        dev_data = []
        for i in range(10):
            premise, hypothesis, label = random.choice(premise_hypothesis_pairs)
            dev_data.append({
                "index": i,
                "sentence1": premise,
                "sentence2": hypothesis,
                "label": label
            })
        
        dev_df = pd.DataFrame(dev_data)
        dev_df.to_csv(f"{task_dir}/dev.tsv", sep='\t', index=False)
        
        # 测试集
        test_data = []
        for i in range(15):
            premise, hypothesis, _ = random.choice(premise_hypothesis_pairs)
            test_data.append({
                "index": i,
                "sentence1": premise,
                "sentence2": hypothesis
            })
        
        test_df = pd.DataFrame(test_data)
        test_df.to_csv(f"{task_dir}/test.tsv", sep='\t', index=False)
    
    else:
        # 其他任务的简单示例数据
        if task in ["MRPC", "QQP"]:
            # 句子对任务
            train_data = []
            for i in range(50):
                train_data.append({
                    "index": i,
                    "sentence1": f"This is sentence {i}",
                    "sentence2": f"This is another sentence {i}",
                    "label": random.randint(0, 1)
                })
            
            train_df = pd.DataFrame(train_data)
            train_df.to_csv(f"{task_dir}/train.tsv", sep='\t', index=False)
            
            # 验证集和测试集类似...
            dev_df = train_df.head(10).copy()
            dev_df.to_csv(f"{task_dir}/dev.tsv", sep='\t', index=False)
            
            test_df = train_df.head(15)[['index', 'sentence1', 'sentence2']].copy()
            test_df.to_csv(f"{task_dir}/test.tsv", sep='\t', index=False)
        
        else:
            # 单句任务
            train_data = []
            for i in range(50):
                train_data.append({
                    "sentence": f"This is sample sentence {i}",
                    "label": random.randint(0, 1)
                })
            
            train_df = pd.DataFrame(train_data)
            train_df.to_csv(f"{task_dir}/train.tsv", sep='\t', index=False)
            
            dev_df = train_df.head(10).copy()
            dev_df.to_csv(f"{task_dir}/dev.tsv", sep='\t', index=False)
            
            test_df = train_df.head(15)[['sentence']].copy()
            test_df['index'] = range(len(test_df))
            test_df = test_df[['index', 'sentence']]
            test_df.to_csv(f"{task_dir}/test.tsv", sep='\t', index=False)

def create_config_files():
    """创建配置文件"""
    print("\n⚙️ 创建配置文件...")
    
    # 主配置文件
    config = {
        "project_name": "Spark_GLUE_Local_Test",
        "data_dir": "data/glue",
        "model_dir": "models",
        "results_dir": "results",
        "logs_dir": "logs",
        
        "model_config": {
            "model_name": "distilbert-base-uncased",
            "max_length": 128,
            "batch_size": 16,
            "learning_rate": 2e-5,
            "num_epochs": 2,
            "warmup_steps": 100
        },
        
        "spark_config": {
            "app_name": "GLUE_Local_Test",
            "master": "local[*]",
            "driver_memory": "4g",
            "executor_memory": "2g"
        },
        
        "tasks": ["SST-2", "RTE", "CoLA", "MRPC"],
        
        "training": {
            "save_steps": 100,
            "eval_steps": 100,
            "logging_steps": 50,
            "seed": 42
        }
    }
    
    with open("configs/config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print("✅ 配置文件已创建: configs/config.json")

def create_requirements():
    """创建requirements.txt"""
    print("\n📋 创建requirements.txt...")
    
    requirements = [
        "torch>=1.9.0",
        "transformers>=4.20.0",
        "datasets>=2.0.0",
        "pyspark>=3.0.0",
        "pandas>=1.3.0",
        "numpy>=1.21.0",
        "scikit-learn>=1.0.0",
        "tqdm>=4.60.0",
        "matplotlib>=3.4.0",
        "seaborn>=0.11.0",
        "jupyter>=1.0.0",
        "notebook>=6.4.0"
    ]
    
    with open("requirements.txt", "w") as f:
        f.write("\n".join(requirements))
    
    print("✅ requirements.txt已创建")

def create_readme():
    """创建README文件"""
    print("\n📝 创建README.md...")
    
    readme_content = """# Spark GLUE 文本分类项目

## 项目简介
基于Apache Spark和Hugging Face Transformers的GLUE基准测试文本分类项目。

## 环境要求
- Python 3.8+
- Java 8+
- Apache Spark 3.0+
- PyTorch 1.9+

## 快速开始

### 1. 环境检查
```bash
python check_environment.py
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 运行测试
```bash
python src/main.py --task SST-2 --mode train
```

## 项目结构
```
├── src/                 # 源代码
├── data/               # 数据目录
├── models/             # 模型保存
├── results/            # 结果输出
├── configs/            # 配置文件
├── tests/              # 测试代码
└── notebooks/          # Jupyter notebooks
```

## 支持的任务
- SST-2: 情感分析
- RTE: 文本蕴含
- CoLA: 语言可接受性
- MRPC: 句子对匹配

## 使用说明
详细使用说明请参考 `notebooks/` 目录下的示例。
"""
    
    with open("README.md", "w") as f:
        f.write(readme_content)
    
    print("✅ README.md已创建")

def main():
    """主函数"""
    print("🚀 开始创建本地Spark GLUE项目...")
    print("=" * 50)
    
    # 创建项目结构
    create_project_structure()
    
    # 下载/创建数据
    download_glue_data()
    
    # 创建配置文件
    create_config_files()
    
    # 创建requirements
    create_requirements()
    
    # 创建README
    create_readme()
    
    print("\n" + "=" * 50)
    print("🎉 项目结构创建完成！")
    print("\n下一步:")
    print("1. 运行环境检查: python check_environment.py")
    print("2. 安装依赖: pip install -r requirements.txt")
    print("3. 开始测试: python src/main.py")

if __name__ == "__main__":
    main()
