{"environment": {"python_version": "3.10.6", "spark_home": "D:\\spark-3.5.0-bin-hadoop3\\spark-3.5.0-bin-hadoop3", "spark_home_exists": true, "java_home": "D:\\Java\\jdk-17.0.2", "java_version": "java version \"17.0.2\" 2022-01-18 LTS"}, "pyspark_import": {"pyspark_version": "3.5.0", "pyspark_import": true, "pyspark.sql_SparkSession": true, "pyspark.sql.functions_col": true, "pyspark.sql.types_StructType": true, "pyspark.ml_Pipeline": true, "pyspark.mllib.stat_Statistics": true}, "spark_session": {"session_creation": true, "creation_time": 3.650498628616333, "spark_version": "3.5.0", "app_name": "SparkTest", "master": "local[*]", "default_parallelism": 20}, "dataframe_operations": {"dataframe_creation": true, "row_count": 5, "column_selection": true, "data_filtering": true, "filtered_count": 2, "groupby_aggregation": true, "group_count": 5, "data_sorting": true}, "sql_operations": {"temp_view_creation": true, "basic_sql_query": true, "basic_query_count": 3, "aggregation_sql_query": true, "agg_query_count": 3, "window_function_query": true}, "file_operations": {"csv_io": true, "csv_read_count": 5, "json_io": true, "json_read_count": 5, "parquet_io": true, "parquet_read_count": 5}, "performance": {"count_time": 11.756263732910156, "filter_time": 0.3758969306945801, "groupby_time": 0.907879114151001, "sort_time": 0.39048004150390625, "performance_test": true, "total_records": 100000}}