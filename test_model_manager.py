"""
测试模型管理模块
"""
import os
import sys
import logging
import torch
from pyspark.sql import SparkSession

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from model_manager import GLUEModelManager, SparkModelManager, ModelEvaluator
from data_processor import GLUEDataProcessor
from utils import setup_logging, set_seed, get_device
import config

def create_spark_session():
    """创建Spark会话"""
    try:
        spark = SparkSession.builder \
            .appName("GLUE-Model-Test") \
            .master("local[2]") \
            .config("spark.driver.memory", "2g") \
            .config("spark.executor.memory", "2g") \
            .getOrCreate()
        
        print(f"Spark会话创建成功，版本: {spark.version}")
        return spark
    except Exception as e:
        print(f"创建Spark会话失败: {e}")
        return None

def test_model_loading():
    """测试模型加载功能"""
    print("=" * 50)
    print("测试模型加载功能")
    print("=" * 50)
    
    try:
        # 创建模型管理器
        model_manager = GLUEModelManager()
        print(f"✓ 模型管理器创建成功")
        print(f"  - 模型名称: {model_manager.model_name}")
        print(f"  - 设备: {model_manager.device}")
        
        # 测试分词器加载
        print("\n测试分词器加载...")
        tokenizer = model_manager.load_tokenizer()
        print(f"✓ 分词器加载成功")
        print(f"  - 词汇表大小: {len(tokenizer)}")
        print(f"  - 特殊token: [PAD]={tokenizer.pad_token}, [CLS]={tokenizer.cls_token}")
        
        # 测试分词功能
        test_texts = [
            "This is a test sentence.",
            "Another test sentence for verification."
        ]
        
        encoded = model_manager.tokenize_texts(test_texts, max_length=64)
        print(f"✓ 分词测试成功")
        print(f"  - 输入形状: {encoded['input_ids'].shape}")
        print(f"  - 注意力掩码形状: {encoded['attention_mask'].shape}")
        
        # 测试句子对分词
        sentence_pairs = [
            ("This is sentence one.", "This is sentence two."),
            ("First sentence.", "Second sentence.")
        ]
        
        encoded_pairs = model_manager.tokenize_texts(sentence_pairs, max_length=64)
        print(f"✓ 句子对分词测试成功")
        print(f"  - 输入形状: {encoded_pairs['input_ids'].shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_task_specific_models():
    """测试任务特定模型加载"""
    print("\n" + "=" * 50)
    print("测试任务特定模型加载")
    print("=" * 50)
    
    try:
        model_manager = GLUEModelManager()
        model_manager.load_tokenizer()
        
        # 测试不同任务的模型
        test_tasks = ["CoLA", "SST-2", "STS-B", "MNLI"]
        
        for task in test_tasks:
            print(f"\n测试任务: {task}")
            
            # 加载任务特定模型
            model = model_manager.load_model_for_task(task)
            print(f"✓ {task} 模型加载成功")
            
            # 获取模型信息
            info = model_manager.get_model_info()
            print(f"  - 总参数: {info['total_parameters']:,}")
            print(f"  - 可训练参数: {info['trainable_parameters']:,}")
            print(f"  - 模型大小: {info['model_size_mb']:.1f} MB")
            print(f"  - 标签数量: {info['config']['num_labels']}")
            
            # 创建优化器
            optimizer = model_manager.create_optimizer()
            print(f"✓ 优化器创建成功")
            
            # 创建调度器
            scheduler = model_manager.create_scheduler(optimizer, num_training_steps=1000)
            print(f"✓ 调度器创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 任务特定模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_inference():
    """测试模型推理功能"""
    print("\n" + "=" * 50)
    print("测试模型推理功能")
    print("=" * 50)
    
    try:
        model_manager = GLUEModelManager()
        model_manager.load_tokenizer()
        
        # 测试分类任务推理
        print("测试分类任务推理 (CoLA)...")
        model = model_manager.load_model_for_task("CoLA")
        model.eval()
        
        test_sentences = [
            "This is a grammatically correct sentence.",
            "This sentence is also correct.",
            "Colorless green ideas sleep furiously."  # 著名的语法正确但语义奇怪的句子
        ]
        
        # 分词
        inputs = model_manager.tokenize_texts(test_sentences)
        inputs = {k: v.to(model_manager.device) for k, v in inputs.items()}
        
        # 推理
        with torch.no_grad():
            outputs = model(**inputs)
            predictions = torch.argmax(outputs.logits, dim=-1)
            probabilities = torch.softmax(outputs.logits, dim=-1)
        
        print(f"✓ 分类推理成功")
        print(f"  - 预测结果: {predictions.cpu().numpy()}")
        print(f"  - 概率分布形状: {probabilities.shape}")
        
        # 测试回归任务推理
        print("\n测试回归任务推理 (STS-B)...")
        model = model_manager.load_model_for_task("STS-B")
        model.eval()
        
        sentence_pairs = [
            ("The cat is on the mat.", "A cat is sitting on a mat."),
            ("I love programming.", "I hate programming."),
            ("Hello world.", "Goodbye world.")
        ]
        
        inputs = model_manager.tokenize_texts(sentence_pairs)
        inputs = {k: v.to(model_manager.device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = model(**inputs)
            scores = outputs.logits.squeeze()
        
        print(f"✓ 回归推理成功")
        print(f"  - 相似度分数: {scores.cpu().numpy()}")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型推理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_evaluation():
    """测试模型评估功能"""
    print("\n" + "=" * 50)
    print("测试模型评估功能")
    print("=" * 50)
    
    try:
        model_manager = GLUEModelManager()
        model_manager.load_tokenizer()
        
        # 创建评估器
        evaluator = ModelEvaluator(model_manager)
        
        # 测试分类任务评估
        print("测试分类任务评估 (CoLA)...")
        model = model_manager.load_model_for_task("CoLA")
        
        # 模拟一些测试数据
        test_texts = [
            "This is a correct sentence.",
            "This also correct sentence.",
            "Sentence this correct is.",
            "Very good sentence structure."
        ]
        test_labels = [1, 1, 0, 1]  # 1=正确, 0=不正确
        
        metrics = evaluator.evaluate_on_batch(test_texts, test_labels, "CoLA")
        print(f"✓ 分类评估成功")
        print(f"  - 准确率: {metrics['accuracy']:.4f}")
        print(f"  - Matthews相关系数: {metrics['matthews_corrcoef']:.4f}")
        
        # 计算损失
        loss = evaluator.compute_loss(test_texts, test_labels, "CoLA")
        print(f"  - 损失: {loss:.4f}")
        
        # 测试回归任务评估
        print("\n测试回归任务评估 (STS-B)...")
        model = model_manager.load_model_for_task("STS-B")
        
        sentence_pairs = [
            ("The cat is sleeping.", "A cat is resting."),
            ("I love pizza.", "I hate vegetables."),
        ]
        similarity_scores = [4.5, 1.0]  # 0-5分相似度
        
        metrics = evaluator.evaluate_on_batch(sentence_pairs, similarity_scores, "STS-B")
        print(f"✓ 回归评估成功")
        print(f"  - Pearson相关系数: {metrics['pearson_correlation']:.4f}")
        print(f"  - Spearman相关系数: {metrics['spearman_correlation']:.4f}")
        print(f"  - MSE: {metrics['mse']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型评估测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_save_load():
    """测试模型保存和加载"""
    print("\n" + "=" * 50)
    print("测试模型保存和加载")
    print("=" * 50)
    
    try:
        # 创建临时保存目录
        save_dir = os.path.join(config.MODEL_SAVE_DIR, "test_model")
        
        # 加载并保存模型
        model_manager = GLUEModelManager()
        model_manager.load_tokenizer()
        model = model_manager.load_model_for_task("CoLA")
        
        print("保存模型...")
        model_manager.save_model(save_dir, "CoLA")
        print(f"✓ 模型已保存到: {save_dir}")
        
        # 创建新的模型管理器并加载模型
        print("加载模型...")
        new_model_manager = GLUEModelManager()
        loaded_model = new_model_manager.load_model(save_dir)
        print(f"✓ 模型加载成功")
        
        # 验证模型功能
        test_text = ["This is a test sentence."]
        inputs = new_model_manager.tokenize_texts(test_text)
        inputs = {k: v.to(new_model_manager.device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = loaded_model(**inputs)
            prediction = torch.argmax(outputs.logits, dim=-1)
        
        print(f"✓ 加载的模型推理成功: {prediction.item()}")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型保存加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试模型管理模块...")
    
    # 设置环境
    set_seed(config.RANDOM_SEED)
    logger = setup_logging(config.LOG_DIR)
    
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"设备: {get_device()}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    
    # 运行测试
    tests = [
        test_model_loading,
        test_task_specific_models,
        test_model_inference,
        test_model_evaluation,
        test_model_save_load
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"测试 {test_func.__name__} 出现异常: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 所有测试通过! ({passed}/{total})")
    else:
        print(f"❌ 部分测试失败: {passed}/{total} 通过")
    
    for i, (test_func, result) in enumerate(zip(tests, results)):
        status = "✅" if result else "❌"
        print(f"{status} {test_func.__name__}")
