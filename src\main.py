#!/usr/bin/env python3
"""
本地Spark GLUE项目主程序
"""
import os
import sys
import argparse
import logging
from datetime import datetime
import torch

# 添加src目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import config
from local_trainer import LocalGLUETrainer

def setup_logging():
    """设置日志"""
    log_dir = config.get("paths.logs_dir", "logs")
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f"glue_local_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"📝 日志文件: {log_file}")
    return logger

def print_system_info():
    """打印系统信息"""
    print("🖥️ 系统信息:")
    print(f"   Python版本: {sys.version}")
    
    if torch.cuda.is_available():
        print(f"   GPU: {torch.cuda.get_device_name(0)}")
        print(f"   CUDA版本: {torch.version.cuda}")
        print(f"   显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    else:
        print("   设备: CPU")
    
    try:
        import pyspark
        print(f"   Spark版本: {pyspark.__version__}")
    except ImportError:
        print("   Spark: 未安装")
    
    print("=" * 50)

def train_single_task(task_name):
    """训练单个任务"""
    logger = logging.getLogger(__name__)
    logger.info(f"🎯 开始训练任务: {task_name}")
    
    try:
        # 创建训练器
        trainer = LocalGLUETrainer(task_name)
        
        # 开始训练
        results = trainer.train()
        
        # 清理资源
        trainer.close()
        
        logger.info(f"✅ 任务 {task_name} 训练完成")
        
        # 显示结果摘要
        if 'eval_results' in results and results['eval_results']:
            accuracy = results['eval_results'].get('eval_accuracy', 0)
            print(f"\n🎯 {task_name} 训练结果:")
            print(f"   准确率: {accuracy:.4f}")
            print(f"   训练时间: {results['training_time']:.1f}秒")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ 任务 {task_name} 训练失败: {str(e)}")
        raise

def train_multiple_tasks(task_list):
    """训练多个任务"""
    logger = logging.getLogger(__name__)
    logger.info(f"🚀 开始训练多个任务: {task_list}")
    
    results_summary = {}
    
    for task_name in task_list:
        try:
            print(f"\n{'='*20} {task_name} {'='*20}")
            results = train_single_task(task_name)
            results_summary[task_name] = {
                "status": "success",
                "results": results
            }
        except Exception as e:
            logger.error(f"❌ 任务 {task_name} 失败: {str(e)}")
            results_summary[task_name] = {
                "status": "failed",
                "error": str(e)
            }
    
    # 保存总结果
    summary_file = os.path.join(config.get("paths.results_dir", "results"), "training_summary.json")
    
    import json
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(results_summary, f, indent=2, ensure_ascii=False)
    
    logger.info(f"📄 训练总结已保存: {summary_file}")
    
    # 显示总结
    print(f"\n{'='*50}")
    print("🎯 训练总结:")
    
    for task_name, result in results_summary.items():
        if result["status"] == "success":
            eval_results = result["results"].get("eval_results", {})
            accuracy = eval_results.get("eval_accuracy", 0)
            print(f"✅ {task_name}: 准确率 {accuracy:.4f}")
        else:
            print(f"❌ {task_name}: 失败")
    
    return results_summary

def run_demo():
    """运行演示"""
    logger = logging.getLogger(__name__)
    logger.info("🎪 运行演示模式")
    
    print("🎪 Spark GLUE 本地测试演示")
    print("=" * 50)
    
    # 显示可用任务
    available_tasks = config.get("training.tasks", ["SST-2", "RTE"])
    print(f"📋 可用任务: {', '.join(available_tasks)}")
    
    # 运行第一个任务作为演示
    demo_task = available_tasks[0]
    print(f"\n🎯 演示任务: {demo_task}")
    
    try:
        results = train_single_task(demo_task)
        print(f"\n🎉 演示完成！")
        
        if 'eval_results' in results and results['eval_results']:
            accuracy = results['eval_results'].get('eval_accuracy', 0)
            print(f"🎯 演示结果: {demo_task} 准确率 {accuracy:.4f}")
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="本地Spark GLUE文本分类项目")
    parser.add_argument("--task", type=str, help="指定训练任务 (如: SST-2, RTE)")
    parser.add_argument("--tasks", type=str, nargs='+', help="指定多个训练任务")
    parser.add_argument("--all", action='store_true', help="训练所有任务")
    parser.add_argument("--demo", action='store_true', help="运行演示")
    parser.add_argument("--config", type=str, help="指定配置文件路径")
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging()
    
    # 打印系统信息
    print_system_info()
    
    # 创建必要目录
    config.create_directories()
    
    logger.info("🚀 本地Spark GLUE项目启动")
    
    try:
        if args.demo:
            run_demo()
        
        elif args.task:
            train_single_task(args.task)
        
        elif args.tasks:
            train_multiple_tasks(args.tasks)
        
        elif args.all:
            all_tasks = config.get("training.tasks", ["SST-2", "RTE"])
            train_multiple_tasks(all_tasks)
        
        else:
            print("请指定要执行的操作:")
            print("  --demo          运行演示")
            print("  --task TASK     训练单个任务")
            print("  --tasks T1 T2   训练多个任务")
            print("  --all           训练所有任务")
            print("\n示例:")
            print("  python src/main.py --demo")
            print("  python src/main.py --task SST-2")
            print("  python src/main.py --tasks SST-2 RTE")
            print("  python src/main.py --all")
        
        logger.info("✅ 程序执行完成")
        
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {str(e)}")
        raise

if __name__ == "__main__":
    main()
