{"CoLA": {"task": "CoLA", "train_time": 107.29737162590027, "best_metric": 0.42271116554738475, "training_history": [{"epoch": 1, "train_loss": 0.606191965810796, "eval_metrics": {"accuracy": 0.7416666666666667, "matthews_corrcoef": 0.3283764130062835, "eval_loss": 0.5412237079519975}, "learning_rate": 1.1058823529411766e-05}, {"epoch": 2, "train_loss": 0.4498459367041892, "eval_metrics": {"accuracy": 0.7716666666666666, "matthews_corrcoef": 0.42271116554738475, "eval_loss": 0.53433800057361}, "learning_rate": 0.0}], "submission_file": "E:\\spark文本分类\\results\\CoLA_submission.tsv", "status": "success"}, "RTE": {"task": "RTE", "train_time": 103.2834734916687, "best_metric": 0.6498194945848376, "training_history": [{"epoch": 1, "train_loss": 0.6802048965906485, "eval_metrics": {"accuracy": 0.6173285198555957, "f1": 0.6918604651162791, "eval_loss": 0.647216796875}, "learning_rate": 1.1063829787234044e-05}, {"epoch": 2, "train_loss": 0.5720089742770562, "eval_metrics": {"accuracy": 0.6498194945848376, "f1": 0.694006309148265, "eval_loss": 0.6337014105584886}, "learning_rate": 0.0}], "submission_file": "E:\\spark文本分类\\results\\RTE_submission.tsv", "status": "success"}, "WNLI": {"task": "WNLI", "train_time": 29.534403562545776, "best_metric": 0.43661971830985913, "training_history": [{"epoch": 1, "train_loss": 0.7004408776760102, "eval_metrics": {"accuracy": 0.43661971830985913, "f1": 0.2857142857142857, "eval_loss": 0.7056268056233724}, "learning_rate": 1.1111111111111113e-05}, {"epoch": 2, "train_loss": 0.7002905547618866, "eval_metrics": {"accuracy": 0.36619718309859156, "f1": 0.21052631578947367, "eval_loss": 0.7046781778335571}, "learning_rate": 0.0}], "submission_file": "E:\\spark文本分类\\results\\WNLI_submission.tsv", "status": "success"}, "MRPC": {"task": "MRPC", "status": "failed", "error": "num_samples should be a positive integer value, but got num_samples=0"}, "STS-B": {"task": "STS-B", "train_time": 134.85606718063354, "best_metric": 0.0, "training_history": [{"epoch": 1, "train_loss": 0.013916656890131057, "eval_metrics": {"pearson_correlation": NaN, "spearman_correlation": NaN, "combined_score": NaN, "eval_loss": 5.265076977497971e-05}, "learning_rate": 1.1058823529411766e-05}, {"epoch": 2, "train_loss": 0.0027119248914730517, "eval_metrics": {"pearson_correlation": NaN, "spearman_correlation": NaN, "combined_score": NaN, "eval_loss": 6.44603050635564e-05}, "learning_rate": 0.0}], "submission_file": "E:\\spark文本分类\\results\\STS-B_submission.tsv", "status": "success"}, "SST-2": {"task": "SST-2", "train_time": 135.8732786178589, "best_metric": 0.8916666666666667, "training_history": [{"epoch": 1, "train_loss": 0.4707089461227681, "eval_metrics": {"accuracy": 0.8766666666666667, "f1": 0.8832807570977917, "eval_loss": 0.3137287379879701}, "learning_rate": 1.1058823529411766e-05}, {"epoch": 2, "train_loss": 0.23834903244959546, "eval_metrics": {"accuracy": 0.8916666666666667, "f1": 0.8939641109298532, "eval_loss": 0.2772102634373464}, "learning_rate": 0.0}], "submission_file": "E:\\spark文本分类\\results\\SST-2_submission.tsv", "status": "success"}, "QNLI": {"task": "QNLI", "train_time": 136.46751356124878, "best_metric": 0.465, "training_history": [{"epoch": 1, "train_loss": 0.11408836784168205, "eval_metrics": {"accuracy": 0.465, "f1": 0.6348122866894198, "eval_loss": 3.380940161253277}, "learning_rate": 1.1058823529411766e-05}, {"epoch": 2, "train_loss": 0.011517999645451362, "eval_metrics": {"accuracy": 0.465, "f1": 0.6348122866894198, "eval_loss": 3.5197190109052157}, "learning_rate": 0.0}], "submission_file": "E:\\spark文本分类\\results\\QNLI_submission.tsv", "status": "success"}}