#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Spark版本兼容性问题
"""
import subprocess
import sys
import os

def print_header():
    """打印标题"""
    print("=" * 60)
    print("🔧 Spark版本兼容性修复工具")
    print("=" * 60)
    print("检测到PySpark 4.0.0与Spark 3.5.0版本不匹配")
    print("将安装匹配的PySpark版本")
    print("=" * 60)

def check_current_versions():
    """检查当前版本"""
    print("\n📋 当前版本检查")
    print("-" * 30)
    
    # 检查Spark版本
    spark_home = os.environ.get('SPARK_HOME')
    if spark_home:
        print(f"SPARK_HOME: {spark_home}")
        # 从路径推断Spark版本
        if "3.5.0" in spark_home:
            spark_version = "3.5.0"
        elif "3.4" in spark_home:
            spark_version = "3.4.x"
        elif "3.3" in spark_home:
            spark_version = "3.3.x"
        else:
            spark_version = "未知"
        print(f"Spark版本: {spark_version}")
    
    # 检查PySpark版本
    try:
        import pyspark
        pyspark_version = pyspark.__version__
        print(f"PySpark版本: {pyspark_version}")
        
        # 版本兼容性检查
        if spark_version == "3.5.0" and pyspark_version.startswith("4."):
            print("❌ 版本不匹配！PySpark 4.x 不兼容 Spark 3.5.0")
            return False, spark_version, pyspark_version
        else:
            print("✅ 版本可能兼容")
            return True, spark_version, pyspark_version
            
    except ImportError:
        print("❌ PySpark未安装")
        return False, spark_version, None

def fix_pyspark_version():
    """修复PySpark版本"""
    print("\n🔧 修复PySpark版本")
    print("-" * 30)
    
    try:
        # 卸载当前PySpark
        print("卸载当前PySpark...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "uninstall", "pyspark", "-y"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ PySpark卸载成功")
        else:
            print(f"⚠️ PySpark卸载警告: {result.stderr}")
        
        # 安装兼容版本的PySpark
        print("安装PySpark 3.5.0...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "pyspark==3.5.0"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ PySpark 3.5.0安装成功")
            return True
        else:
            print(f"❌ PySpark安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 修复过程失败: {e}")
        return False

def test_fixed_spark():
    """测试修复后的Spark"""
    print("\n🧪 测试修复后的Spark")
    print("-" * 30)
    
    try:
        # 重新导入PySpark
        import importlib
        if 'pyspark' in sys.modules:
            importlib.reload(sys.modules['pyspark'])
        
        from pyspark.sql import SparkSession
        
        print("创建测试Spark会话...")
        spark = SparkSession.builder \
            .appName("VersionFixTest") \
            .master("local[2]") \
            .config("spark.driver.memory", "1g") \
            .getOrCreate()
        
        print(f"✅ Spark会话创建成功！")
        print(f"   Spark版本: {spark.version}")
        print(f"   应用名称: {spark.sparkContext.appName}")
        
        # 简单测试
        data = [("Alice", 25), ("Bob", 30), ("Charlie", 35)]
        df = spark.createDataFrame(data, ["name", "age"])
        count = df.count()
        
        print(f"✅ DataFrame测试成功，行数: {count}")
        
        # 关闭会话
        spark.stop()
        print("✅ Spark会话正常关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_spark_test_simple():
    """创建简化的Spark测试脚本"""
    print("\n📝 创建简化测试脚本")
    print("-" * 30)
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Spark测试脚本
"""
from pyspark.sql import SparkSession
import time

def test_spark_basic():
    """基本Spark测试"""
    print("=== 简化Spark测试 ===")
    
    try:
        # 创建Spark会话
        print("创建Spark会话...")
        spark = SparkSession.builder \\
            .appName("SimpleSparkTest") \\
            .master("local[2]") \\
            .config("spark.driver.memory", "1g") \\
            .config("spark.executor.memory", "512m") \\
            .getOrCreate()
        
        print(f"✅ Spark版本: {spark.version}")
        
        # 创建测试数据
        print("创建测试数据...")
        data = [
            ("Alice", 25, "Engineer"),
            ("Bob", 30, "Manager"), 
            ("Charlie", 35, "Developer"),
            ("Diana", 28, "Analyst")
        ]
        
        df = spark.createDataFrame(data, ["name", "age", "job"])
        
        print(f"✅ DataFrame创建成功，行数: {df.count()}")
        
        # 基本操作
        print("执行基本操作...")
        df.show()
        
        # 过滤操作
        filtered_df = df.filter(df.age > 30)
        print(f"✅ 过滤操作成功，结果: {filtered_df.count()}行")
        
        # SQL操作
        df.createOrReplaceTempView("people")
        sql_result = spark.sql("SELECT job, AVG(age) as avg_age FROM people GROUP BY job")
        print("✅ SQL操作成功:")
        sql_result.show()
        
        # 关闭会话
        spark.stop()
        print("✅ 所有测试通过！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_spark_basic()
'''
    
    with open("test_spark_simple.py", "w", encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 简化测试脚本已创建: test_spark_simple.py")

def main():
    """主函数"""
    print_header()
    
    # 检查当前版本
    compatible, spark_ver, pyspark_ver = check_current_versions()
    
    if compatible:
        print("\n✅ 版本兼容，无需修复")
        return
    
    # 询问是否修复
    print(f"\n🤔 检测到版本不匹配:")
    print(f"   Spark: {spark_ver}")
    print(f"   PySpark: {pyspark_ver}")
    print(f"\n建议安装PySpark 3.5.0以匹配您的Spark 3.5.0")
    
    response = input("\n是否继续修复？(y/n): ").lower().strip()
    
    if response != 'y':
        print("取消修复")
        return
    
    # 执行修复
    if fix_pyspark_version():
        print("\n🎉 版本修复完成！")
        
        # 测试修复结果
        if test_fixed_spark():
            print("\n✅ Spark环境修复成功！")
            
            # 创建简化测试脚本
            create_spark_test_simple()
            
            print("\n📋 下一步操作:")
            print("1. 运行简化测试: python test_spark_simple.py")
            print("2. 运行完整测试: python test_spark_complete.py")
            print("3. 运行GLUE训练: python run_pandas_glue.py --mode demo")
            
        else:
            print("\n❌ 修复后测试仍然失败")
    else:
        print("\n❌ 版本修复失败")

if __name__ == "__main__":
    main()
