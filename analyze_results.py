"""
分析GLUE基准测试结果并生成对比报告
"""
import json
import os

def load_results():
    """加载测试结果"""
    results_file = "results/glue_benchmark_results.json"
    if os.path.exists(results_file):
        with open(results_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}

def extract_our_metrics(results):
    """提取我们的评估指标"""
    metrics = {}
    
    for task, result in results.items():
        if result.get('status') == 'success' and 'training_history' in result:
            history = result['training_history']
            if history:
                # 取最后一个epoch的结果
                last_epoch = history[-1]
                eval_metrics = last_epoch.get('eval_metrics', {})
                
                if task == "CoLA":
                    metrics[task] = {
                        'Matthews_corr': eval_metrics.get('matthews_corrcoef', 0.0) * 100,
                        'Accuracy': eval_metrics.get('accuracy', 0.0) * 100
                    }
                elif task == "SST-2":
                    metrics[task] = {
                        'Accuracy': eval_metrics.get('accuracy', 0.0) * 100
                    }
                elif task == "MRPC":
                    metrics[task] = {
                        'F1': eval_metrics.get('f1', 0.0) * 100,
                        'Accuracy': eval_metrics.get('accuracy', 0.0) * 100
                    }
                elif task == "STS-B":
                    pearson = eval_metrics.get('pearson_correlation', 0.0)
                    spearman = eval_metrics.get('spearman_correlation', 0.0)
                    # 处理NaN值
                    if str(pearson) == 'NaN':
                        pearson = 0.0
                    if str(spearman) == 'NaN':
                        spearman = 0.0
                    metrics[task] = {
                        'Pearson_corr': pearson * 100,
                        'Spearman_corr': spearman * 100
                    }
                elif task == "QQP":
                    metrics[task] = {
                        'Accuracy': eval_metrics.get('accuracy', 0.0) * 100,
                        'F1': eval_metrics.get('f1', 0.0) * 100
                    }
                elif task == "MNLI":
                    metrics[task] = {
                        'Matched_acc': eval_metrics.get('accuracy', 0.0) * 100,
                        'Mismatched_acc': eval_metrics.get('accuracy', 0.0) * 100
                    }
                elif task in ["QNLI", "RTE", "WNLI"]:
                    metrics[task] = {
                        'Accuracy': eval_metrics.get('accuracy', 0.0) * 100
                    }
    
    return metrics

def create_comparison_report():
    """创建详细的对比报告"""
    
    # BERT-base基准结果
    bert_base_results = {
        "CoLA": {"Matthews_corr": 57.11},
        "SST-2": {"Accuracy": 92.66},
        "MRPC": {"F1": 89.00, "Accuracy": 84.64},
        "STS-B": {"Pearson_corr": 89.13, "Spearman_corr": 88.54},
        "QQP": {"Accuracy": 90.84, "F1": 87.70},
        "MNLI": {"Matched_acc": 83.27, "Mismatched_acc": 83.65},
        "QNLI": {"Accuracy": 91.47},
        "RTE": {"Accuracy": 72.20},
        "WNLI": {"Accuracy": 56.34}
    }
    
    # 加载我们的结果
    results = load_results()
    our_metrics = extract_our_metrics(results)
    
    print("🎯 GLUE基准测试结果对比分析")
    print("=" * 80)
    
    print(f"\n📊 测试概况:")
    print(f"- 成功完成任务: {len([r for r in results.values() if r.get('status') == 'success'])}")
    print(f"- 失败任务: {len([r for r in results.values() if r.get('status') == 'failed'])}")
    print(f"- 总任务数: {len(results)}")
    
    print(f"\n📈 详细结果对比:")
    print("-" * 80)
    
    # 表格标题
    print(f"{'任务':<8} {'指标':<15} {'BERT-base':<12} {'我们的结果':<12} {'差异':<10} {'状态':<8}")
    print("-" * 80)
    
    total_comparisons = 0
    better_count = 0
    close_count = 0
    worse_count = 0
    
    for task in ["CoLA", "SST-2", "MRPC", "STS-B", "QQP", "MNLI", "QNLI", "RTE", "WNLI"]:
        if task in our_metrics and task in bert_base_results:
            our_task_metrics = our_metrics[task]
            bert_task_metrics = bert_base_results[task]
            
            for metric_name in bert_task_metrics:
                if metric_name in our_task_metrics:
                    bert_value = bert_task_metrics[metric_name]
                    our_value = our_task_metrics[metric_name]
                    diff = our_value - bert_value
                    
                    if diff > 2:
                        status = "🟢 更好"
                        better_count += 1
                    elif diff > -5:
                        status = "🟡 接近"
                        close_count += 1
                    else:
                        status = "🔴 较差"
                        worse_count += 1
                    
                    print(f"{task:<8} {metric_name:<15} {bert_value:<12.2f} {our_value:<12.2f} {diff:+<10.2f} {status:<8}")
                    total_comparisons += 1
        else:
            if task in results:
                if results[task].get('status') == 'failed':
                    print(f"{task:<8} {'N/A':<15} {'N/A':<12} {'失败':<12} {'N/A':<10} {'❌ 错误':<8}")
                else:
                    print(f"{task:<8} {'N/A':<15} {'N/A':<12} {'未测试':<12} {'N/A':<10} {'⚪ 跳过':<8}")
            else:
                print(f"{task:<8} {'N/A':<15} {'N/A':<12} {'未运行':<12} {'N/A':<10} {'⚫ 未测':<8}")
    
    print("-" * 80)
    
    # 总结分析
    print(f"\n📋 性能总结:")
    print(f"- 🟢 超越基准: {better_count}/{total_comparisons} ({better_count/total_comparisons*100:.1f}%)")
    print(f"- 🟡 接近基准: {close_count}/{total_comparisons} ({close_count/total_comparisons*100:.1f}%)")
    print(f"- 🔴 低于基准: {worse_count}/{total_comparisons} ({worse_count/total_comparisons*100:.1f}%)")
    
    # 具体任务分析
    print(f"\n🔍 任务分析:")
    
    if "CoLA" in our_metrics:
        cola_matthews = our_metrics["CoLA"]["Matthews_corr"]
        print(f"- CoLA (语法判断): Matthews相关系数 {cola_matthews:.2f}% vs 基准 57.11%")
        if cola_matthews > 40:
            print("  ✅ 表现良好，语法理解能力较强")
        else:
            print("  ⚠️ 需要改进，可能需要更多训练数据或调整超参数")
    
    if "SST-2" in our_metrics:
        sst2_acc = our_metrics["SST-2"]["Accuracy"]
        print(f"- SST-2 (情感分析): 准确率 {sst2_acc:.2f}% vs 基准 92.66%")
        if sst2_acc > 85:
            print("  ✅ 情感分析能力优秀")
        else:
            print("  ⚠️ 情感分析有待提升")
    
    if "RTE" in our_metrics:
        rte_acc = our_metrics["RTE"]["Accuracy"]
        print(f"- RTE (文本蕴含): 准确率 {rte_acc:.2f}% vs 基准 72.20%")
        if rte_acc > 60:
            print("  ✅ 逻辑推理能力不错")
        else:
            print("  ⚠️ 逻辑推理需要加强")
    
    # 训练效率分析
    print(f"\n⏱️ 训练效率:")
    total_time = 0
    for task, result in results.items():
        if result.get('status') == 'success' and 'train_time' in result:
            train_time = result['train_time']
            total_time += train_time
            print(f"- {task}: {train_time:.1f}秒")
    
    print(f"- 总训练时间: {total_time:.1f}秒 ({total_time/60:.1f}分钟)")
    
    # 改进建议
    print(f"\n💡 改进建议:")
    print("1. 增加训练轮数 (epochs) 以获得更好的收敛")
    print("2. 调整学习率和批次大小优化训练效果")
    print("3. 使用更大的训练样本数量")
    print("4. 考虑使用BERT-large或其他更大的预训练模型")
    print("5. 针对表现较差的任务进行特定优化")
    
    # 提交指南
    print(f"\n🌐 GLUE官网提交:")
    print("1. 检查生成的提交文件:")
    for task in our_metrics.keys():
        submission_file = f"results/{task}_submission.tsv"
        if os.path.exists(submission_file):
            print(f"   ✅ {submission_file}")
        else:
            print(f"   ❌ {submission_file} (缺失)")
    
    print("2. 访问 https://gluebenchmark.com/")
    print("3. 创建账户并上传TSV文件")
    print("4. 查看官方排名和详细分析")
    
    return our_metrics, bert_base_results

def main():
    """主函数"""
    our_metrics, bert_base = create_comparison_report()
    
    # 保存分析结果
    analysis_result = {
        "our_results": our_metrics,
        "bert_base_benchmark": bert_base,
        "analysis_date": "2025-07-27",
        "model_used": "bert-base-uncased",
        "training_epochs": 2,
        "max_samples": 3000
    }
    
    with open("results/performance_analysis.json", 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, indent=2, ensure_ascii=False)
    
    print(f"\n📁 分析结果已保存到: results/performance_analysis.json")

if __name__ == "__main__":
    main()
