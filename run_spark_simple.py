#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Spark GLUE启动脚本 - 避开TensorFlow冲突
"""
import sys
import os
import argparse
from datetime import datetime

def print_header():
    """打印标题"""
    print("=" * 60)
    print("🚀 Spark GLUE 简化版训练系统")
    print("=" * 60)
    print("避开TensorFlow冲突，使用纯PyTorch + Spark")
    print("支持SST-2情感分析和RTE文本蕴含任务")
    print("=" * 60)
    
    # 显示环境信息
    try:
        import torch
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"GPU设备: {torch.cuda.get_device_name(0)}")
            print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
        
        import pyspark
        print(f"PySpark版本: {pyspark.__version__}")
        
        import pandas as pd
        print(f"Pandas版本: {pd.__version__}")
        
        import numpy as np
        print(f"NumPy版本: {np.__version__}")
        
    except ImportError as e:
        print(f"环境检查失败: {e}")
    
    print("=" * 60)

def run_single_task(task_name="SST-2", num_samples=1000):
    """运行单任务训练"""
    print(f"\n🎯 运行单任务训练: {task_name}")
    print(f"样本数量: {num_samples}")
    
    try:
        from spark_glue_simple import run_spark_glue_simple
        
        # 运行训练
        results = run_spark_glue_simple(task_name, num_samples)
        
        print(f"\n✅ {task_name} 训练成功!")
        return results
        
    except Exception as e:
        print(f"❌ 单任务训练失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_demo():
    """运行演示"""
    print("\n🎪 运行Spark GLUE简化版演示")
    
    tasks = [
        ("SST-2", 500, "情感分析"),
        ("RTE", 400, "文本蕴含")
    ]
    
    results_summary = {}
    
    for task_name, samples, description in tasks:
        print(f"\n--- 演示任务: {task_name} ({description}) ---")
        
        result = run_single_task(task_name, samples)
        
        if result:
            results_summary[task_name] = {
                "accuracy": result["accuracy"],
                "f1": result["f1"],
                "training_time": result["training_time"],
                "status": "success"
            }
            print(f"✅ {task_name} 演示成功")
        else:
            results_summary[task_name] = {"status": "failed"}
            print(f"❌ {task_name} 演示失败")
    
    # 保存演示结果
    import json
    os.makedirs("results", exist_ok=True)
    with open("results/demo_results.json", "w") as f:
        json.dump(results_summary, f, indent=2)
    
    # 显示总结
    print("\n" + "=" * 60)
    print("🎪 演示完成!")
    print("\n演示结果总结:")
    
    for task, result in results_summary.items():
        if result["status"] == "success":
            print(f"  {task:6s}: 准确率={result['accuracy']:.4f}, "
                  f"F1={result['f1']:.4f}, 时间={result['training_time']:.1f}s")
        else:
            print(f"  {task:6s}: 失败")
    
    print(f"\n详细结果保存在: results/demo_results.json")

def run_benchmark():
    """运行基准测试"""
    print("\n🏆 运行简化版基准测试")
    
    tasks = [
        ("SST-2", 1500, "情感分析"),
        ("RTE", 1000, "文本蕴含")
    ]
    
    results_summary = {}
    
    for task_name, samples, description in tasks:
        print(f"\n--- 基准测试: {task_name} ({description}) ---")
        
        result = run_single_task(task_name, samples)
        
        if result:
            results_summary[task_name] = {
                "accuracy": result["accuracy"],
                "f1": result["f1"],
                "training_time": result["training_time"],
                "samples": samples,
                "status": "success"
            }
        else:
            results_summary[task_name] = {"status": "failed"}
    
    # 保存基准测试结果
    import json
    os.makedirs("results", exist_ok=True)
    with open("results/benchmark_simple_results.json", "w") as f:
        json.dump(results_summary, f, indent=2)
    
    # 显示总结
    print("\n" + "=" * 60)
    print("🏆 基准测试完成!")
    print("\n基准测试结果:")
    
    for task, result in results_summary.items():
        if result["status"] == "success":
            print(f"  {task:6s}: 准确率={result['accuracy']:.4f}, "
                  f"F1={result['f1']:.4f}, 时间={result['training_time']:.1f}s, "
                  f"样本={result['samples']}")
        else:
            print(f"  {task:6s}: 失败")
    
    print(f"\n详细结果保存在: results/benchmark_simple_results.json")

def show_results():
    """显示历史结果"""
    print("\n📊 查看训练结果")
    
    results_dir = "results"
    if not os.path.exists(results_dir):
        print("⚠️ 结果目录不存在")
        return
    
    result_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
    
    if not result_files:
        print("⚠️ 没有找到结果文件")
        return
    
    print(f"📁 找到 {len(result_files)} 个结果文件:")
    
    for file in result_files:
        file_path = os.path.join(results_dir, file)
        try:
            import json
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            print(f"\n📄 {file}:")
            
            if "task_name" in data:
                # 单任务结果
                print(f"   任务: {data['task_name']}")
                print(f"   准确率: {data.get('accuracy', 'N/A'):.4f}")
                print(f"   F1分数: {data.get('f1', 'N/A'):.4f}")
                print(f"   训练时间: {data.get('training_time', 'N/A'):.1f}秒")
            else:
                # 多任务结果
                for task, result in data.items():
                    if isinstance(result, dict) and "accuracy" in result:
                        print(f"   {task}: 准确率={result['accuracy']:.4f}")
            
        except Exception as e:
            print(f"   ❌ 读取失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Spark GLUE简化版训练系统")
    parser.add_argument("--mode", choices=["single", "demo", "benchmark", "results"], 
                       default="demo", help="运行模式")
    parser.add_argument("--task", default="SST-2", 
                       help="单任务模式的任务名称 (SST-2 或 RTE)")
    parser.add_argument("--samples", type=int, default=1000, 
                       help="样本数量")
    
    args = parser.parse_args()
    
    # 打印标题
    print_header()
    
    # 记录开始时间
    start_time = datetime.now()
    
    try:
        if args.mode == "single":
            run_single_task(args.task, args.samples)
        
        elif args.mode == "demo":
            run_demo()
        
        elif args.mode == "benchmark":
            run_benchmark()
        
        elif args.mode == "results":
            show_results()
        
        else:
            print("❌ 未知的运行模式")
            return
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断训练")
    
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 显示总用时
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        print(f"\n⏱️ 总用时: {total_time:.1f}秒")
        print("=" * 60)

if __name__ == "__main__":
    main()
