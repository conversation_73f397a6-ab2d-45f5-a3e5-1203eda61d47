
import sys
import os

def test_basic_imports():
    """测试基本导入"""
    print("测试基本导入...")
    
    try:
        import numpy as np
        print(f"NumPy版本: {np.__version__}")
        
        import pandas as pd
        print(f"Pandas版本: {pd.__version__}")
        
        import torch
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"GPU: {torch.cuda.get_device_name(0)}")
        
        return True
    except Exception as e:
        print(f"导入失败: {e}")
        return False

def test_simple_model():
    """测试简单模型"""
    print("测试简单模型...")
    
    try:
        import torch
        import torch.nn as nn
        
        # 创建简单模型
        model = nn.Linear(10, 2)
        x = torch.randn(5, 10)
        y = model(x)
        
        print(f"模型测试成功，输出形状: {y.shape}")
        return True
    except Exception as e:
        print(f"模型测试失败: {e}")
        return False

def test_transformers():
    """测试Transformers"""
    print("测试Transformers...")
    
    try:
        from transformers import AutoTokenizer
        
        # 测试tokenizer
        tokenizer = AutoTokenizer.from_pretrained("distilbert-base-uncased")
        text = "Hello world"
        tokens = tokenizer(text, return_tensors="pt")
        
        print(f"Tokenizer测试成功，token数量: {len(tokens['input_ids'][0])}")
        return True
    except Exception as e:
        print(f"Transformers测试失败: {e}")
        return False

def main():
    print("=== 最小化环境测试 ===")
    
    tests = [
        ("基本导入", test_basic_imports),
        ("简单模型", test_simple_model),
        ("Transformers", test_transformers)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"{test_name}测试出错: {e}")
            results[test_name] = False
    
    print("\n=== 测试结果 ===")
    for test_name, passed in results.items():
        status = "通过" if passed else "失败"
        print(f"{test_name}: {status}")
    
    if all(results.values()):
        print("\n所有测试通过！环境正常")
        return True
    else:
        print("\n部分测试失败，需要修复环境")
        return False

if __name__ == "__main__":
    main()
