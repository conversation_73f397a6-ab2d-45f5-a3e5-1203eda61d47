"""
GLUE基准测试运行脚本
专门用于运行完整的GLUE基准测试并生成性能报告
"""
import os
import sys
import json
import time
from typing import Dict, Any

# 设置环境变量
os.environ['USE_TF'] = 'NO'
os.environ['USE_TORCH'] = 'YES'

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from main import GLUEBenchmark
from utils import setup_logging, set_seed
import config

def run_single_task_benchmark(task: str, epochs: int = 2, max_samples: int = 2000) -> Dict[str, Any]:
    """运行单个任务的基准测试"""
    print(f"\n{'='*60}")
    print(f"运行GLUE基准测试: {task}")
    print(f"{'='*60}")
    
    benchmark = GLUEBenchmark()
    
    try:
        # 训练
        print(f"🚀 开始训练 {task}...")
        start_time = time.time()
        
        train_results = benchmark.train_task(
            task=task,
            epochs=epochs,
            max_samples=max_samples
        )
        
        train_time = time.time() - start_time
        
        # 预测
        print(f"🔮 开始预测 {task}...")
        submission_path = benchmark.predict_task(task)
        
        # 收集结果
        results = {
            'task': task,
            'train_time': train_time,
            'best_metric': train_results['best_metric'],
            'training_history': train_results['training_history'],
            'submission_file': submission_path,
            'status': 'success'
        }
        
        print(f"✅ {task} 完成!")
        print(f"   最佳指标: {train_results['best_metric']:.4f}")
        print(f"   训练时间: {train_time:.2f}秒")
        
        return results
        
    except Exception as e:
        print(f"❌ {task} 失败: {e}")
        return {
            'task': task,
            'status': 'failed',
            'error': str(e)
        }
    
    finally:
        benchmark.cleanup()

def extract_metrics_from_results(results: Dict[str, Any]) -> Dict[str, float]:
    """从训练结果中提取评估指标"""
    metrics = {}
    
    for task, result in results.items():
        if result.get('status') == 'success' and 'training_history' in result:
            history = result['training_history']
            if history:
                last_epoch = history[-1]
                eval_metrics = last_epoch.get('eval_metrics', {})
                
                # 根据任务类型提取主要指标
                if task == "CoLA":
                    metrics[task] = {
                        'Matthews_corr': eval_metrics.get('matthews_corrcoef', 0.0),
                        'Accuracy': eval_metrics.get('accuracy', 0.0)
                    }
                elif task == "SST-2":
                    metrics[task] = {
                        'Accuracy': eval_metrics.get('accuracy', 0.0)
                    }
                elif task == "MRPC":
                    metrics[task] = {
                        'F1': eval_metrics.get('f1', 0.0),
                        'Accuracy': eval_metrics.get('accuracy', 0.0)
                    }
                elif task == "STS-B":
                    metrics[task] = {
                        'Pearson_corr': eval_metrics.get('pearson_correlation', 0.0),
                        'Spearman_corr': eval_metrics.get('spearman_correlation', 0.0)
                    }
                elif task == "QQP":
                    metrics[task] = {
                        'Accuracy': eval_metrics.get('accuracy', 0.0),
                        'F1': eval_metrics.get('f1', 0.0)
                    }
                elif task == "MNLI":
                    metrics[task] = {
                        'Matched_acc': eval_metrics.get('accuracy', 0.0),
                        'Mismatched_acc': eval_metrics.get('accuracy', 0.0)  # 简化处理
                    }
                elif task in ["QNLI", "RTE", "WNLI"]:
                    metrics[task] = {
                        'Accuracy': eval_metrics.get('accuracy', 0.0)
                    }
    
    return metrics

def create_comparison_report(our_metrics: Dict[str, Dict[str, float]]) -> str:
    """创建与基准结果的对比报告"""
    
    # BERT-base基准结果
    bert_base_results = {
        "CoLA": {"Matthews_corr": 57.11},
        "SST-2": {"Accuracy": 92.66},
        "MRPC": {"F1": 89.00, "Accuracy": 84.64},
        "STS-B": {"Pearson_corr": 89.13, "Spearman_corr": 88.54},
        "QQP": {"Accuracy": 90.84, "F1": 87.70},
        "MNLI": {"Matched_acc": 83.27, "Mismatched_acc": 83.65},
        "QNLI": {"Accuracy": 91.47},
        "RTE": {"Accuracy": 72.20},
        "WNLI": {"Accuracy": 56.34}
    }
    
    report = []
    report.append("# GLUE基准测试结果对比报告\n")
    report.append("## 我们的结果 vs BERT-base基准\n")
    report.append("| 任务 | 指标 | BERT-base基准 | 我们的结果 | 差异 | 状态 |\n")
    report.append("|------|------|---------------|------------|------|------|\n")
    
    total_tasks = 0
    better_tasks = 0
    
    for task in config.GLUE_TASKS:
        if task in our_metrics and task in bert_base_results:
            our_task_metrics = our_metrics[task]
            bert_task_metrics = bert_base_results[task]
            
            for metric_name in bert_task_metrics:
                if metric_name in our_task_metrics:
                    bert_value = bert_task_metrics[metric_name]
                    our_value = our_task_metrics[metric_name] * 100  # 转换为百分比
                    diff = our_value - bert_value
                    status = "🟢 更好" if diff > 0 else "🔴 较差" if diff < -5 else "🟡 接近"
                    
                    report.append(f"| {task} | {metric_name} | {bert_value:.2f} | {our_value:.2f} | {diff:+.2f} | {status} |\n")
                    
                    total_tasks += 1
                    if diff > 0:
                        better_tasks += 1
        else:
            report.append(f"| {task} | - | - | 未测试 | - | ❌ 失败 |\n")
    
    report.append(f"\n## 总结\n")
    report.append(f"- 成功测试任务: {len(our_metrics)}/{len(config.GLUE_TASKS)}\n")
    report.append(f"- 超越基准指标: {better_tasks}/{total_tasks}\n")
    report.append(f"- 整体表现: {'优秀' if better_tasks/total_tasks > 0.5 else '良好' if better_tasks/total_tasks > 0.3 else '需要改进'}\n")
    
    return "".join(report)

def main():
    """主函数"""
    print("🚀 开始GLUE基准测试")
    print("=" * 80)
    
    # 设置日志
    logger = setup_logging(config.LOG_DIR, "INFO")
    set_seed(config.RANDOM_SEED)
    
    # 测试参数
    epochs = 2  # 使用2个epoch进行更好的训练
    max_samples = 3000  # 使用更多样本
    
    print(f"测试参数:")
    print(f"- 训练轮数: {epochs}")
    print(f"- 最大样本数: {max_samples}")
    print(f"- 模型: {config.MODEL_NAME}")
    
    # 运行所有任务
    all_results = {}
    
    # 优先测试较小的任务
    task_order = ["CoLA", "RTE", "WNLI", "MRPC", "STS-B", "SST-2", "QNLI", "QQP", "MNLI"]
    
    for task in task_order:
        try:
            result = run_single_task_benchmark(task, epochs, max_samples)
            all_results[task] = result
            
            # 保存中间结果
            with open(os.path.join(config.RESULTS_DIR, "glue_benchmark_results.json"), 'w', encoding='utf-8') as f:
                json.dump(all_results, f, indent=2, ensure_ascii=False, default=str)
            
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断测试")
            break
        except Exception as e:
            print(f"❌ 任务 {task} 出现异常: {e}")
            all_results[task] = {'status': 'failed', 'error': str(e)}
    
    # 提取指标
    print(f"\n{'='*80}")
    print("📊 提取评估指标...")
    metrics = extract_metrics_from_results(all_results)
    
    # 生成对比报告
    print("📝 生成对比报告...")
    comparison_report = create_comparison_report(metrics)
    
    # 保存报告
    report_path = os.path.join(config.RESULTS_DIR, "glue_benchmark_comparison.md")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(comparison_report)
    
    # 显示结果
    print(f"\n{'='*80}")
    print("🎯 GLUE基准测试完成!")
    print(f"{'='*80}")
    
    print(comparison_report)
    
    print(f"\n📁 详细结果文件:")
    print(f"- 完整结果: {os.path.join(config.RESULTS_DIR, 'glue_benchmark_results.json')}")
    print(f"- 对比报告: {report_path}")
    print(f"- 提交文件: {config.RESULTS_DIR}/*_submission.tsv")
    
    print(f"\n🌐 下一步:")
    print(f"1. 查看详细的对比报告")
    print(f"2. 访问 https://gluebenchmark.com/ 提交结果")
    print(f"3. 分析性能差异并优化模型")

if __name__ == "__main__":
    main()
