#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Spark GLUE训练器 - 结合Spark数据处理和PyTorch深度学习
"""
import os
import json
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer, AutoModelForSequenceClassification, Trainer, TrainingArguments
from sklearn.metrics import accuracy_score, f1_score
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, when, lit, length, regexp_replace
from pyspark.sql.types import StructType, StructField, StringType, IntegerType
import numpy as np
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SparkGLUEDataProcessor:
    """Spark GLUE数据处理器"""
    
    def __init__(self):
        self.spark = self._create_spark_session()
        
    def _create_spark_session(self):
        """创建Spark会话"""
        logger.info("创建Spark会话...")
        
        spark = SparkSession.builder \
            .appName("GLUE_Text_Classification") \
            .master("local[*]") \
            .config("spark.driver.memory", "4g") \
            .config("spark.executor.memory", "2g") \
            .config("spark.sql.adaptive.enabled", "true") \
            .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
            .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
            .getOrCreate()
        
        spark.sparkContext.setLogLevel("WARN")
        logger.info(f"Spark会话创建成功: {spark.version}")
        return spark
    
    def create_glue_datasets(self, task_name="SST-2", num_samples=1000):
        """使用Spark创建GLUE数据集"""
        logger.info(f"使用Spark创建{task_name}数据集...")
        
        if task_name == "SST-2":
            # 情感分析数据
            positive_sentences = [
                "This movie is absolutely fantastic and amazing!",
                "I love this film so much, it's incredible!",
                "Outstanding performance and brilliant story!",
                "Excellent cinematography and great acting!",
                "One of the best movies I have ever seen!",
                "Wonderful direction and superb screenplay!",
                "Brilliant acting and engaging plot!",
                "Amazing visual effects and soundtrack!",
                "Perfect casting and excellent execution!",
                "Incredible story and fantastic performances!"
            ]
            
            negative_sentences = [
                "This movie is terrible and completely boring.",
                "I hate this film, it's a waste of time.",
                "Awful acting and terrible storyline here.",
                "Poor direction and bad script writing.",
                "One of the worst films ever made.",
                "Terrible plot and horrible acting.",
                "Boring story and poor execution.",
                "Bad cinematography and weak dialogue.",
                "Disappointing film with no redeeming qualities.",
                "Completely unwatchable and poorly made."
            ]
            
            # 创建数据
            data = []
            for i in range(num_samples):
                if i % 2 == 0:
                    sentence = positive_sentences[i % len(positive_sentences)]
                    label = 1
                else:
                    sentence = negative_sentences[i % len(negative_sentences)]
                    label = 0
                data.append((sentence, label))
            
            # 创建Spark DataFrame
            schema = StructType([
                StructField("sentence", StringType(), True),
                StructField("label", IntegerType(), True)
            ])
            
            df = self.spark.createDataFrame(data, schema)
            
        elif task_name == "RTE":
            # 文本蕴含数据
            premise_hypothesis_pairs = [
                ("The cat is sleeping on the sofa.", "There is a cat on the furniture.", 1),
                ("John went to the grocery store.", "John is shopping for food.", 1),
                ("The weather is sunny today.", "It's raining outside.", 0),
                ("She is reading a science book.", "She is studying science.", 1),
                ("The car is painted red.", "The car is blue in color.", 0),
                ("The dog is barking loudly.", "There is a dog making noise.", 1),
                ("Students are taking an exam.", "Students are playing games.", 0),
                ("The restaurant serves Italian food.", "You can eat pasta there.", 1),
                ("The movie starts at 8 PM.", "The movie ends at 8 PM.", 0),
                ("She works as a teacher.", "She educates students.", 1)
            ]
            
            data = []
            for i in range(num_samples):
                premise, hypothesis, label = premise_hypothesis_pairs[i % len(premise_hypothesis_pairs)]
                data.append((i, premise, hypothesis, label))
            
            schema = StructType([
                StructField("index", IntegerType(), True),
                StructField("sentence1", StringType(), True),
                StructField("sentence2", StringType(), True),
                StructField("label", IntegerType(), True)
            ])
            
            df = self.spark.createDataFrame(data, schema)
        
        return df
    
    def preprocess_with_spark(self, df, task_name):
        """使用Spark进行数据预处理"""
        logger.info(f"使用Spark预处理{task_name}数据...")
        
        if task_name == "SST-2":
            # 文本清理和预处理
            processed_df = df.select(
                regexp_replace(col("sentence"), "[^a-zA-Z0-9\\s]", "").alias("text"),
                col("label").alias("labels")
            ).filter(length(col("text")) > 5)  # 过滤太短的文本
            
        elif task_name == "RTE":
            # 组合两个句子
            processed_df = df.select(
                col("index"),
                regexp_replace(
                    concat(col("sentence1"), lit(" [SEP] "), col("sentence2")),
                    "[^a-zA-Z0-9\\s\\[\\]]", ""
                ).alias("text"),
                col("label").alias("labels")
            ).filter(length(col("text")) > 10)
        
        # 添加文本长度统计
        processed_df = processed_df.withColumn("text_length", length(col("text")))
        
        # 显示统计信息
        total_count = processed_df.count()
        avg_length = processed_df.agg({"text_length": "avg"}).collect()[0][0]
        
        logger.info(f"预处理完成: {total_count}条记录，平均长度: {avg_length:.1f}")
        
        return processed_df
    
    def split_data_with_spark(self, df, train_ratio=0.8):
        """使用Spark分割数据"""
        logger.info("使用Spark分割训练和验证数据...")
        
        # 随机分割数据
        train_df, dev_df = df.randomSplit([train_ratio, 1-train_ratio], seed=42)
        
        train_count = train_df.count()
        dev_count = dev_df.count()
        
        logger.info(f"数据分割完成: 训练集{train_count}条，验证集{dev_count}条")
        
        return train_df, dev_df
    
    def convert_to_pandas(self, spark_df):
        """将Spark DataFrame转换为Pandas"""
        return spark_df.toPandas()
    
    def close(self):
        """关闭Spark会话"""
        if self.spark:
            self.spark.stop()
            logger.info("Spark会话已关闭")

class PyTorchGLUETrainer:
    """PyTorch GLUE训练器"""
    
    def __init__(self, model_name="distilbert-base-uncased"):
        self.model_name = model_name
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        
        logger.info(f"初始化训练器: {model_name}")
        logger.info(f"使用设备: {self.device}")
    
    class GLUEDataset(Dataset):
        """GLUE数据集类"""
        
        def __init__(self, texts, labels, tokenizer, max_length=128):
            self.texts = texts
            self.labels = labels
            self.tokenizer = tokenizer
            self.max_length = max_length
        
        def __len__(self):
            return len(self.texts)
        
        def __getitem__(self, idx):
            text = str(self.texts[idx])
            label = self.labels[idx]
            
            encoding = self.tokenizer(
                text,
                truncation=True,
                padding='max_length',
                max_length=self.max_length,
                return_tensors='pt'
            )
            
            return {
                'input_ids': encoding['input_ids'].flatten(),
                'attention_mask': encoding['attention_mask'].flatten(),
                'labels': torch.tensor(label, dtype=torch.long)
            }
    
    def prepare_datasets(self, train_df, dev_df):
        """准备训练数据集"""
        logger.info("准备PyTorch数据集...")
        
        train_dataset = self.GLUEDataset(
            train_df['text'].tolist(),
            train_df['labels'].tolist(),
            self.tokenizer
        )
        
        dev_dataset = self.GLUEDataset(
            dev_df['text'].tolist(),
            dev_df['labels'].tolist(),
            self.tokenizer
        )
        
        logger.info(f"数据集准备完成: 训练{len(train_dataset)}条，验证{len(dev_dataset)}条")
        
        return train_dataset, dev_dataset
    
    def train_model(self, train_dataset, dev_dataset, output_dir="results/spark_glue"):
        """训练模型"""
        logger.info("开始训练模型...")
        
        # 创建模型
        model = AutoModelForSequenceClassification.from_pretrained(
            self.model_name,
            num_labels=2
        ).to(self.device)
        
        # 训练参数
        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=3,
            per_device_train_batch_size=8,
            per_device_eval_batch_size=8,
            warmup_steps=100,
            weight_decay=0.01,
            learning_rate=2e-5,
            logging_dir=f'{output_dir}/logs',
            logging_steps=50,
            evaluation_strategy="epoch",
            save_strategy="epoch",
            save_total_limit=2,
            load_best_model_at_end=True,
            report_to=None,
            fp16=True if self.device.type == 'cuda' else False,
        )
        
        # 创建训练器
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=dev_dataset,
            compute_metrics=self.compute_metrics,
        )
        
        # 开始训练
        start_time = datetime.now()
        train_result = trainer.train()
        end_time = datetime.now()
        
        training_time = (end_time - start_time).total_seconds()
        logger.info(f"训练完成，用时: {training_time:.1f}秒")
        
        # 评估
        eval_results = trainer.evaluate()
        
        # 保存模型
        trainer.save_model(f"{output_dir}/model")
        
        return {
            "train_results": train_result.metrics,
            "eval_results": eval_results,
            "training_time": training_time,
            "model_path": f"{output_dir}/model"
        }
    
    def compute_metrics(self, eval_pred):
        """计算评估指标"""
        predictions, labels = eval_pred
        predictions = np.argmax(predictions, axis=1)
        
        return {
            "accuracy": accuracy_score(labels, predictions),
            "f1": f1_score(labels, predictions, average='weighted')
        }

class SparkGLUEPipeline:
    """完整的Spark GLUE训练流水线"""
    
    def __init__(self, task_name="SST-2", model_name="distilbert-base-uncased"):
        self.task_name = task_name
        self.model_name = model_name
        self.data_processor = SparkGLUEDataProcessor()
        self.trainer = PyTorchGLUETrainer(model_name)
        
    def run_full_pipeline(self, num_samples=1000):
        """运行完整的训练流水线"""
        logger.info(f"开始Spark GLUE训练流水线: {self.task_name}")
        
        try:
            # 1. 使用Spark创建数据
            spark_df = self.data_processor.create_glue_datasets(self.task_name, num_samples)
            
            # 2. 使用Spark预处理数据
            processed_df = self.data_processor.preprocess_with_spark(spark_df, self.task_name)
            
            # 3. 使用Spark分割数据
            train_spark_df, dev_spark_df = self.data_processor.split_data_with_spark(processed_df)
            
            # 4. 转换为Pandas（用于PyTorch）
            train_df = self.data_processor.convert_to_pandas(train_spark_df)
            dev_df = self.data_processor.convert_to_pandas(dev_spark_df)
            
            # 5. 准备PyTorch数据集
            train_dataset, dev_dataset = self.trainer.prepare_datasets(train_df, dev_df)
            
            # 6. 训练模型
            results = self.trainer.train_model(train_dataset, dev_dataset)
            
            # 7. 保存完整结果
            final_results = {
                "task_name": self.task_name,
                "model_name": self.model_name,
                "timestamp": datetime.now().isoformat(),
                "data_info": {
                    "total_samples": num_samples,
                    "train_samples": len(train_df),
                    "dev_samples": len(dev_df)
                },
                "spark_version": self.data_processor.spark.version,
                "device": str(self.trainer.device),
                **results
            }
            
            # 保存结果
            os.makedirs("results", exist_ok=True)
            with open(f"results/spark_glue_{self.task_name}_results.json", "w") as f:
                json.dump(final_results, f, indent=2)
            
            logger.info("Spark GLUE训练流水线完成！")
            return final_results
            
        finally:
            # 清理资源
            self.data_processor.close()

def main():
    """主函数"""
    print("=== Spark GLUE 文本分类训练 ===")
    print("结合Spark数据处理和PyTorch深度学习")
    print("=" * 50)
    
    # 检查环境
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
    
    # 运行SST-2任务
    print("\n开始SST-2情感分析任务...")
    pipeline = SparkGLUEPipeline("SST-2", "distilbert-base-uncased")
    results = pipeline.run_full_pipeline(num_samples=2000)
    
    # 显示结果
    print("\n" + "=" * 50)
    print("训练结果:")
    print(f"  任务: {results['task_name']}")
    print(f"  模型: {results['model_name']}")
    print(f"  准确率: {results['eval_results']['eval_accuracy']:.4f}")
    print(f"  F1分数: {results['eval_results']['eval_f1']:.4f}")
    print(f"  训练时间: {results['training_time']:.1f}秒")
    print(f"  训练样本: {results['data_info']['train_samples']}")
    print(f"  验证样本: {results['data_info']['dev_samples']}")
    print(f"  使用设备: {results['device']}")
    print(f"  Spark版本: {results['spark_version']}")

if __name__ == "__main__":
    main()
