#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Spark GLUE训练器 - 避开TensorFlow冲突
"""
import os
import json
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, when, lit, length, regexp_replace, concat
from pyspark.sql.types import StructType, StructField, StringType, IntegerType
import numpy as np
from sklearn.metrics import accuracy_score, f1_score
from sklearn.feature_extraction.text import TfidfVectorizer
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SparkDataProcessor:
    """Spark数据处理器"""
    
    def __init__(self):
        self.spark = self._create_spark_session()
        
    def _create_spark_session(self):
        """创建Spark会话"""
        logger.info("创建Spark会话...")
        
        spark = SparkSession.builder \
            .appName("GLUE_Simple_Training") \
            .master("local[*]") \
            .config("spark.driver.memory", "4g") \
            .config("spark.executor.memory", "2g") \
            .config("spark.sql.adaptive.enabled", "true") \
            .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
            .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
            .getOrCreate()
        
        spark.sparkContext.setLogLevel("WARN")
        logger.info(f"✅ Spark会话创建成功: {spark.version}")
        return spark
    
    def create_sst2_data(self, num_samples=1000):
        """创建SST-2情感分析数据"""
        logger.info(f"使用Spark创建SST-2数据集: {num_samples}样本")
        
        # 正面情感模板
        positive_templates = [
            "This movie is {} and {}!",
            "I {} this film, it's {}!",
            "{} performance and {} story!",
            "{} cinematography and {} acting!",
            "One of the {} movies I have ever seen!",
            "The {} plot and {} characters!",
            "{} direction and {} screenplay!",
            "Amazing {} and incredible {}!"
        ]
        
        # 负面情感模板
        negative_templates = [
            "This movie is {} and {}.",
            "I {} this film, it's {}.",
            "{} acting and {} storyline.",
            "{} direction and {} script.",
            "One of the {} films ever made.",
            "The {} plot and {} characters.",
            "{} cinematography and {} dialogue.",
            "Terrible {} and awful {}."
        ]
        
        positive_words = [
            "fantastic", "amazing", "excellent", "brilliant", "outstanding", 
            "wonderful", "incredible", "superb", "magnificent", "spectacular",
            "love", "adore", "enjoy", "appreciate", "best", "greatest", "perfect"
        ]
        
        negative_words = [
            "terrible", "awful", "horrible", "boring", "disappointing", 
            "bad", "poor", "worst", "hate", "dislike", "dreadful", "pathetic",
            "disgusting", "annoying", "frustrating", "unbearable", "painful"
        ]
        
        # 生成数据
        data = []
        for i in range(num_samples):
            if i % 2 == 0:
                # 正面情感
                template = positive_templates[i % len(positive_templates)]
                words = np.random.choice(positive_words, 2, replace=False)
                sentence = template.format(words[0], words[1])
                label = 1
            else:
                # 负面情感
                template = negative_templates[i % len(negative_templates)]
                words = np.random.choice(negative_words, 2, replace=False)
                sentence = template.format(words[0], words[1])
                label = 0
            
            data.append((sentence, label))
        
        # 创建Spark DataFrame
        schema = StructType([
            StructField("sentence", StringType(), True),
            StructField("label", IntegerType(), True)
        ])
        
        df = self.spark.createDataFrame(data, schema)
        logger.info(f"✅ SST-2数据创建完成: {df.count()}条记录")
        return df
    
    def create_rte_data(self, num_samples=800):
        """创建RTE文本蕴含数据"""
        logger.info(f"使用Spark创建RTE数据集: {num_samples}样本")
        
        # 蕴含关系对
        entailment_pairs = [
            ("The cat is sleeping on the sofa.", "There is a cat on the furniture.", 1),
            ("John went to the grocery store.", "John is shopping for food.", 1),
            ("She is reading a science book.", "She is studying science.", 1),
            ("The dog is barking loudly.", "There is a dog making noise.", 1),
            ("The restaurant serves Italian food.", "You can eat pasta there.", 1),
            ("She works as a teacher.", "She educates students.", 1),
            ("The car is moving fast.", "The vehicle is in motion.", 1),
            ("He is playing the piano.", "He is making music.", 1),
            ("The flowers are blooming.", "Plants are growing.", 1),
            ("Students are studying hard.", "People are learning.", 1)
        ]
        
        # 矛盾关系对
        contradiction_pairs = [
            ("The weather is sunny today.", "It's raining outside.", 0),
            ("The car is painted red.", "The car is blue in color.", 0),
            ("Students are taking an exam.", "Students are playing games.", 0),
            ("The movie starts at 8 PM.", "The movie ends at 8 PM.", 0),
            ("The book is very thick.", "The book is very thin.", 0),
            ("The room is completely dark.", "The room is brightly lit.", 0),
            ("He is very tall.", "He is very short.", 0),
            ("The water is hot.", "The water is cold.", 0),
            ("The door is open.", "The door is closed.", 0),
            ("She is happy.", "She is sad.", 0)
        ]
        
        all_pairs = entailment_pairs + contradiction_pairs
        
        # 生成数据
        data = []
        for i in range(num_samples):
            premise, hypothesis, label = all_pairs[i % len(all_pairs)]
            data.append((i, premise, hypothesis, label))
        
        schema = StructType([
            StructField("index", IntegerType(), True),
            StructField("sentence1", StringType(), True),
            StructField("sentence2", StringType(), True),
            StructField("label", IntegerType(), True)
        ])
        
        df = self.spark.createDataFrame(data, schema)
        logger.info(f"✅ RTE数据创建完成: {df.count()}条记录")
        return df
    
    def preprocess_data(self, df, task_name):
        """使用Spark预处理数据"""
        logger.info(f"使用Spark预处理{task_name}数据...")
        
        if task_name == "SST-2":
            # 单句情感分析
            processed_df = df.select(
                regexp_replace(col("sentence"), "[^a-zA-Z0-9\\s]", "").alias("text"),
                col("label").alias("labels")
            ).filter(length(col("text")) > 5)
            
        elif task_name == "RTE":
            # 文本蕴含，组合两个句子
            processed_df = df.select(
                col("index"),
                regexp_replace(
                    concat(col("sentence1"), lit(" [SEP] "), col("sentence2")),
                    "[^a-zA-Z0-9\\s\\[\\]]", ""
                ).alias("text"),
                col("label").alias("labels")
            ).filter(length(col("text")) > 10)
        
        # 添加文本长度统计
        processed_df = processed_df.withColumn("text_length", length(col("text")))
        
        # 显示统计信息
        total_count = processed_df.count()
        avg_length = processed_df.agg({"text_length": "avg"}).collect()[0][0]
        
        logger.info(f"✅ 预处理完成: {total_count}条记录，平均长度: {avg_length:.1f}")
        
        return processed_df
    
    def split_data(self, df, train_ratio=0.8):
        """使用Spark分割数据"""
        logger.info("使用Spark分割训练和验证数据...")
        
        train_df, dev_df = df.randomSplit([train_ratio, 1-train_ratio], seed=42)
        
        train_count = train_df.count()
        dev_count = dev_df.count()
        
        logger.info(f"✅ 数据分割完成: 训练集{train_count}条，验证集{dev_count}条")
        
        return train_df, dev_df
    
    def to_pandas(self, spark_df):
        """转换为Pandas DataFrame"""
        return spark_df.toPandas()
    
    def close(self):
        """关闭Spark会话"""
        if self.spark:
            self.spark.stop()
            logger.info("✅ Spark会话已关闭")

class SimpleTextClassifier(nn.Module):
    """简单的文本分类器"""
    
    def __init__(self, vocab_size=10000, embed_dim=128, hidden_dim=64, num_classes=2):
        super().__init__()
        self.embedding = nn.Embedding(vocab_size, embed_dim)
        self.lstm = nn.LSTM(embed_dim, hidden_dim, batch_first=True)
        self.fc = nn.Linear(hidden_dim, num_classes)
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        embedded = self.embedding(x)
        lstm_out, (hidden, _) = self.lstm(embedded)
        # 使用最后一个时间步的输出
        output = self.fc(self.dropout(hidden[-1]))
        return output

class SimpleGLUETrainer:
    """简化的GLUE训练器"""
    
    def __init__(self):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.vectorizer = TfidfVectorizer(max_features=10000, stop_words='english')
        
        logger.info(f"初始化训练器，使用设备: {self.device}")
    
    def prepare_data(self, train_df, dev_df):
        """准备训练数据"""
        logger.info("准备训练数据...")
        
        # 提取文本和标签
        train_texts = train_df['text'].tolist()
        train_labels = train_df['labels'].tolist()
        dev_texts = dev_df['text'].tolist()
        dev_labels = dev_df['labels'].tolist()
        
        # 使用TF-IDF向量化
        X_train = self.vectorizer.fit_transform(train_texts)
        X_dev = self.vectorizer.transform(dev_texts)
        
        # 转换为密集矩阵
        X_train = X_train.toarray()
        X_dev = X_dev.toarray()
        
        # 转换为PyTorch张量
        X_train = torch.FloatTensor(X_train).to(self.device)
        X_dev = torch.FloatTensor(X_dev).to(self.device)
        y_train = torch.LongTensor(train_labels).to(self.device)
        y_dev = torch.LongTensor(dev_labels).to(self.device)
        
        logger.info(f"✅ 数据准备完成: 训练{len(train_labels)}条，验证{len(dev_labels)}条")
        
        return X_train, y_train, X_dev, y_dev
    
    def train_model(self, X_train, y_train, X_dev, y_dev, epochs=5):
        """训练模型"""
        logger.info("开始训练模型...")
        
        # 创建模型
        input_dim = X_train.shape[1]
        model = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(64, 2)
        ).to(self.device)
        
        # 优化器和损失函数
        optimizer = optim.Adam(model.parameters(), lr=0.001)
        criterion = nn.CrossEntropyLoss()
        
        # 训练循环
        model.train()
        for epoch in range(epochs):
            optimizer.zero_grad()
            outputs = model(X_train)
            loss = criterion(outputs, y_train)
            loss.backward()
            optimizer.step()
            
            # 计算训练准确率
            with torch.no_grad():
                train_pred = torch.argmax(outputs, dim=1)
                train_acc = (train_pred == y_train).float().mean()
            
            logger.info(f"Epoch {epoch+1}/{epochs}, 损失: {loss.item():.4f}, 训练准确率: {train_acc:.4f}")
        
        # 评估
        model.eval()
        with torch.no_grad():
            dev_outputs = model(X_dev)
            dev_pred = torch.argmax(dev_outputs, dim=1)
            dev_acc = (dev_pred == y_dev).float().mean()
            dev_f1 = f1_score(y_dev.cpu().numpy(), dev_pred.cpu().numpy(), average='weighted')
        
        logger.info(f"✅ 训练完成! 验证准确率: {dev_acc:.4f}, F1分数: {dev_f1:.4f}")
        
        return {
            "model": model,
            "accuracy": dev_acc.item(),
            "f1": dev_f1,
            "final_loss": loss.item()
        }

def run_spark_glue_simple(task_name="SST-2", num_samples=1000):
    """运行简化版Spark GLUE训练"""
    print(f"=== Spark GLUE 简化版训练: {task_name} ===")
    print(f"样本数量: {num_samples}")
    print("=" * 50)
    
    # 检查环境
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
    
    processor = SparkDataProcessor()
    trainer = SimpleGLUETrainer()
    
    try:
        start_time = datetime.now()
        
        # 1. 使用Spark创建数据
        if task_name == "SST-2":
            df = processor.create_sst2_data(num_samples)
        elif task_name == "RTE":
            df = processor.create_rte_data(num_samples)
        else:
            raise ValueError(f"不支持的任务: {task_name}")
        
        # 2. 使用Spark预处理数据
        processed_df = processor.preprocess_data(df, task_name)
        
        # 3. 使用Spark分割数据
        train_spark_df, dev_spark_df = processor.split_data(processed_df)
        
        # 4. 转换为Pandas
        train_df = processor.to_pandas(train_spark_df)
        dev_df = processor.to_pandas(dev_spark_df)
        
        # 5. 准备训练数据
        X_train, y_train, X_dev, y_dev = trainer.prepare_data(train_df, dev_df)
        
        # 6. 训练模型
        results = trainer.train_model(X_train, y_train, X_dev, y_dev)
        
        end_time = datetime.now()
        training_time = (end_time - start_time).total_seconds()
        
        # 7. 保存结果
        final_results = {
            "task_name": task_name,
            "timestamp": datetime.now().isoformat(),
            "training_time": training_time,
            "accuracy": results["accuracy"],
            "f1": results["f1"],
            "final_loss": results["final_loss"],
            "num_samples": num_samples,
            "train_samples": len(train_df),
            "dev_samples": len(dev_df),
            "device": str(trainer.device),
            "spark_version": processor.spark.version
        }
        
        os.makedirs("results", exist_ok=True)
        with open(f"results/spark_simple_{task_name}_results.json", "w") as f:
            json.dump(final_results, f, indent=2)
        
        # 显示结果
        print("\n" + "=" * 50)
        print("🎉 训练完成!")
        print(f"任务: {task_name}")
        print(f"准确率: {results['accuracy']:.4f}")
        print(f"F1分数: {results['f1']:.4f}")
        print(f"训练时间: {training_time:.1f}秒")
        print(f"使用设备: {trainer.device}")
        print(f"Spark版本: {processor.spark.version}")
        print(f"结果保存: results/spark_simple_{task_name}_results.json")
        
        return final_results
        
    finally:
        processor.close()

if __name__ == "__main__":
    import sys
    
    task = sys.argv[1] if len(sys.argv) > 1 else "SST-2"
    samples = int(sys.argv[2]) if len(sys.argv) > 2 else 1000
    
    run_spark_glue_simple(task, samples)
